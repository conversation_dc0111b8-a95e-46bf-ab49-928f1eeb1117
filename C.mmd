---
title: Code Deep Research Assistant
---
flowchart TD
	__START__((start))
	__END__((stop))
	意图识别("意图识别")
	要素收集("要素收集")
	规划_TodoList("规划_TodoList")
	任务执行协调("任务执行协调")
subgraph 代码检索Agent
	___START__((start)):::___START__
	___END__((stop)):::___END__
	_Query改写扩写("Query改写扩写")
	_推理检索_ToolCall("推理检索_ToolCall")
	_检索结果评判_正确性_相关性_完整性("检索结果评判_正确性_相关性_完整性")
	_检索结果Report("检索结果Report")
	_condition1{"check state"}
	___START__:::___START__ --> _Query改写扩写:::_Query改写扩写
	_Query改写扩写:::_Query改写扩写 --> _推理检索_ToolCall:::_推理检索_ToolCall
	_推理检索_ToolCall:::_推理检索_ToolCall --> _检索结果评判_正确性_相关性_完整性:::_检索结果评判_正确性_相关性_完整性
	_检索结果评判_正确性_相关性_完整性:::_检索结果评判_正确性_相关性_完整性 -.-> _condition1:::_condition1
	_condition1:::_condition1 -.->|collect| _Query改写扩写:::_Query改写扩写
	%%	_检索结果评判_正确性_相关性_完整性:::_检索结果评判_正确性_相关性_完整性 -.->|collect| _Query改写扩写:::_Query改写扩写
	_condition1:::_condition1 -.->|end| _检索结果Report:::_检索结果Report
	%%	_检索结果评判_正确性_相关性_完整性:::_检索结果评判_正确性_相关性_完整性 -.->|end| _检索结果Report:::_检索结果Report
	_检索结果Report:::_检索结果Report --> ___END__:::___END__
end
	动态反思("动态反思")
	结束节点("结束节点")
	condition1{"check state"}
	condition2{"check state"}
	condition3{"check state"}
	__START__:::__START__ --> 意图识别:::意图识别
	要素收集:::要素收集 --> 规划_TodoList:::规划_TodoList
	规划_TodoList:::规划_TodoList --> 任务执行协调:::任务执行协调
	代码检索Agent:::代码检索Agent --> 动态反思:::动态反思
	意图识别:::意图识别 -.-> condition1:::condition1
	condition1:::condition1 -.->|collect| 要素收集:::要素收集
	%%	意图识别:::意图识别 -.->|collect| 要素收集:::要素收集
	condition1:::condition1 -.->|end| 结束节点:::结束节点
	%%	意图识别:::意图识别 -.->|end| 结束节点:::结束节点
	任务执行协调:::任务执行协调 -.-> condition2:::condition2
	condition2:::condition2 -.->|search| 代码检索Agent:::代码检索Agent
	%%	任务执行协调:::任务执行协调 -.->|search| 代码检索Agent:::代码检索Agent
	condition2:::condition2 -.->|end| 结束节点:::结束节点
	%%	任务执行协调:::任务执行协调 -.->|end| 结束节点:::结束节点
	动态反思:::动态反思 -.-> condition3:::condition3
	condition3:::condition3 -.->|Reflection| 规划_TodoList:::规划_TodoList
	%%	动态反思:::动态反思 -.->|Reflection| 规划_TodoList:::规划_TodoList
	condition3:::condition3 -.->|exec| 任务执行协调:::任务执行协调
	%%	动态反思:::动态反思 -.->|exec| 任务执行协调:::任务执行协调
	结束节点:::结束节点 --> __END__:::__END__

	classDef ___START__ fill:black,stroke-width:1px,font-size:xx-small;
	classDef ___END__ fill:black,stroke-width:1px,font-size:xx-small;
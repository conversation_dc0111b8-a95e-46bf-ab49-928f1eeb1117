# 货架流程分层

| 流程 | 功能 | 能力点 | 介绍 | 功能点举例 |
| --- | --- | --- | --- | --- |
| 实验流程 | ab实验 | ShelfDouHuFetcher | 斗槲实验接入，一般不会改代码，配置实验号即可 | |
| | | MatrixExperimentFetcher | Matrix实验接入，一般不会改代码，配置实验号即可 神会员一期接入，后续下掉 | |
| | | ArenaExperimentFetcher | Arena实验接入，一般不会改代码，配置实验号即可 后续斗槲会切到Arena | |
| 取数流程 | 上下文预处理 | PreHandlerContextAbility | 提供可选的前置预处理能力，包括取数，前置计算，拦截处理等 | 门店权益查询、门店一品多态查询、zdc标签查询、医美无忧品查询等 |
| | 导航召回 | FilterFirstFetcher | 导航查询取数，召回货架的筛选项，核心类为ShelfFilterFetcher，配置即可，涉及到交互协议调整需要修改 | |
| | 商品召回 | DealQueryFetcher | 商品查询取数，召回导航下挂商品列表，核心类为ShelfQueryFetcher，配置即可，涉及到交互协议调整需要修改 | |
| | 商品信息填充 | ProductPaddingFetcher | 根据召回的商品列表查询商品数据，填充商品展示所需要的信息，配置填充器和参数 | 团购主题填充：DealGroupThemePaddingHandler 商品主题填充： ProductThemePaddingHandler |
| | 异步上下文处理 | ContextHandlerAbility | 异步加载数据，与召回、填充并行 | 门店买贵必赔查询、门店销量查询、KTV预订规则查询等 |
| | 异步商品召回 | ProductParallelQueryFetcher | 异步商品列表查询取数，与导航召回并行 | |
| | 异步商品召回后置处理 | ProductParallelQueryPostHandler | 异步商品列表召回后置处理，主要为导航锚定，异常兜底等处理 | |
| | 异步商品信息填充 | ShopProductPaddingFetcher | 异步查询商品数据，预填充商品信息，与导航召回、商品召回并行 | |
| | 卡数据查询 | CardFetcher | 查询用户持卡数据，历史能力点，可迁移至异步上下文处理能力 | |
| | 商品活动列表数据查询 | ProductActivitiesFetcher | 查询门店活动信息，历史能力点，可迁移至异步上下文处理能力 | |
| 构造流程 | 内部模型组装 | ShelfMainDataAssembler | 取数后组装为内部模型数据，可进行商品分组处理 | 货架堆头分组处理 |
| | 筛选栏构造 | UnifiedShelfFilterBuilder | 对召回的导航进行展示信息构造 | |
| | 商品区构造 | UnifiedProductAreaBuilder | 对召回的商品进行展示信息构造 | |
| | 打点构造 | UnifiedShelfOceanBuilder | 构造打点信息 | |
| | 主标题构造 | UnifiedShelfMainTitleBuilder | 构造货架的主标题信息 | |
| 结果组装流程 | 货架生成 | UnifiedShelfResponseAssembler | 对构造的各个模块结果组装成最终响应结果 | |
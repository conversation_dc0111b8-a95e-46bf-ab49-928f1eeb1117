diff --git a/pom.xml b/pom.xml
index a55d46d..2cc6adb 100644
--- a/pom.xml
+++ b/pom.xml
@@ -6,7 +6,7 @@
     <parent>
         <groupId>com.meituan.mdp</groupId>
         <artifactId>mdp-basic-parent</artifactId>
-        <version>1.8.7.2</version>
+        <version>2.9.0.7</version>
         <relativePath/>
     </parent>
 
@@ -14,7 +14,7 @@
     <artifactId>professor</artifactId>
     <version>${revision}</version>
     <packaging>pom</packaging>
-    <name>demo</name>
+    <name>professor</name>
 
     <modules>
         <module>professor-api</module>
@@ -25,7 +25,7 @@
     </modules>
 
     <properties>
-        <revision>1.0.0-SNAPSHOT</revision>
+        <revision>1.0.1-SNAPSHOT</revision>
         <open-sdk.version>1.0.37-RELEASE</open-sdk.version>
         <shelf-operator-api.version>0.0.2</shelf-operator-api.version>
         <cpv.version>1.0.20</cpv.version>
@@ -151,6 +151,11 @@
                     </exclusion>
                 </exclusions>
             </dependency>
+            <dependency>
+                <groupId>com.meituan.nibscp.flow</groupId>
+                <artifactId>scp-general-enhance-api</artifactId>
+                <version>1.0.28</version>
+            </dependency>
             <dependency>
                 <groupId>com.meituan.nibscp</groupId>
                 <artifactId>unity-common-util</artifactId>
@@ -183,6 +188,13 @@
         </dependencies>
     </dependencyManagement>
 
+    <dependencies>
+        <dependency>
+            <groupId>org.projectlombok</groupId>
+            <artifactId>lombok</artifactId>
+        </dependency>
+    </dependencies>
+
     <build>
         <plugins>
             <plugin>
@@ -221,4 +233,5 @@
             </plugin>
         </plugins>
     </build>
-</project>
+
+</project>
\ No newline at end of file

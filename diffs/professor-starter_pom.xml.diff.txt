diff --git a/professor-starter/pom.xml b/professor-starter/pom.xml
index 0f2c6d1..b477d12 100644
--- a/professor-starter/pom.xml
+++ b/professor-starter/pom.xml
@@ -38,15 +38,14 @@
         <dependency>
             <groupId>com.meituan.mdp.boot</groupId>
             <artifactId>mdp-boot-starter</artifactId>
+            <exclusions>
+                <exclusion>
+                    <artifactId>mdp-boot-starter-bean-copy</artifactId>
+                    <groupId>com.meituan.mdp.boot</groupId>
+                </exclusion>
+            </exclusions>
         </dependency>
 
-        <!-- Mdp Boot Components -->
-        <dependency>
-            <groupId>com.meituan.mdp.component</groupId>
-            <artifactId>swagger-analysis-core</artifactId>
-            <scope>compile</scope>
-            <optional>true</optional>
-        </dependency>
         <dependency>
             <groupId>com.meituan.mdp.component</groupId>
             <artifactId>mdp-doc</artifactId>
@@ -126,17 +125,22 @@
             </plugin>
             <plugin>
                 <groupId>com.meituan.mdp.maven.plugins</groupId>
-                <artifactId>swagger-analysis-maven-plugin</artifactId>
-            </plugin>
-            <plugin>
-                <groupId>com.meituan.mdp.maven.plugins</groupId>
-                <artifactId>mdp-doc-maven-plugin</artifactId>
+                <artifactId>mdp-graphql-maven-plugin</artifactId>
             </plugin>
             <plugin>
                 <groupId>com.meituan.mdp.maven.plugins</groupId>
-                <artifactId>mdp-graphql-maven-plugin</artifactId>
+                <artifactId>mdp-statistics-maven-plugin</artifactId>
+                <executions>
+                    <execution>
+                        <id>register</id>
+                        <phase>compile</phase>
+                        <goals>
+                            <goal>make</goal>
+                        </goals>
+                    </execution>
+                </executions>
             </plugin>
         </plugins>
     </build>
 
-</project>
+</project>
\ No newline at end of file

<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.meituan.mdp</groupId>
        <artifactId>mdp-parent</artifactId>
        <version>2.9.0.12</version>
        <relativePath/>
    </parent>

    <groupId>com.sankuai.qpro.ai</groupId>
    <artifactId>professor</artifactId>
    <version>${revision}</version>
    <packaging>pom</packaging>
    <name>professor</name>

    <modules>
        <module>professor-api</module>
        <module>professor-starter</module>
        <module>professor-application</module>
        <module>professor-domain</module>
        <module>professor-infrastructure</module>
    </modules>

    <properties>
        <revision>1.0.1-SNAPSHOT</revision>
        <open-sdk.version>1.0.47-RELEASE</open-sdk.version>
        <shelf-operator-api.version>0.0.2</shelf-operator-api.version>
        <cpv.version>1.0.20</cpv.version>
        <spt-ark-api.version>0.0.66</spt-ark-api.version>
        <spt-ark-common.version>1.0.21</spt-ark-common.version>
        <spt-gray-api.version>0.0.7</spt-gray-api.version>
        <spoon.version>11.2.1</spoon.version>
        <mcode-analyze-api.version>1.0.20</mcode-analyze-api.version>
        <langgraph4j.version>1.6.0-rc4</langgraph4j.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.sankuai.qpro.ai</groupId>
                <artifactId>professor-api</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.sankuai.qpro.ai</groupId>
                <artifactId>professor-starter</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.sankuai.qpro.ai</groupId>
                <artifactId>professor-domain</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.sankuai.qpro.ai</groupId>
                <artifactId>professor-application</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.sankuai.qpro.ai</groupId>
                <artifactId>professor-infrastructure</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.meituan.nibscp.framework</groupId>
                <artifactId>scp-cpv-api</artifactId>
                <version>${cpv.version}</version>
            </dependency>
            <dependency>
                <groupId>com.sankuai.dxenterprise.open.gateway</groupId>
                <artifactId>open-sdk</artifactId>
                <version>${open-sdk.version}</version>
            </dependency>
            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>1.18.36</version>
            </dependency>
            <dependency>
                <groupId>com.sankuai.spt</groupId>
                <artifactId>spt-ark-api</artifactId>
                <version>${spt-ark-api.version}</version>
            </dependency>
            <dependency>
                <groupId>com.sankuai.spt</groupId>
                <artifactId>spt-ark-common</artifactId>
                <version>${spt-ark-common.version}</version>
            </dependency>
            <dependency>
                <groupId>com.sankuai.spt</groupId>
                <artifactId>spt-gray-api</artifactId>
                <version>${spt-gray-api.version}</version>
            </dependency>
            <!-- mockito-core -->
            <dependency>
                <groupId>com.meituan.mdp</groupId>
                <artifactId>langmodel-api</artifactId>
                <version>1.1.10.1</version>
            </dependency>
            <dependency>
                <groupId>com.meituan.mdp</groupId>
                <artifactId>langmodel-component</artifactId>
                <version>1.1.10.1</version>
            </dependency>
            <dependency>
                <groupId>com.meituan.mdp</groupId>
                <artifactId>langmodel-starter</artifactId>
                <version>1.1.10.1</version>
                <exclusions>
                    <exclusion>
                        <groupId>javax.validation</groupId>
                        <artifactId>validation-api</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.dianping</groupId>
                <artifactId>tuangou-category-api</artifactId>
                <version>1.0.7</version>
            </dependency>
            <dependency>
                <groupId>com.dianping.poi</groupId>
                <artifactId>poi-cateproperty-api</artifactId>
                <version>0.2.10</version>
            </dependency>
            <dependency>
                <groupId>com.sankuai.dzviewscene</groupId>
                <artifactId>dzviewscene-unified-shelf-operator-api</artifactId>
                <version>${shelf-operator-api.version}</version>
            </dependency>
            <dependency>
                <groupId>com.meituan.nibscp.domain</groupId>
                <artifactId>scp-accessory-utils</artifactId>
                <version>1.0.2</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.meituan.nibscp</groupId>
                        <artifactId>unity-common-util</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>ma.glasnost.orika</groupId>
                        <artifactId>orika-core</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.apache.logging.log4j</groupId>
                        <artifactId>log4j-slf4j-impl</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.meituan.nibscp.flow</groupId>
                <artifactId>scp-general-enhance-api</artifactId>
                <version>1.0.28</version>
            </dependency>
            <dependency>
                <groupId>com.meituan.nibscp</groupId>
                <artifactId>unity-common-util</artifactId>
                <version>0.3.6.21</version>
            </dependency>
            <dependency>
                <groupId>ma.glasnost.orika</groupId>
                <artifactId>orika-core</artifactId>
                <version>1.5.4</version>
            </dependency>
            <dependency>
                <groupId>edu.stanford.nlp</groupId>
                <artifactId>stanford-corenlp</artifactId>
                <version>3.9.2</version>
                <exclusions>
                    <exclusion>
                        <artifactId>javax.servlet-api</artifactId>
                        <groupId>javax.servlet</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>jaxb-api</artifactId>
                        <groupId>javax.xml.bind</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>javax.activation-api</artifactId>
                        <groupId>javax.activation</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>fr.inria.gforge.spoon</groupId>
                <artifactId>spoon-core</artifactId>
                <version>${spoon.version}</version>
            </dependency>
            <dependency>
                <groupId>com.github.javaparser</groupId>
                <artifactId>javaparser-core</artifactId>
                <version>${javaparser.version}</version>
            </dependency>
            <dependency>
                <groupId>com.sankuai.ee.mcode</groupId>
                <artifactId>mcode-analyze-api</artifactId>
                <version>${mcode-analyze-api.version}</version>
            </dependency>
            <dependency>
                <groupId>org.seleniumhq.selenium</groupId>
                <artifactId>selenium-java</artifactId>
                <version>4.30.0</version>
            </dependency>
            <dependency>
                <groupId>org.apache.httpcomponents.client5</groupId>
                <artifactId>httpclient5</artifactId>
                <version>5.4.1</version>
            </dependency>
            <!-- 如果要保留4.x，只能指定它的版本，不能替换为5 -->
            <dependency>
                <groupId>org.apache.httpcomponents</groupId>
                <artifactId>httpclient</artifactId>
                <version>4.5.14</version>
            </dependency>
            <dependency>
                <groupId>com.google.guava</groupId>
                <artifactId>guava</artifactId>
                <version>31.1-jre</version>
            </dependency>
            <!-- mdp-ai -->

            <!-- Mdp Boot Components -->
            <dependency>
                <groupId>com.meituan.mdp.component</groupId>
                <artifactId>swagger-analysis-core</artifactId>
                <version>2.4.0</version>
            </dependency>
            <!-- sso -->
            <dependency>
                <groupId>com.sankuai.it.sso</groupId>
                <artifactId>sso-java-sdk</artifactId>
                <version>2.6.1</version>
            </dependency>
            <!-- 图片生成依赖 -->
            <dependency>
                <groupId>net.coobird</groupId>
                <artifactId>thumbnailator</artifactId>
                <version>0.4.20</version>
            </dependency>
            <dependency>
                <groupId>org.bsc.langgraph4j</groupId>
                <artifactId>langgraph4j-bom</artifactId>
                <version>${langgraph4j.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>org.bsc.langgraph4j</groupId>
                <artifactId>langgraph4j-core</artifactId>
                <version>1.6.0-rc4</version>
            </dependency>
            <dependency>
                <groupId>org.bsc.langgraph4j</groupId>
                <artifactId>langgraph4j-spring-ai</artifactId>
                <version>1.6.0-rc4</version>
            </dependency>
            <dependency>
                <groupId>org.bsc.langgraph4j</groupId>
                <artifactId>langgraph4j-springai-agentexecutor</artifactId>
                <version>1.6.0-rc4</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <dependencies>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>flatten-maven-plugin</artifactId>
                <executions>
                    <execution>
                        <id>flatten</id>
                        <phase>process-resources</phase>
                        <goals>
                            <goal>flatten</goal>
                        </goals>
                        <configuration>
                            <flattenMode>defaults</flattenMode>
                            <pomElements>
                                <parent>expand</parent>
                                <profiles>expand</profiles>
                                <dependencies>keep</dependencies>
                                <build>keep</build>
                            </pomElements>
                            <updatePomFile>true</updatePomFile>
                        </configuration>
                    </execution>
                    <execution>
                        <id>flatten-clean</id>
                        <phase>clean</phase>
                        <goals>
                            <goal>clean</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
            </plugin>
            <!-- 单测启动必要配置 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <configuration>
                    <argLine>-ea --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.lang.reflect=ALL-UNNAMED --add-opens=java.base/java.nio=ALL-UNNAMED --add-opens=java.base/java.text=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.util.concurrent=ALL-UNNAMED --add-exports=java.base/sun.reflect.generics.reflectiveObjects=ALL-UNNAMED --add-exports=java.base/sun.net.util=ALL-UNNAMED</argLine>
                </configuration>
            </plugin>
        </plugins>
    </build>

</project>
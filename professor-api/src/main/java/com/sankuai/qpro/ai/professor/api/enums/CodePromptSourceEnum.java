package com.sankuai.qpro.ai.professor.api.enums;

import com.google.common.collect.Maps;
import org.apache.commons.lang.StringUtils;

import java.util.Map;

/**
 * @author: wu<PERSON><PERSON><PERSON>
 * @create: 2025-06-03
 * @description:
 */
public enum CodePromptSourceEnum {
    WEB(1, "web", "运营侧"),
    PLUGIN(2, "plugin", "插件");

    CodePromptSourceEnum(int type, String tag, String desc) {
        this.type = type;
        this.tag = tag;
        this.desc = desc;
    }

    public int getType() {
        return type;
    }

    public String getTag() {
        return tag;
    }

    private static final Map<String, CodePromptSourceEnum> TAG_MAP = Maps.newHashMap();
    private static final Map<Integer, CodePromptSourceEnum> TYPE_MAP = Maps.newHashMap();

    static {
        for (CodePromptSourceEnum sourceEnum : CodePromptSourceEnum.values()) {
            TAG_MAP.put(sourceEnum.getTag(), sourceEnum);
            TYPE_MAP.put(sourceEnum.getType(), sourceEnum);
        }
    }

    public static CodePromptSourceEnum getByTag(String tag) {
        if (StringUtils.isBlank(tag)) {
            return null;
        }
        return TAG_MAP.get(tag);
    }

    public static CodePromptSourceEnum getByType(Integer type) {
        if (type == null) {
            return null;
        }
        return TYPE_MAP.get(type);
    }

    private int type;
    private String tag;
    private String desc;
}

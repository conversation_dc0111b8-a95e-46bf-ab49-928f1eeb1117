package com.sankuai.qpro.ai.professor.api.enums;

import com.google.common.collect.Maps;

import java.util.Map;
import java.util.Objects;

/**
 * @author: wu<PERSON><PERSON><PERSON>
 * @create: 2025-06-03
 * @description:
 */
public enum CodePromptStageEnum {
    EXTRACT_REQUIREMENT(1, "需求提取", "需求提取"),
    TRACE_RAG(2, "链路RAG", "链路RAG"),
    GRAPH_RAG(3, "图RAG", "图RAG");

    private static final Map<Integer, CodePromptStageEnum> ORDER_MAP = Maps.newHashMap();

    static {
        for (CodePromptStageEnum stageEnum : CodePromptStageEnum.values()) {
            ORDER_MAP.put(stageEnum.getOrder(), stageEnum);
        }
    }

    CodePromptStageEnum(int order, String tag, String desc) {
        this.order = order;
        this.tag = tag;
        this.desc = desc;
    }

    public int getOrder() {
        return order;
    }

    public String getTag() {
        return tag;
    }

    public static CodePromptStageEnum getByOrder(Integer order) {
        if (Objects.isNull(order)) {
            return null;
        }
        return ORDER_MAP.get(order);
    }

    private int order;
    private String tag;
    private String desc;
}

package com.sankuai.qpro.ai.professor.api.enums;

/**
 * <AUTHOR>
 * @date 2024/12/30
 */
public enum ConfigUnitEnum {

    SHELF_SUBTITLE("shelf_subtitle", "货架副标题"),
    TITLE_PREFIX("title_prefix", "标题前缀");

    private final String code;
    private final String description;

    ConfigUnitEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }
}

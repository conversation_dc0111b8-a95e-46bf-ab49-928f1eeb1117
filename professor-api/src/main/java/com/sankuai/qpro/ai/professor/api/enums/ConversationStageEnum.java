package com.sankuai.qpro.ai.professor.api.enums;

/**
 * <AUTHOR>
 * @date 2025/1/19
 */
public enum ConversationStageEnum {
    START(0, "开始"),
    CATEGORY_SELECTED(10, "选完分类"),
    EXP_ENTERED(20, "实验号已输入"),
    EXP_CHECKED_FAILED(21, "实验号校验失败"),
    EXP_CHECKED_SUCCEED(22, "实验号校验通过"),
    CONF_ENTERED(30, "配置已输入"),
    STRATEGY_GENERATED(40, "策略小结已生成"),
    CODE_GENERATED(41, "代码已生成"),
    END(99, "结束");

    private final int code;
    private final String description;

    ConversationStageEnum(int code, String description) {
        this.code = code;
        this.description = description;
    }

    public int getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }
}

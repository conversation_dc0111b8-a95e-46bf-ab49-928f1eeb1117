package com.sankuai.qpro.ai.professor.api.enums;

/**
 * @author: w<PERSON><PERSON><PERSON><PERSON>
 * @create: 2025-01-10
 * @description:
 */
public enum ExpPlatformEnum {
    DP(0,  "点评"),
    MT(1,  "美团");

    ExpPlatformEnum(int type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    private int type;
    private String desc;

    public int getType() {
        return type;
    }

    public String getDesc() {
        return desc;
    }
}

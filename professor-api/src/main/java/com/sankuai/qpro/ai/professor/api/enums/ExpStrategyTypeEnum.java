package com.sankuai.qpro.ai.professor.api.enums;

/**
 * @author: w<PERSON><PERSON><PERSON><PERSON>
 * @create: 2024-12-16
 * @description: 实验策略类型
 */
public enum ExpStrategyTypeEnum {
    EXP(1, "实验组"),
    CONTROL(2, "对照组"),
    WHITE_CONTROL(3, "空白对照组");

    ExpStrategyTypeEnum(int type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    private int type;
    private String desc;

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}

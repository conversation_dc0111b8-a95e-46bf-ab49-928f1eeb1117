package com.sankuai.qpro.ai.professor.api.enums;

/**
 * @author: w<PERSON><PERSON><PERSON><PERSON>
 * @create: 2024-12-10
 * @description:
 */
public enum ResponseCodeEnum {
    OK(200, "操作成功"),

    INVALID_REQUEST(400, "参数错误"),

    UNAUTHORIZED(401, "未授权"),

    SERVER_ERROR(500, "服务器错误");

    private int code;
    private String name;

    ResponseCodeEnum(int code, String name) {
        this.code = code;
        this.name = name;
    }

    public int getCode() {
        return code;
    }

    public String getName() {
        return name;
    }
}

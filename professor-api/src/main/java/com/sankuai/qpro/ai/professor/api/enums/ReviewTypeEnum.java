package com.sankuai.qpro.ai.professor.api.enums;

/**
 * @author: w<PERSON><PERSON><PERSON><PERSON>
 * @create: 2025-01-09
 * @description:
 */
public enum ReviewTypeEnum {

    NON_REVIEW(0, "未评论"),
    POSITIVE_REVIEW(1, "赞"),
    NEGATIVE_REVIEW(2, "踩");

    ReviewTypeEnum(int type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public int getType() {
        return type;
    }

    public String getDesc() {
        return desc;
    }

    private int type;
    private String desc;
}

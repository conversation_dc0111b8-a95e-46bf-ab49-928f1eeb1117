package com.sankuai.qpro.ai.professor.api.enums;

/**
 * @author: w<PERSON><PERSON><PERSON><PERSON>
 * @create: 2024-12-10
 * @description:
 */
public enum RoleTypeEnum {
    SYSTEM(1, "系统"),
    USER(2, "用户"),
    AI(3, "AI");

    RoleTypeEnum(int type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    private int type;
    private String desc;

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}

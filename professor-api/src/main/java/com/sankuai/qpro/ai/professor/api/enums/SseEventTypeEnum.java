package com.sankuai.qpro.ai.professor.api.enums;

/**
 * @author: w<PERSON><PERSON><PERSON><PERSON>
 * @create: 2024-12-12
 * @description:
 */
public enum SseEventTypeEnum {
    START("start"),
    MESSAGE("message"),
    STRATEGY("strategy"),
    END("end"),
    ERROR("error");

    SseEventTypeEnum(String eventType) {
        this.eventType = eventType;
    }

    private String eventType;

    public String getEventType() {
        return eventType;
    }

    public void setEventType(String eventType) {
        this.eventType = eventType;
    }
}

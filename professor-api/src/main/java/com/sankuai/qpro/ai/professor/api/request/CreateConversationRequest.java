package com.sankuai.qpro.ai.professor.api.request;

import lombok.Data;

import java.io.Serializable;

/**
 * @author: wu<PERSON><PERSON><PERSON>
 * @create: 2024-12-10
 * @description:
 */
@Data
public class CreateConversationRequest implements Serializable {
    // 商户后台类目
    @Deprecated
    private String poiBackCate;
    // 商品类目
    @Deprecated
    private String productCate;
    // 配置单元ID
    private Long configUnitId;
    // 用户mis
    private String userMis;
}

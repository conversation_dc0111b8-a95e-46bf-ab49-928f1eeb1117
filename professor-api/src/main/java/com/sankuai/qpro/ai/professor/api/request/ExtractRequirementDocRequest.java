package com.sankuai.qpro.ai.professor.api.request;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.sankuai.qpro.ai.professor.api.constants.PromptTaskAttrConstant;
import lombok.Data;

import java.io.Serializable;

/**
 * @author: wu<PERSON><PERSON><PERSON>
 * @create: 2025-05-29
 * @description:
 */
@Data
public class ExtractRequirementDocRequest implements Serializable {
    // 需求任务ID
    private Long taskId;
    // PRD链接
    @JsonProperty(PromptTaskAttrConstant.PRD_URL)
    private String prdUrl;
    // 需求评审文档链接
    @JsonProperty(PromptTaskAttrConstant.REVIEW_DOC_URL)
    private String reviewDocUrl;
    // 学城cookieValue
    @JsonProperty(PromptTaskAttrConstant.KM_COOKIE_VALUE)
    private String kmCookieValue;
    // 需要提取的PRD范围
    @JsonProperty(PromptTaskAttrConstant.SCENE)
    private String scene;
    // 用户补充说明
    @JsonProperty(PromptTaskAttrConstant.USER_INPUT)
    private String userInput;
}

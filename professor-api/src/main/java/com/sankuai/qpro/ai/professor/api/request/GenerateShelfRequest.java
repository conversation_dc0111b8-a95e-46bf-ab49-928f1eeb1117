package com.sankuai.qpro.ai.professor.api.request;

import lombok.Data;

import java.util.List;

/**
 * @author: wu<PERSON><PERSON><PERSON>
 * @create: 2024-12-12
 * @description:
 */
@Data
public class GenerateShelfRequest {
    // 会话ID
    private Long conversationId;
    // 实验结果
    private List<String> expResults;
    // 商户ID
    private Long shopId;
    // 平台
    private Integer platform;
    // 筛选按钮ID
    private Long filterBtnId;
}

package com.sankuai.qpro.ai.professor.api.response;

import lombok.Data;

/**
 * @author: wu<PERSON><PERSON><PERSON>
 * @create: 2025-06-03
 * @description:
 */
@Data
public class CodePromptTaskModel {
    // 需求任务ID
    private Long taskId;
    // 标题
    private String title;
    // 任务来源
    private String source;
    // 任务阶段
    private String stage;
    // PRD链接
    private String prdUrl;
    // 评审记录链接
    private String reviewDocUrl;
    // 学城cookie
    private String kmCookieValue;
    private String scene;
    private String userInput;
    // 需求总结
    private String requirementSummary;
    // 评审提取数据
    private RequirementReviewInfoModel requirementReviewInfo;
    // 创建时间
    private String createTime;
}

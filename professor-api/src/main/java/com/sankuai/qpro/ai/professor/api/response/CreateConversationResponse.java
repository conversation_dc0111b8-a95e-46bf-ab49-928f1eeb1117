package com.sankuai.qpro.ai.professor.api.response;

import lombok.Builder;
import lombok.Data;

import java.io.Serializable;

/**
 * @author: wu<PERSON><PERSON><PERSON>
 * @create: 2024-12-10
 * @description:
 */
@Data
@Builder
public class CreateConversationResponse implements Serializable {
    // 会话ID
    private Long id;
    // 会话创建后msg
    private String msg;
    // 货架/团详预览单元标识
    private String previewUnitName;
}

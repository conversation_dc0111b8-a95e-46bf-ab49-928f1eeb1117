package com.sankuai.qpro.ai.professor.api.response;

import com.sankuai.qpro.ai.professor.api.enums.ResponseCodeEnum;
import lombok.Data;

import java.io.Serializable;

/**
 * @author: w<PERSON><PERSON><PERSON><PERSON>
 * @create: 2024-12-10
 * @description:
 */
@Data
public class RemoteResponse<T> implements Serializable {
    // 状态码
    private int code;

    // 状态描述
    private String msg;

    // 结果
    private T data;

    public RemoteResponse()
    {
        //do nothing
    }

    public RemoteResponse(int code, String msg, T data)
    {
        this.code = code;
        this.msg = msg;
        this.data = data;
    }

    public int getCode()
    {
        return code;
    }

    public void setCode(int code)
    {
        this.code = code;
    }

    public String getMsg()
    {
        return msg;
    }

    public void setMsg(String msg)
    {
        this.msg = msg;
    }

    public T getData()
    {
        return data;
    }

    public void setData(T data)
    {
        this.data = data;
    }

    /**
     * 保存成功：success
     * @return boolean
     */
    public boolean isSuccess()
    {
        return ResponseCodeEnum.OK.getCode() == this.code ;
    }

    public static <T> RemoteResponse<T> invalidParam(String msg)
    {
        RemoteResponse<T> remoteResponse = new RemoteResponse<>();
        remoteResponse.code = ResponseCodeEnum.INVALID_REQUEST.getCode();
        remoteResponse.msg = msg;
        return remoteResponse;
    }

    public static <T> RemoteResponse<T> success(T data) {
        RemoteResponse<T> remoteResponse = new RemoteResponse<>();
        remoteResponse.code = ResponseCodeEnum.OK.getCode();
        remoteResponse.msg = "success";
        remoteResponse.data = data;
        return remoteResponse;
    }

    public static <T> RemoteResponse<T> success(T data, String msg) {
        RemoteResponse<T> remoteResponse = new RemoteResponse<>();
        remoteResponse.code = ResponseCodeEnum.OK.getCode();
        remoteResponse.msg = msg;
        remoteResponse.data = data;
        return remoteResponse;
    }

    public static <T> RemoteResponse<T> fail(ResponseCodeEnum status, String msg) {
        RemoteResponse<T> remoteResponse = new RemoteResponse<>();
        remoteResponse.code = status.getCode();
        remoteResponse.msg = msg;
        return remoteResponse;
    }
}

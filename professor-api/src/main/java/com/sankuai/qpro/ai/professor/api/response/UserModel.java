package com.sankuai.qpro.ai.professor.api.response;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @author: wu<PERSON><PERSON><PERSON>
 * @create: 2025-06-05
 * @description:
 */
@Data
public class UserModel implements Serializable {
    private int id;
    private String login;
    private String name;
    private String code;
    private String email;
    private String tenantId;
    private List<String> roles;
    public Boolean isVerified;
    public String verifyType;
    public Long verifyExpireTime;
    private String passport;
    private String avatarUrl;
}

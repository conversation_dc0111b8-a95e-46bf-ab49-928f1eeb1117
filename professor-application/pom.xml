<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.sankuai.qpro.ai</groupId>
        <artifactId>professor</artifactId>
        <version>${revision}</version>
    </parent>

    <artifactId>professor-application</artifactId>
    <version>${revision}</version>
    <packaging>jar</packaging>
    <name>professor-application</name>

    <dependencies>
        <!-- Project module -->
        <dependency>
            <groupId>com.sankuai.qpro.ai</groupId>
            <artifactId>professor-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sankuai.qpro.ai</groupId>
            <artifactId>professor-domain</artifactId>
        </dependency>
        <dependency>
            <groupId>com.meituan.nibscp.framework</groupId>
            <artifactId>scp-cpv-api</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>unity-distributed-proxy</artifactId>
                    <groupId>com.meituan.nibscp</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>validation-api</artifactId>
                    <groupId>javax.validation</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>mdp-boot-starter-bean-copy</artifactId>
                    <groupId>com.meituan.mdp.boot</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.sankuai.dxenterprise.open.gateway</groupId>
            <artifactId>open-sdk</artifactId>
        </dependency>
        <dependency>
            <groupId>com.meituan.mdp.boot</groupId>
            <artifactId>mdp-boot-starter-thrift</artifactId>
        </dependency>
        <dependency>
            <groupId>com.meituan.mdp.boot</groupId>
            <artifactId>mdp-boot-starter-pigeon</artifactId>
        </dependency>
        <dependency>
            <groupId>com.meituan.mdp.boot</groupId>
            <artifactId>mdp-boot-starter-langmodel</artifactId>
        </dependency>
        <dependency>
            <groupId>com.meituan.mdp</groupId>
            <artifactId>langmodel-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.meituan.mdp</groupId>
            <artifactId>langmodel-component</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>validation-api</artifactId>
                    <groupId>javax.validation</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.meituan.mdp</groupId>
            <artifactId>langmodel-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.meituan.mdp.boot</groupId>
            <artifactId>mdp-boot-starter-log</artifactId>
        </dependency>
        <dependency>
            <groupId>com.meituan.mdp.boot</groupId>
            <artifactId>mdp-boot-starter-config</artifactId>
        </dependency>
        <dependency>
            <groupId>com.google.code.gson</groupId>
            <artifactId>gson</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-tx</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-webflux</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sankuai.spt</groupId>
            <artifactId>spt-ark-api</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>*</groupId>
                    <artifactId>*</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.sankuai.spt</groupId>
            <artifactId>spt-ark-common</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>*</groupId>
                    <artifactId>*</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.sankuai.spt</groupId>
            <artifactId>spt-gray-api</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>*</groupId>
                    <artifactId>*</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <!-- nibscp enhance api -->
        <dependency>
            <groupId>com.meituan.nibscp.flow</groupId>
            <artifactId>scp-general-enhance-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.meituan.mdp.boot</groupId>
            <artifactId>mdp-boot-starter-squirrel</artifactId>
        </dependency>
        <dependency>
            <groupId>com.squareup.okhttp3</groupId>
            <artifactId>okhttp</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>tuangou-category-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dianping.poi</groupId>
            <artifactId>poi-cateproperty-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.meituan.nibscp.domain</groupId>
            <artifactId>scp-accessory-utils</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>log4j-slf4j-impl</artifactId>
                    <groupId>org.apache.logging.log4j</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.meituan.nibscp</groupId>
            <artifactId>unity-common-util</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sankuai.dzviewscene</groupId>
            <artifactId>dzviewscene-unified-shelf-operator-api</artifactId>
        </dependency>
        <dependency>
            <groupId>edu.stanford.nlp</groupId>
            <artifactId>stanford-corenlp</artifactId>
        </dependency>
        <dependency>
            <groupId>fr.inria.gforge.spoon</groupId>
            <artifactId>spoon-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.github.javaparser</groupId>
            <artifactId>javaparser-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sankuai.ee.mcode</groupId>
            <artifactId>mcode-analyze-api</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>*</groupId>
                    <artifactId>*</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.seleniumhq.selenium</groupId>
            <artifactId>selenium-java</artifactId>
        </dependency>
        <!-- mdp-ai -->
        <dependency>
            <groupId>com.meituan.mdp.ai</groupId>
            <artifactId>mdp-ai-starter-openai</artifactId>
        </dependency>
        <dependency>
            <groupId>com.meituan.mdp.ai</groupId>
            <artifactId>mdp-ai-starter-mcp-client-webflux</artifactId>
        </dependency>
        <dependency>
            <groupId>com.meituan.mdp.ai</groupId>
            <artifactId>mdp-ai-starter-mcp-server</artifactId>
        </dependency>
        <!-- langgraph4j -->
        <dependency>
            <groupId>org.bsc.langgraph4j</groupId>
            <artifactId>langgraph4j-core</artifactId>
        </dependency>
        <dependency>
            <groupId>org.bsc.langgraph4j</groupId>
            <artifactId>langgraph4j-spring-ai</artifactId>
        </dependency>
        <dependency>
            <groupId>org.bsc.langgraph4j</groupId>
            <artifactId>langgraph4j-springai-agentexecutor</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sankuai.it.sso</groupId>
            <artifactId>sso-java-sdk</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>commons-logging</artifactId>
                    <groupId>commons-logging</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- 图片处理依赖 -->
        <dependency>
            <groupId>net.coobird</groupId>
            <artifactId>thumbnailator</artifactId>
        </dependency>

        <!-- 单元测试 -->
        <!-- mockito-core -->
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-core</artifactId>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>org.eclipse.jgit</groupId>
            <artifactId>org.eclipse.jgit.ssh.jsch</artifactId>
        </dependency>
    </dependencies>

</project>

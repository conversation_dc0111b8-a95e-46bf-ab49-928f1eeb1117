package com.sankuai.qpro.ai.professor.application.code;

import com.dianping.cat.Cat;
import com.dianping.lion.client.Lion;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.meituan.mdp.boot.starter.MdpContextUtils;
import com.sankuai.ee.mcode.analyze.api.thrift.dto.CallGraphMethodDefDTO;
import com.sankuai.ee.mcode.analyze.api.thrift.dto.MethodDefWithoutInvocationsDTO;
import com.sankuai.ee.mcode.analyze.api.thrift.exception.ThriftApiException;
import com.sankuai.ee.mcode.analyze.api.thrift.response.CreateJavaProjectResponse;
import com.sankuai.ee.mcode.analyze.api.thrift.response.ProjectCallGraphByRootMethodResponse;
import com.sankuai.ee.mcode.analyze.api.thrift.service.AnalysisProjectThriftService;
import com.sankuai.qpro.ai.professor.application.code.pojo.CallTopology;
import com.sankuai.qpro.ai.professor.application.lion.P2PLion;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.poi.ss.formula.functions.T;
import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.web.reactive.function.client.WebClient;

import java.time.Duration;
import java.util.*;
import java.util.regex.Pattern;

/**
 * TopologyService
 *
 * <AUTHOR>
 * @since 2025/5/29
 */
@Repository
@Slf4j
public class TopologyService {

    @Autowired
    private AnalysisProjectThriftService analysisProjectThriftService;

    @Autowired
    private P2PLion p2pLion;

    private final WebClient webClient = WebClient.builder()
            .codecs(configurer -> configurer.defaultCodecs().maxInMemorySize(20 * 1024 * 1024))
            .build();

    private final ObjectMapper objectMapper = new ObjectMapper();
    // 匹配getter方法的正则表达式
    private static final Pattern GETTER_PATTERN = Pattern.compile(".*\\.get[A-Z][a-zA-Z0-9_]*\\s*\\([^)]*\\)\\s*$");

    // 匹配setter方法的正则表达式
    private static final Pattern SETTER_PATTERN = Pattern.compile(".*\\.set[A-Z][a-zA-Z0-9_]*\\s*\\([^)]*\\)\\s*$");

    // 匹配is方法的正则表达式
    private static final Pattern IS_PATTERN = Pattern.compile(".*\\.is[A-Z][a-zA-Z0-9_]*\\s*\\([^)]*\\)\\s*$");

    // 匹配init方法的正则表达式
    private static final Pattern INIT_PATTERN = Pattern.compile(".*<init>\\([^)]*\\)");

    // 匹配无参方法的正则表达式
    private static final Pattern NOPARAM_PATTERN = Pattern.compile(".*\\.[a-zA-Z_][a-zA-Z0-9_]*\\(\\)$");

    private static final String BASEURL = "http://10.98.14.201:8080/ai/probe/chat/api/code";

    public boolean createAnalysisTask(String repo) {
        try {
            CreateJavaProjectResponse res = analysisProjectThriftService.createJavaProject(repo);
            if (CollectionUtils.isNotEmpty(res.getAppKeys())) {
                res.getAppKeys().forEach(appkey -> {
                    Cat.logEvent("createAnalysisTask", appkey);
                });
            }
            return true;
        } catch (ThriftApiException | TException e) {
            log.error("createJavaProject error repo={}, ", repo, e);
        }
        return false;
    }

    public CallTopology queryCallTopology(String repo, String methodSignature) {
        try {
            ProjectCallGraphByRootMethodResponse res = analysisProjectThriftService.getJavaProjectCallGraphByRootMethod(repo, null, methodSignature);
            res.setRootMethod(methodSignature);
            return buildCallTopology(methodSignature, res);
        } catch (ThriftApiException | TException e) {
            log.error("getJavaProjectCallGraphByRootMethod error repo={}, methodSignature={}", repo, methodSignature, e);
        }
        return null;
    }

    public Map<String, List<String>> queryCallTopologyNew(String repo, String methodSignature) {
        try {
            ProjectCallGraphByRootMethodResponse res = analysisProjectThriftService.getJavaProjectCallGraphByRootMethod(repo, null, methodSignature);
            res.setRootMethod(methodSignature);
            return buildCallTopologyNew(methodSignature, res);
        } catch (ThriftApiException | TException e) {
            log.error("getJavaProjectCallGraphByRootMethod error repo={}, methodSignature={}", repo, methodSignature, e);
        }
        return null;
    }

    public Map<String, List<String>> buildCallTopologyNew(String rootMethodName, ProjectCallGraphByRootMethodResponse res) {
        Map<String, List<String>> result = Map.of();
        Set<String> visited = new HashSet<>();
        for (String invocation : res.getInvocationSignatures()) {
            // 过滤掉不需要的调用和POJO类的getter/setter方法
            if (!filterOutput(invocation, res.getMethodDefMap())) {
                recordAllNode(rootMethodName, res.getMethodDefMap(), visited);
            }
        }
        visited.add(rootMethodName);
        result = convertToMap(new ArrayList<>(visited));

        List<String> rmClassNames = new ArrayList<>();
        result.forEach((k, v) -> {
            if (v.stream().allMatch(e -> {
                String method = k + "." + e;
                return INIT_PATTERN.matcher(method).matches() || NOPARAM_PATTERN.matcher(method).matches() || SETTER_PATTERN.matcher(method).matches() || IS_PATTERN.matcher(method).matches();
            }) && v.size() >= 3) {
                rmClassNames.add(k);
            }
        });
        for (String className : rmClassNames) {
            result.remove(className);
        }
        return result;
    }

    public Map<String, List<String>> queryCallTopologyNewWithDepth(String repo, String methodSignature, int depth) {
        try {
            boolean isOpenRPC = Lion.getBoolean(MdpContextUtils.getAppKey(), "isOpenRPC", false);
            if (isOpenRPC) {
                ProjectCallGraphByRootMethodResponse res = analysisProjectThriftService.getJavaProjectCallGraphByRootMethod(repo, null, methodSignature);
                res.setRootMethod(methodSignature);
                return buildCallTopologyNewDepth(methodSignature, res, depth);
            } else {
                return getProjectCallTopologyAsMap(repo, methodSignature, depth);
            }
        } catch (ThriftApiException | TException e) {
            log.error("getJavaProjectCallGraphByRootMethod error repo={}, methodSignature={}", repo, methodSignature, e);
        }
        return null;
    }

    public Map<String, List<String>> buildCallTopologyNewDepth(String rootMethodName, ProjectCallGraphByRootMethodResponse res, int depth) {
        Map<String, List<String>> result = new HashMap<>();
        Set<String> visited = new HashSet<>();

        // 从根方法开始，按层级遍历调用链路，限制深度
        recordAllNodeWithDepth(rootMethodName, res.getMethodDefMap(), visited, depth, 0);
        visited.add(rootMethodName);
        result = convertToMap(new ArrayList<>(visited));

        List<String> rmClassNames = new ArrayList<>();
        result.forEach((k, v) -> {
            if (v.stream().allMatch(e -> {
                String method = k + "." + e;
                return INIT_PATTERN.matcher(method).matches() || NOPARAM_PATTERN.matcher(method).matches() || SETTER_PATTERN.matcher(method).matches() || IS_PATTERN.matcher(method).matches();
            }) && v.size() >= 3) {
                rmClassNames.add(k);
            }
        });
        for (String className : rmClassNames) {
            result.remove(className);
        }
        return result;
    }

    public CallTopology queryCallTopologyWithDepth(String repo, String methodSignature, int depth) {
        try {
            boolean isOpenRPC = Lion.getBoolean(MdpContextUtils.getAppKey(), "isOpenRPC", false);
            if (isOpenRPC) {
                ProjectCallGraphByRootMethodResponse res = analysisProjectThriftService.getJavaProjectCallGraphByRootMethod(repo, null, methodSignature);
                res.setRootMethod(methodSignature);
                return buildCallTopologyDepth(methodSignature, res, repo, depth);
            }else{
                return getProjectCallTopologyAsTopology(repo, methodSignature, depth);
            }
        } catch (ThriftApiException | TException e) {
            log.error("getJavaProjectCallGraphByRootMethod error repo={}, methodSignature={}", repo, methodSignature, e);
        }
        return null;
    }

    public CallTopology buildCallTopologyDepth(String rootMethodName, ProjectCallGraphByRootMethodResponse res, String repo, int depth) {
        // 获取项目方法列表并转换为Map格式
        Map<String, MethodDefWithoutInvocationsDTO> methodDefMap = getProjectMethodsAsMap(repo);
        Map<String, List<String>> result = new HashMap<>();
        result = convertToMap(new ArrayList<>(methodDefMap.keySet()));
        List<String> rmClassNames = new ArrayList<>();
        result.forEach((k, v) -> {
            if (v.stream().allMatch(e -> {
                String method = k + "." + e;
                return INIT_PATTERN.matcher(method).matches() || NOPARAM_PATTERN.matcher(method).matches() || SETTER_PATTERN.matcher(method).matches() || IS_PATTERN.matcher(method).matches();
            }) && v.size() >= 3) {
                rmClassNames.add(k);
            }
        });
        CallTopology root = new CallTopology(rootMethodName, new ArrayList<>());
        Set<String> visited = new HashSet<>();
        visited.add(rootMethodName);

        for (String invocation : res.getInvocationSignatures()) {
            if (!filterOutputNew(invocation, rmClassNames)) {
                CallTopology child = buildNodeDepth(invocation, res.getMethodDefMap(), visited, rmClassNames, depth, 1);
                root.getChildren().add(child);
            }
        }
        return root;
    }

    private CallTopology buildNodeDepth(String methodSignature, Map<String, CallGraphMethodDefDTO> methodDefMap, Set<String> visited, List<String> excludeClasses, int depth, int currentDepth) {
        CallTopology node = new CallTopology(methodSignature, null);

        // 如果已访问过或达到最大深度，则停止递归
        if (visited.contains(methodSignature) || currentDepth >= depth) {
            return node;
        }
        visited.add(methodSignature);

        CallGraphMethodDefDTO methodDef = methodDefMap.get(methodSignature);
        if (methodDef == null) {
            return node;
        }
        List<CallTopology> children = new ArrayList<>();
        for (String invocation : methodDef.getInvocationSignatures()) {
            if (!invocation.equals(methodSignature)) {
                if (!filterOutputNew(invocation, excludeClasses)) {
                    CallTopology child = buildNodeDepth(invocation, methodDefMap, visited, excludeClasses, depth, currentDepth + 1);
                    children.add(child);
                }
            }
        }

        if (!children.isEmpty()) {
            node.setChildren(children);
        }
        return node;
    }

    public Map<String, List<String>> queryCallTopologyNewWithExcludeClass(String repo, String methodSignature, List<String> excludeClasses) {
        try {
            ProjectCallGraphByRootMethodResponse res = analysisProjectThriftService.getJavaProjectCallGraphByRootMethod(repo, null, methodSignature);
            res.setRootMethod(methodSignature);
            return buildCallTopologyNewWithExcludeClass(methodSignature, res, excludeClasses);
        } catch (ThriftApiException | TException e) {
            log.error("getJavaProjectCallGraphByRootMethod error repo={}, methodSignature={}", repo, methodSignature, e);
        }
        return null;
    }

    public Map<String, List<String>> buildCallTopologyNewWithExcludeClass(String rootMethodName, ProjectCallGraphByRootMethodResponse res, List<String> excludeClasses) {
        Map<String, List<String>> result = new HashMap<>();
        Set<String> visited = new HashSet<>();

        // 从根方法开始遍历调用链路，排除指定的类
        recordAllNodeWithExcludeClass(rootMethodName, res.getMethodDefMap(), visited, excludeClasses);
        visited.add(rootMethodName);
        result = convertToMap(new ArrayList<>(visited));

        List<String> rmClassNames = new ArrayList<>();
        result.forEach((k, v) -> {
            if (v.stream().allMatch(e -> {
                String method = k + "." + e;
                return INIT_PATTERN.matcher(method).matches() || NOPARAM_PATTERN.matcher(method).matches() || SETTER_PATTERN.matcher(method).matches() || IS_PATTERN.matcher(method).matches();
            }) && v.size() >= 3) {
                rmClassNames.add(k);
            }
        });
        for (String className : rmClassNames) {
            result.remove(className);
        }
        return result;
    }

    /**
     * 按深度限制记录所有节点
     *
     * @param methodSignature 方法签名
     * @param methodDefMap    方法定义映射
     * @param visited         已访问的方法集合
     * @param maxDepth        最大深度
     * @param currentDepth    当前深度
     */
    private void recordAllNodeWithDepth(String methodSignature, Map<String, CallGraphMethodDefDTO> methodDefMap, Set<String> visited, int maxDepth, int currentDepth) {
        // 如果超过最大深度或已访问过，则停止递归
        if (currentDepth >= maxDepth || visited.contains(methodSignature)) {
            return;
        }
        visited.add(methodSignature);
        CallGraphMethodDefDTO methodDef = methodDefMap.get(methodSignature);
        if (methodDef != null) {
            for (String invocation : methodDef.getInvocationSignatures()) {
                if (!invocation.equals(methodSignature)) {
                    // 过滤掉不需要的调用和POJO类的getter/setter方法
                    if (!filterOutput(invocation, methodDefMap)) {
                        recordAllNodeWithDepth(invocation, methodDefMap, visited, maxDepth, currentDepth + 1);
                    }
                }
            }
        }
    }

    /**
     * 记录所有节点，排除指定的类
     *
     * @param methodSignature 方法签名
     * @param methodDefMap    方法定义映射
     * @param visited         已访问的方法集合
     * @param excludeClasses  要排除的类列表
     */
    private void recordAllNodeWithExcludeClass(String methodSignature, Map<String, CallGraphMethodDefDTO> methodDefMap, Set<String> visited, List<String> excludeClasses) {
        if (visited.contains(methodSignature)) {
            return;
        }
        // 检查当前方法是否属于要排除的类
        String className = extractClassName(methodSignature);
        if (className != null && excludeClasses != null && excludeClasses.contains(className)) {
            return;
        }
        visited.add(methodSignature);
        CallGraphMethodDefDTO methodDef = methodDefMap.get(methodSignature);
        if (methodDef != null) {
            for (String invocation : methodDef.getInvocationSignatures()) {
                if (!invocation.equals(methodSignature)) {
                    // 过滤掉不需要的调用和POJO类的getter/setter方法
                    if (!filterOutput(invocation, methodDefMap)) {
                        recordAllNodeWithExcludeClass(invocation, methodDefMap, visited, excludeClasses);
                    }
                }
            }
        }
    }

    /**
     * 将字符串列表转换成Map
     *
     * @param inputList 输入的字符串列表
     * @return 转换后的Map，key为类名，value为方法名列表
     */
    public static Map<String, List<String>> convertToMap(List<String> inputList) {
        Map<String, List<String>> resultMap = new HashMap<>();

        for (String str : inputList) {
            // 先找到第一个左括号的位置
            int leftParenIndex = str.indexOf('(');

            if (leftParenIndex != -1) {
                // 从左括号位置往前找，找到最近的一个点号
                int dotIndex = str.lastIndexOf('.', leftParenIndex);

                if (dotIndex != -1 && dotIndex < leftParenIndex) {
                    // 截取类名（点号前的部分）
                    String className = str.substring(0, dotIndex);

                    // 截取方法名和参数（点号后的部分）
                    String methodSignature = str.substring(dotIndex + 1);

                    // 如果Map中已存在该类名，则添加到现有列表中；否则创建新列表
                    resultMap.computeIfAbsent(className, k -> new ArrayList<>()).add(methodSignature);
                }
            }
        }

        return resultMap;
    }

    private void recordAllNode(String methodSignature, Map<String, CallGraphMethodDefDTO> methodDefMap, Set<String> visited) {
        if (!visited.contains(methodSignature)) {
            visited.add(methodSignature);
            CallGraphMethodDefDTO methodDef = methodDefMap.get(methodSignature);
            if (methodDef != null) {
                for (String invocation : methodDef.getInvocationSignatures()) {
                    if (!invocation.equals(methodSignature)) {
                        // 过滤掉不需要的调用和POJO类的getter/setter方法
                        if (!filterOutput(invocation, methodDefMap)) {
                            recordAllNode(invocation, methodDefMap, visited);
                        }
                    }
                }
            }
        }
    }

    public CallTopology buildCallTopology(String rootMethodName, ProjectCallGraphByRootMethodResponse res) {
        CallTopology root = new CallTopology(rootMethodName, new ArrayList<>());
        Set<String> visited = new HashSet<>();
        visited.add(rootMethodName);

        for (String invocation : res.getInvocationSignatures()) {
            // 过滤掉不需要的调用和无调用方法的getter/setter方法
            if (!filterOutput(invocation, res.getMethodDefMap())) {
                CallTopology child = buildNode(invocation, res.getMethodDefMap(), visited);
                if (isGetterSetterMethod(invocation) && (child.getChildren() == null || child.getChildren().isEmpty())) {
                    continue;
                }
                root.getChildren().add(child);
            }
        }
        return root;
    }

    private CallTopology buildNode(String methodSignature, Map<String, CallGraphMethodDefDTO> methodDefMap, Set<String> visited) {
        CallTopology node = new CallTopology(methodSignature, null);
        if (visited.contains(methodSignature)) {
            // 已经处理过这个方法，避免循环依赖导致栈溢出
            return node;
        }
        visited.add(methodSignature);

        CallGraphMethodDefDTO methodDef = methodDefMap.get(methodSignature);
        if (methodDef == null) {
            return node;
        }
        List<CallTopology> children = new ArrayList<>();
        for (String invocation : methodDef.getInvocationSignatures()) {
            if (!invocation.equals(methodSignature)) {
                // 过滤掉不需要的调用和无调用方法的getter/setter方法
                if (!filterOutput(invocation, methodDefMap)) {
                    CallTopology child = buildNode(invocation, methodDefMap, visited);
                    children.add(child);
                }
            }
        }

        if (!children.isEmpty()) {
            node.setChildren(children);
        }
        return node;
    }

    /**
     * 判断一个方法是否是getter、setter、is方法
     *
     * @param methodSignature 方法签名
     * @return 是否是getter、setter方法
     */
    private boolean isGetterSetterMethod(String methodSignature) {
        return GETTER_PATTERN.matcher(methodSignature).matches() ||
                SETTER_PATTERN.matcher(methodSignature).matches() ||
                IS_PATTERN.matcher(methodSignature).matches();
    }

    /**
     * 从方法签名中提取类名
     *
     * @param methodSignature 方法签名
     * @return 类名
     */
    private String extractClassName(String methodSignature) {
        try {
            // 假设方法签名格式为：com.example.ClassName.methodName(args)
            int parenthesisIndex = methodSignature.indexOf("(");
            if (parenthesisIndex <= 0) {
                return null;
            }
            String beforeParenthesis = methodSignature.substring(0, parenthesisIndex);
            int lastDotIndex = beforeParenthesis.lastIndexOf(".");
            if (lastDotIndex <= 0) {
                return null;
            }
            return methodSignature.substring(0, lastDotIndex);
        } catch (Exception e) {
            log.warn("提取类名失败: {}", methodSignature, e);
            return null;
        }
    }

    /**
     * 排除一些链路
     *
     * @param invocation 调用链
     * @return true filter
     */
    private boolean filterOutput(String invocation, Map<String, CallGraphMethodDefDTO> methodDefMap) {

        // 检查是否匹配初始化方法或无参方法模式
        if (INIT_PATTERN.matcher(invocation).matches() || NOPARAM_PATTERN.matcher(invocation).matches()) {
            return true;
        }

        // 检查是否是POJO类的getter/setter方法
        if ((SETTER_PATTERN.matcher(invocation).matches() || IS_PATTERN.matcher(invocation).matches()) && methodDefMap != null && methodDefMap.containsKey(invocation) && CollectionUtils.isEmpty(methodDefMap.get(invocation).getInvocationSignatures())) {
            return true;
        }

        List<String> prefixList = Lion.getList(MdpContextUtils.getAppKey(), "TOPOLOGY_EXCLUDE_PACKAGE_NAMES", String.class);
        if (CollectionUtils.isEmpty(prefixList)) {
            prefixList = List.of(
                    "java.util.", "java.lang.", "java.math.", "java.time.", "java.net.", "java.text.", "org.apache.", "org.jsoup.", "org.slf4j.", "com.alibaba.", "com.dianping.lion.", "com.dianping.cat.", "com.dianping.pigeon.", "com.dianping.squirrel.", "com.google.", "com.fasterxml.", "org.springframework."
            );
        }
        return prefixList.stream().anyMatch(invocation::startsWith);
    }

    private boolean filterOutputNew(String invocation, List<String> excludeClass) {
        if (excludeClass.contains(extractClassName(invocation))) {
            return true;
        }
        List<String> prefixList = Lion.getList(MdpContextUtils.getAppKey(), "TOPOLOGY_EXCLUDE_PACKAGE_NAMES", String.class);
        if (CollectionUtils.isEmpty(prefixList)) {
            prefixList = List.of(
                    "java.util.", "java.lang.", "java.math.", "java.time.", "java.net.", "java.text.", "org.apache.", "org.jsoup.", "org.slf4j.", "com.alibaba.", "com.dianping.lion.", "com.dianping.cat.", "com.dianping.pigeon.", "com.dianping.squirrel.", "com.google.", "com.fasterxml.", "org.springframework."
            );
        }
        return prefixList.stream().anyMatch(invocation::startsWith);
    }

    /**
     * 获取指定HTTP请求的返回值
     *
     * @param url HTTP请求URL
     * @return HTTP响应结果
     */
    public String getHttpResponse(String url) {
        try {
            String response = webClient.get()
                    .uri(url)
                    .retrieve()
                    .bodyToMono(String.class)
                    .timeout(Duration.ofSeconds(30))
                    .block();
            return response;

        } catch (Exception e) {
            return "HTTP请求失败: " + e.getMessage();
        }
    }

    /**
     * 获取项目方法列表的HTTP请求返回值
     *
     * @param repo 仓库地址
     * @return 项目方法列表的JSON字符串
     */
    public String getProjectMethodsHttpResponse(String repo) {
        String url = BASEURL + "/project/methods?repo=" + repo;
        return getHttpResponse(url);
    }

    /**
     * 获取项目方法列表并转换为Map格式
     *
     * @param repo 仓库地址
     * @return 方法定义映射，key为方法签名，value为方法定义对象
     */
    public Map<String, MethodDefWithoutInvocationsDTO> getProjectMethodsAsMap(String repo) {
        try {
            // 获取HTTP响应的JSON字符串
            String jsonResponse = getProjectMethodsHttpResponse(repo);

            if (jsonResponse == null || jsonResponse.startsWith("HTTP请求失败")) {
                log.warn("HTTP请求失败");
                return new HashMap<>();
            }
            // 将JSON字符串转换为Map<String, MethodDefWithoutInvocationsDTO>
            JsonNode root = objectMapper.readTree(jsonResponse);
            JsonNode dataNode = root.get("data");
            Map<String, MethodDefWithoutInvocationsDTO> methodDefMap = objectMapper.convertValue(dataNode, new TypeReference<Map<String, MethodDefWithoutInvocationsDTO>>() {
            });

            return methodDefMap != null ? methodDefMap : new HashMap<>();

        } catch (Exception e) {
            log.error("解析HTTP响应JSON失败: {}", e.getMessage(), e);
            return new HashMap<>();
        }
    }

    /**
     * 获取depth层项目调用链路并转换为Map格式
     *
     * @param repo            仓库地址
     * @param methodSignature 方法签名
     * @param depth           深度
     * @return 链路中类映射，key为类签名，value为其方法列表
     */
    public Map<String, List<String>> getProjectCallTopologyAsMap(String repo, String methodSignature, int depth) {
        try {
            // 获取HTTP响应的JSON字符串
            String url = BASEURL + "/topology/query/new/depth?repo=" + repo + "&method-signature=" + methodSignature + "&depth=" + depth;
            String jsonResponse = getHttpResponse(url);

            if (jsonResponse == null || jsonResponse.startsWith("HTTP请求失败")) {
                log.warn("HTTP请求失败");
                return new HashMap<>();
            }
            JsonNode root = objectMapper.readTree(jsonResponse);
            JsonNode dataNode = root.get("data");
            Map<String, List<String>> methodDefMap = objectMapper.convertValue(dataNode, new TypeReference<Map<String, List<String>>>() {
            });
            return methodDefMap != null ? methodDefMap : new HashMap<>();

        } catch (Exception e) {
            log.error("解析HTTP响应JSON失败: {}", e.getMessage(), e);
            return new HashMap<>();
        }
    }
    /**
     * 获取depth层项目调用链路并转换为Topology格式
     *
     * @param repo            仓库地址
     * @param methodSignature 方法签名
     * @param depth           深度
     * @return 调用链路
     */
    public CallTopology getProjectCallTopologyAsTopology(String repo, String methodSignature, int depth) {
        try {
            // 获取HTTP响应的JSON字符串
            String url = BASEURL + "/topology/query/depth?repo=" + repo + "&method-signature=" + methodSignature + "&depth=" + depth;
            String jsonResponse = getHttpResponse(url);

            if (jsonResponse == null || jsonResponse.startsWith("HTTP请求失败")) {
                log.warn("HTTP请求失败");
                return null;
            }
            JsonNode root = objectMapper.readTree(jsonResponse);
            JsonNode dataNode = root.get("data");
            CallTopology methodDefMap = objectMapper.convertValue(dataNode, new TypeReference<CallTopology>() {});
            return methodDefMap != null ? methodDefMap : null;

        } catch (Exception e) {
            log.error("解析HTTP响应JSON失败: {}", e.getMessage(), e);
            return null;
        }
    }
}

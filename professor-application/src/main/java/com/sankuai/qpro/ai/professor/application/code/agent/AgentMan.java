package com.sankuai.qpro.ai.professor.application.code.agent;

import com.sankuai.qpro.ai.professor.application.code.agent.consts.Consts;
import com.sankuai.qpro.ai.professor.application.code.agent.core.edge.StateDispatcherOrEnd;
import com.sankuai.qpro.ai.professor.application.code.agent.core.node.*;
import com.sankuai.qpro.ai.professor.application.code.agent.state.ResearchState;
import com.sankuai.qpro.ai.professor.application.code.agent.state.ResearchStateSerializer;
import lombok.extern.slf4j.Slf4j;
import org.bsc.langgraph4j.CompileConfig;
import org.bsc.langgraph4j.CompiledGraph;
import org.bsc.langgraph4j.GraphRepresentation;
import org.bsc.langgraph4j.StateGraph;
import org.bsc.langgraph4j.action.AsyncEdgeAction;
import org.bsc.langgraph4j.action.AsyncNodeAction;
import org.bsc.langgraph4j.checkpoint.MemorySaver;
import org.bsc.langgraph4j.utils.EdgeMappings;
import org.springframework.stereotype.Service;

/**
 * AgentMan
 *
 * <AUTHOR>
 * @since 2025/8/5
 */
@Slf4j
@Service
public class AgentMan {

    private final Coordinator coordinator;

    private final RewriteAndMultiQuery rewriteAndMultiQuery;

    private final Reasoning reasoning;

    private final CodeSearch codeSearch;

    private final Summary summary;

    public AgentMan(Coordinator coordinator,
                    RewriteAndMultiQuery rewriteAndMultiQuery,
                    Reasoning reasoning,
                    CodeSearch codeSearch,
                    Summary summary) {
        this.coordinator = coordinator;
        this.rewriteAndMultiQuery = rewriteAndMultiQuery;
        this.reasoning = reasoning;
        this.codeSearch = codeSearch;
        this.summary = summary;
    }

    public CompiledGraph<ResearchState> codeSearch() throws Throwable {
        StateGraph<ResearchState> graph = new StateGraph<>(ResearchState.SCHEMA, new ResearchStateSerializer())
                // 执行节点
                .addNode(Consts.ConstsNode.COORDINATOR, AsyncNodeAction.node_async(coordinator))
                .addNode(Consts.ConstsNode.REWRITE_AND_MULTI_QUERY, AsyncNodeAction.node_async(rewriteAndMultiQuery))
                .addNode(Consts.ConstsNode.CODE_SEARCH, AsyncNodeAction.node_async(codeSearch))
                .addNode(Consts.ConstsNode.REASONING, AsyncNodeAction.node_async(reasoning))
                .addNode(Consts.ConstsNode.SUMMARY, AsyncNodeAction.node_async(summary))
                // 固定开始
                .addEdge(org.bsc.langgraph4j.StateGraph.START, Consts.ConstsNode.COORDINATOR)
                // 任务协调节点条件边
                .addConditionalEdges(Consts.ConstsNode.COORDINATOR,
                        AsyncEdgeAction.edge_async(
                                new StateDispatcherOrEnd(Consts.ConstsNode.COORDINATOR, Consts.ConstsState.COORDINATOR_NEXT_NODE)),
                        EdgeMappings.builder()
                                .to(Consts.ConstsNode.REWRITE_AND_MULTI_QUERY, Consts.ConstsNode.REWRITE_AND_MULTI_QUERY)
                                .to(Consts.ConstsNode.SUMMARY, org.bsc.langgraph4j.StateGraph.END)
                                .build()
                )
                .addEdge(Consts.ConstsNode.REWRITE_AND_MULTI_QUERY, Consts.ConstsNode.CODE_SEARCH)
                .addEdge(Consts.ConstsNode.CODE_SEARCH, Consts.ConstsNode.REASONING)
                // 推理节点条件边
                .addConditionalEdges(Consts.ConstsNode.REASONING,
                        AsyncEdgeAction.edge_async(
                                new StateDispatcherOrEnd(Consts.ConstsNode.REASONING, Consts.ConstsState.REASONING_NEXT_NODE)),
                        EdgeMappings.builder()
                                .to(Consts.ConstsNode.REWRITE_AND_MULTI_QUERY, Consts.ConstsNode.REWRITE_AND_MULTI_QUERY)
                                .to(Consts.ConstsNode.SUMMARY, org.bsc.langgraph4j.StateGraph.END)
                                .build()
                )
                // 固定结束
                .addEdge(Consts.ConstsNode.SUMMARY, StateGraph.END);
        GraphRepresentation graphRepresentation = graph.getGraph(GraphRepresentation.Type.MERMAID, "Code Search Agent", true);
        log.info("code search graph mermaid：\n{}", graphRepresentation.content());
        CompileConfig config = CompileConfig.builder().checkpointSaver(new MemorySaver()).build();
        return graph.compile(config);
    }

}
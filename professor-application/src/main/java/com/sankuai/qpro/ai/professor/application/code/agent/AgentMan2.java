package com.sankuai.qpro.ai.professor.application.code.agent;

import com.sankuai.qpro.ai.professor.application.code.agent.state.ResearchState;
import com.sankuai.qpro.ai.professor.application.code.agent.state.ResearchStateSerializer;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.bsc.langgraph4j.CompileConfig;
import org.bsc.langgraph4j.CompiledGraph;
import org.bsc.langgraph4j.GraphRepresentation;
import org.bsc.langgraph4j.StateGraph;
import org.bsc.langgraph4j.action.AsyncEdgeAction;
import org.bsc.langgraph4j.action.AsyncNodeAction;
import org.bsc.langgraph4j.checkpoint.MemorySaver;
import org.bsc.langgraph4j.utils.EdgeMappings;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * AgentMan
 *
 * <AUTHOR>
 * @since 2025/8/5
 */
@Slf4j
@Service
public class AgentMan2 {

    public AgentMan2() {
    }

    public CompiledGraph<ResearchState> agentDeepResearch() throws Throwable {

        StateGraph<ResearchState> researcherAgent = new StateGraph<>(ResearchState.SCHEMA, new ResearchStateSerializer())
                // 执行节点
                .addNode("意图识别", AsyncNodeAction.node_async(state -> Map.of()))
                .addNode("上下文感知", AsyncNodeAction.node_async(state -> Map.of()))
                .addNode("规划_TodoList", AsyncNodeAction.node_async(state -> Map.of()))
                .addNode("任务执行协调", AsyncNodeAction.node_async(state -> Map.of()))
                .addNode("代码检索Agent", new StateGraph<>(ResearchState.SCHEMA, new ResearchStateSerializer())
                        .addNode("Query改写扩写", AsyncNodeAction.node_async(state -> Map.of()))
                        .addNode("推理检索_ToolCall", AsyncNodeAction.node_async(state -> Map.of()))
                        .addNode("检索结果评判_正确性_相关性_完整性", AsyncNodeAction.node_async(state -> Map.of()))
                        .addNode("检索结果Report", AsyncNodeAction.node_async(state -> Map.of()))
                        .addEdge(StateGraph.START, "Query改写扩写")
                        .addEdge("Query改写扩写", "推理检索_ToolCall")
                        .addEdge("推理检索_ToolCall", "检索结果评判_正确性_相关性_完整性")
                        // 条件边
                        .addConditionalEdges("检索结果评判_正确性_相关性_完整性", AsyncEdgeAction.edge_async(state -> state.value("next_node").toString()),
                                EdgeMappings.builder()
                                        .to("Query改写扩写", "collect")
                                        .to("检索结果Report", "end")
                                        .build()
                        )
                        .addEdge("检索结果Report", StateGraph.END)
                )
                .addNode("动态反思", AsyncNodeAction.node_async(state -> Map.of()))
                .addNode("结束节点", AsyncNodeAction.node_async(state -> Map.of()))
                // 固定开始
                .addEdge(StateGraph.START, "意图识别")
                .addEdge("上下文感知", "规划_TodoList")
                .addEdge("规划_TodoList", "任务执行协调")
                .addEdge("代码检索Agent", "动态反思")
                // 条件边
                .addConditionalEdges("意图识别", AsyncEdgeAction.edge_async(state -> state.value("next_node").toString()),
                        EdgeMappings.builder()
                                .to("上下文感知", "collect")
                                .to("结束节点", "end")
                                .build()
                )
                .addConditionalEdges("任务执行协调", AsyncEdgeAction.edge_async(state -> state.value("next_node").toString()),
                        EdgeMappings.builder()
                                .to("代码检索Agent", "search")
                                .to("结束节点", "end")
                                .build()
                )
                .addConditionalEdges("动态反思", AsyncEdgeAction.edge_async(state -> state.value("next_node").toString()),
                        EdgeMappings.builder()
                                .to("规划_TodoList", "Reflection")
                                .to("任务执行协调", "exec")
                                .build()
                )
                // 固定结束
                .addEdge("结束节点", StateGraph.END);
        GraphRepresentation graphRepresentation = researcherAgent.getGraph(GraphRepresentation.Type.MERMAID, "Code Deep Research Assistant", true);
        log.info("code deep research assistant graph mermaid：\n{}", graphRepresentation.content());
        System.out.println(graphRepresentation.content());
        CompileConfig config = CompileConfig.builder().checkpointSaver(new MemorySaver()).build();
        return researcherAgent.compile(config);
    }

    @SneakyThrows
    public static void main(String[] args) {
        AgentMan2 agentMan2 = new AgentMan2();
        agentMan2.agentDeepResearch();
    }

}
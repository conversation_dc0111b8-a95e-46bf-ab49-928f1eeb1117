package com.sankuai.qpro.ai.professor.application.code.agent.consts;

/**
 * Consts
 *
 * <AUTHOR>
 * @since 2025/8/5
 */
public class Consts {

    public interface ConstsNode {

        /**
         * 协调节点（引导使用，后续可做任务规划）
         */
        String COORDINATOR = "coordinator";

        /**
         * 查询改写扩写节点
         */
        String REWRITE_AND_MULTI_QUERY = "rewrite_and_multi_query";

        /**
         * 代码检索节点
         */
        String CODE_SEARCH = "code_search";

        /**
         * 推理节点
         */
        String REASONING = "reasoning";

        /**
         * 数据汇总
         */
        String SUMMARY = "summary";

    }


    public interface ConstsState {

        /**
         * 会话消息列表
         */
        String MESSAGES = "messages";

        /**
         * 初始需求
         */
        String REQUIREMENT = "requirement";

        /**
         * 用户输入的查询词
         */
        String QUERY = "query";

        /**
         * 优化后的查询词
         */
        String OPTIMIZE_QUERIES = "optimize_queries";

        /**
         * 优化后的查询词
         */
        String SEARCH_SUMMARY = "search_summary";

        /**
         * 核心代码的入口
         */
        String KEY_CODE_ENTRANCE = "key_code_entrance";

        /**
         * 协调节点下个执行节点
         */
        String COORDINATOR_NEXT_NODE = "coordinator_next_node";

        /**
         * 推理节点下个执行节点
         */
        String REASONING_NEXT_NODE = "reasoning_next_node";

        /**
         * 预检索节点下个执行节点
         */
        String PRE_RETRIEVAL_NEXT_NODE = "pre_retrieval_next_node";

    }

}
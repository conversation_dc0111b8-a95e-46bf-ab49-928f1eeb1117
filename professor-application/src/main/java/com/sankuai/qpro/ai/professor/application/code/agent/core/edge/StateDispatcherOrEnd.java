package com.sankuai.qpro.ai.professor.application.code.agent.core.edge;

import com.sankuai.qpro.ai.professor.application.code.agent.state.ResearchState;
import com.sankuai.qpro.ai.professor.application.code.agent.utils.StateUtils;
import lombok.extern.slf4j.Slf4j;
import org.bsc.langgraph4j.action.EdgeAction;

import static org.bsc.langgraph4j.StateGraph.END;

/**
 * 状态条件边分发器
 *
 * <AUTHOR>
 * @since 2025/8/5
 */
@Slf4j
public class StateDispatcherOrEnd implements EdgeAction<ResearchState> {

    private final String currentNode;

    private final String nextNodeIdentification;

    public StateDispatcherOrEnd(String currentNode, String nextNodeIdentification) {
        this.currentNode = currentNode;
        this.nextNodeIdentification = nextNodeIdentification;
    }


    @Override
    public String apply(ResearchState state) {
        String nextNode = StateUtils.defaultStr(state, nextNodeIdentification, END);
        log.info("执行状态条件分发决策，评估节点: {} 下一个节点: {}", currentNode, nextNode);
        return nextNode;
    }

}
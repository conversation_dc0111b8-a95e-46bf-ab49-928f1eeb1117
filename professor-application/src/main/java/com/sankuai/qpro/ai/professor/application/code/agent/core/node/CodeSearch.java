package com.sankuai.qpro.ai.professor.application.code.agent.core.node;

import com.google.common.collect.Maps;
import com.sankuai.qpro.ai.professor.application.code.agent.state.ResearchState;
import com.sankuai.qpro.ai.professor.application.code.agent.utils.StateUtils;
import lombok.extern.slf4j.Slf4j;
import org.bsc.langgraph4j.action.NodeAction;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.chat.messages.Message;
import org.springframework.ai.chat.prompt.ChatOptions;
import org.springframework.ai.model.tool.ToolCallingChatOptions;
import org.springframework.ai.model.tool.ToolCallingManager;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.Resource;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * 代码检索器
 *
 * <AUTHOR>
 * @since 2025/8/5
 */
@Slf4j
@Service
public class CodeSearch implements NodeAction<ResearchState> {

    private final Resource systemText;

    private final ChatClient chatClient;

    private final ChatOptions chatOptions;

    private final ToolCallingManager toolCallingManager;


    public CodeSearch(ChatClient.Builder builder, @Value("classpath:/prompts/system_prompt_code_search.st") Resource systemText) {
        this.chatClient = builder.build();
        this.systemText = systemText;
        chatOptions = ToolCallingChatOptions.builder()
                .model("anthropic.claude-3.7-sonnet")
                .temperature(0.0d)
                .internalToolExecutionEnabled(false)
                .build();
        toolCallingManager = ToolCallingManager.builder().build();
    }

    @Override
    public Map<String, Object> apply(ResearchState state) {
        log.info("代码检索节点正在运行～");
        List<Message> messages = state.messages();
        List<String> queries = StateUtils.optimizeQueries(state);

        // reason，推理要调用哪些工具

        // action，执行工具调用的结果

        // observe，将工具调用结果再给大模型接着推理

        Map<String, Object> updated = Maps.newConcurrentMap();
        return updated;
    }

}
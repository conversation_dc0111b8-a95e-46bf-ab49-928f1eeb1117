package com.sankuai.qpro.ai.professor.application.code.agent.core.node;

import com.google.common.collect.Maps;
import com.sankuai.qpro.ai.professor.application.code.agent.consts.Consts;
import com.sankuai.qpro.ai.professor.application.code.agent.state.ResearchState;
import com.sankuai.qpro.ai.professor.application.code.agent.utils.StateUtils;
import com.sankuai.qpro.ai.professor.application.code.tools.TaskTools;
import lombok.extern.slf4j.Slf4j;
import org.bsc.langgraph4j.action.NodeAction;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.chat.messages.AssistantMessage;
import org.springframework.ai.chat.messages.Message;
import org.springframework.ai.chat.messages.UserMessage;
import org.springframework.ai.chat.model.ChatResponse;
import org.springframework.ai.chat.model.Generation;
import org.springframework.ai.chat.prompt.ChatOptions;
import org.springframework.ai.chat.prompt.Prompt;
import org.springframework.ai.chat.prompt.SystemPromptTemplate;
import org.springframework.ai.model.tool.ToolCallingChatOptions;
import org.springframework.ai.model.tool.ToolCallingManager;
import org.springframework.ai.model.tool.ToolExecutionResult;
import org.springframework.ai.support.ToolCallbacks;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.Resource;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

import static org.bsc.langgraph4j.StateGraph.END;

/**
 * 协调器
 *
 * <AUTHOR>
 * @since 2025/8/5
 */
@Slf4j
@Service
public class Coordinator implements NodeAction<ResearchState> {

    private final Resource systemText;

    private final ChatClient chatClient;

    private final ChatOptions chatOptions;

    private final ToolCallingManager toolCallingManager;

    public Coordinator(ChatClient.Builder builder, @Value("classpath:/prompts/system_prompt_coordinator.st") Resource systemText) {
        this.chatClient = builder.build();
        this.systemText = systemText;
        chatOptions = ToolCallingChatOptions.builder().model("gpt-4.1-mini").temperature(0.0d).toolCallbacks(ToolCallbacks.from(new TaskTools())).internalToolExecutionEnabled(false).build();
        toolCallingManager = ToolCallingManager.builder().build();
    }

    @Override
    public Map<String, Object> apply(ResearchState state) {
        log.info("协调节点正在运行～");
        Map<String, Object> updated = Maps.newConcurrentMap();
        Message systemMessage = new SystemPromptTemplate(systemText).createMessage(Map.of("CURRENT_TIME", LocalDateTime.now()));
        UserMessage userMessage = new UserMessage(StateUtils.query(state));
        updated.put(Consts.ConstsState.MESSAGES, userMessage);
        Prompt prompt = new Prompt(List.of(systemMessage, userMessage), chatOptions);
        ChatResponse response = chatClient.prompt(prompt).call().chatResponse();
        // 获取 assistant 消息内容
        assert response != null;
        AssistantMessage assistantMessage = response.getResult().getOutput();
        // 判断是否触发工具调用
        String nextStep = END;
        if (!assistantMessage.getToolCalls().isEmpty()) {
            log.info("工具已调用: {}", assistantMessage.getToolCalls());
            nextStep = Consts.ConstsNode.REWRITE_AND_MULTI_QUERY;
            // 仅作记录使用
            ToolExecutionResult toolExecutionResult = toolCallingManager.executeToolCalls(prompt, response);
            List<Generation> generations = ToolExecutionResult.buildGenerations(toolExecutionResult);
            generations.forEach(e -> log.info("Tool Message Resp: {}", e.getOutput()));
        } else {
            log.warn("未触发工具调用");
            updated.put("message", assistantMessage);
        }
        updated.put(Consts.ConstsState.COORDINATOR_NEXT_NODE, nextStep);
        return updated;
    }

}
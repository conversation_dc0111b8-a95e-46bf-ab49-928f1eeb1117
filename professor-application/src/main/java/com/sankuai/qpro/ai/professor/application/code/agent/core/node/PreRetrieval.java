package com.sankuai.qpro.ai.professor.application.code.agent.core.node;

import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.meituan.mdp.boot.starter.config.annotation.MdpConfig;
import com.sankuai.qpro.ai.professor.application.code.agent.consts.Consts;
import com.sankuai.qpro.ai.professor.application.code.agent.state.ResearchState;
import com.sankuai.qpro.ai.professor.application.code.agent.utils.StateUtils;
import com.sankuai.qpro.ai.professor.application.model.codegenerate.PreQueryData;
import com.sankuai.qpro.ai.professor.application.model.codegenerate.PreQueryResult;
import com.sankuai.qpro.ai.professor.application.utils.SerializeUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.bsc.langgraph4j.action.NodeAction;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.chat.messages.AssistantMessage;
import org.springframework.ai.chat.messages.Message;
import org.springframework.ai.chat.messages.UserMessage;
import org.springframework.ai.chat.model.ChatResponse;
import org.springframework.ai.chat.model.Generation;
import org.springframework.ai.chat.prompt.ChatOptions;
import org.springframework.ai.chat.prompt.Prompt;
import org.springframework.ai.chat.prompt.SystemPromptTemplate;
import org.springframework.ai.model.tool.ToolCallingChatOptions;
import org.springframework.ai.model.tool.ToolCallingManager;
import org.springframework.ai.model.tool.ToolExecutionResult;
import org.springframework.ai.support.ToolCallbacks;
import org.springframework.ai.tool.annotation.Tool;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;
import org.springframework.stereotype.Service;
import org.springframework.util.StreamUtils;

import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import static org.bsc.langgraph4j.StateGraph.END;

/**
 * 预检索节点
 * 基于QueryRetrievalService逻辑，内嵌实现预检索功能
 *
 * <AUTHOR>
 * @since 2025/8/6
 */
@Slf4j
@Service
public class PreRetrieval implements NodeAction<ResearchState> {

    private final ChatClient chatClient;

    private final ChatOptions chatOptions;

    private final ToolCallingManager toolCallingManager;

    @MdpConfig("CODE_GENERATE_PRE_RETRIEVAL_PROMPT")
    private String queryPreRetrievalPrompt;

    public PreRetrieval(ChatClient.Builder builder) {
        this.chatClient = builder.build();
        this.chatOptions = ToolCallingChatOptions.builder()
                .model("gpt-4.1")
                .temperature(0.0d)
                .toolCallbacks(ToolCallbacks.from(this))
                .internalToolExecutionEnabled(false)
                .build();
        this.toolCallingManager = ToolCallingManager.builder().build();
    }

    @Override
    public Map<String, Object> apply(ResearchState state) {
        log.info("预检索节点正在运行～");

        String query = StateUtils.query(state);
        Map<String, Object> updated = new HashMap<>(16);

        try {
            // 执行预检索逻辑
            PreQueryResult preQueryResult = queryPreRetrieval(query);

            if (preQueryResult == null) {
                log.warn("❌ Pre-Retrieval未获取到有效结果");
                updated.put("message", new AssistantMessage("无法执行预检索，查询结果为空"));
                updated.put(Consts.ConstsState.PRE_RETRIEVAL_NEXT_NODE, END);
                return updated;
            }

            // 判断是否有工具调用
            String nextStep = END;

            // 将预检索结果存储到状态中
            updated.put("pre_query_result", preQueryResult);
            updated.put(Consts.ConstsState.PRE_RETRIEVAL_NEXT_NODE, nextStep);

            log.info("✅ Pre-Retrieval完成，找到{}个查询结果，{}个未找到结果",
                    preQueryResult.getQueryResults() != null ? preQueryResult.getQueryResults().size() : 0,
                    preQueryResult.getNotFoundQueryResults() != null ? preQueryResult.getNotFoundQueryResults().size() : 0);
        } catch (Exception e) {
            log.error("❌ Pre-Retrieval执行异常", e);
            updated.put("message", new AssistantMessage("Pre-Retrieval执行异常: " + e.getMessage()));
            updated.put(Consts.ConstsState.PRE_RETRIEVAL_NEXT_NODE, END);
        }

        return updated;
    }

    /**
     * Pre-Query-Retrieval 核心逻辑
     * 基于QueryRetrievalService.queryPreRetrieval实现
     */
    private PreQueryResult queryPreRetrieval(String requirement) {
        if (StringUtils.isBlank(requirement)) {
            return null;
        }
        List<PreQueryData> preQueryData = getPreQueryData(requirement);
        if (CollectionUtils.isEmpty(preQueryData)) {
            return null;
        }
        List<PreQueryData> queryResults = preQueryData.stream()
                .filter(data -> data != null && StringUtils.isNotBlank(data.getKeyCodeEntrance()))
                .collect(Collectors.toList());
        List<PreQueryData> notFoundQueryResults = preQueryData.stream()
                .filter(data -> data != null && StringUtils.isBlank(data.getKeyCodeEntrance()))
                .collect(Collectors.toList());
        PreQueryResult result = new PreQueryResult();
        result.setQueryResults(queryResults);
        result.setNotFoundQueryResults(notFoundQueryResults);
        return result;
    }

    /**
     * 获取预查询数据
     */
    private List<PreQueryData> getPreQueryData(String requirement) {
        // 1. 获取基础分层知识
        Map<String, String> baseKnowledge = getBaseKnowledge();
        List<Message> messages = buildMessage(requirement, baseKnowledge);
        Prompt prompt = new Prompt(messages, chatOptions);
        return chatClient.prompt(prompt)
                .tools(this)
                .call().entity(new ParameterizedTypeReference<List<PreQueryData>>() {
                });
    }

    /**
     * 构建消息
     */
    private List<Message> buildMessage(String requirement, Map<String, String> baseKnowledge) {
        List<Message> messages = Lists.newArrayList();
        SystemPromptTemplate systemPromptTemplate = new SystemPromptTemplate(queryPreRetrievalPrompt);
        Message systemMessage = systemPromptTemplate.createMessage(Map.of("layerBaseKnowledge", SerializeUtils.toJsonStr(baseKnowledge), "requirement", requirement));
        messages.add(systemMessage);
        return messages;
    }

    /**
     * 获取基础知识
     */
    private Map<String, String> getBaseKnowledge() {
        Map<String, String> result = Maps.newHashMap();
        result.put("流程分层", readFileContent("base_layer.md"));
        return result;
    }

    /**
     * 读取文件内容
     */
    private String readFileContent(String filePath) {
        try {
            ClassPathResource resource = new ClassPathResource(filePath);
            InputStream inputStream = resource.getInputStream();
            return StreamUtils.copyToString(inputStream, StandardCharsets.UTF_8);
        } catch (IOException e) {
            log.warn("读取文件失败: {}", filePath, e);
            return "";
        }
    }

    /**
     * 工具方法：获取指定关键入口类的知识
     */
    @Tool(name = "getKeyEntranceKnowledge", description = "获取指定关键入口类的知识")
    private String getKeyEntranceKnowledge(List<String> abilities) {
        String keyEntranceKnowledgeStr = readFileContent("layer_data_info.json");
        Map<String, Object> keyEntranceKnowledgeMap = SerializeUtils.toObj(keyEntranceKnowledgeStr, new TypeReference<Map<String, Object>>() {
        });
        if (CollectionUtils.isEmpty(abilities) || MapUtils.isEmpty(keyEntranceKnowledgeMap)) {
            return null;
        }
        Set<String> layerLowerSet = Sets.newHashSet();
        abilities.forEach(layer -> layerLowerSet.add(layer.toLowerCase()));
        Map<String, Object> filterKeyEntranceKnowledge = keyEntranceKnowledgeMap.entrySet().stream()
                .filter(entry -> entry != null && StringUtils.isNotBlank(entry.getKey())
                        && layerLowerSet.contains(entry.getKey().toLowerCase()))
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
        return SerializeUtils.toJsonStr(filterKeyEntranceKnowledge);
    }

    /**
     * 根据预检索结果和AI响应决定下一步节点
     */
    private String determineNextStep(PreQueryResult preQueryResult, AssistantMessage assistantMessage) {
        return END;
    }
}
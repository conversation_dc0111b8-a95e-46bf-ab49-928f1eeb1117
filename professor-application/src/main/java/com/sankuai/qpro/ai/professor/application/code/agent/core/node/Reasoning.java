package com.sankuai.qpro.ai.professor.application.code.agent.core.node;

import com.google.common.collect.Maps;
import com.sankuai.qpro.ai.professor.application.code.agent.consts.Consts;
import com.sankuai.qpro.ai.professor.application.code.agent.state.ResearchState;
import lombok.extern.slf4j.Slf4j;
import org.bsc.langgraph4j.action.NodeAction;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.chat.prompt.ChatOptions;
import org.springframework.ai.model.tool.ToolCallingChatOptions;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.Resource;
import org.springframework.stereotype.Service;

import java.util.Map;

import static org.bsc.langgraph4j.StateGraph.END;

/**
 * 推理节点
 *
 * <AUTHOR>
 * @since 2025/8/5
 */
@Slf4j
@Service
public class Reasoning implements NodeAction<ResearchState> {

    private final Resource systemText;

    private final ChatClient chatClient;

    private final ChatOptions chatOptions;

    public Reasoning(ChatClient.Builder builder, @Value("classpath:/prompts/system_prompt_reasoning.st") Resource systemText) {
        this.chatClient = builder.build();
        this.systemText = systemText;
        this.chatOptions = ToolCallingChatOptions.builder()
                .model("gpt-4.1")
                .temperature(0.0d)
                .build();
    }

    @Override
    public Map<String, Object> apply(ResearchState state) {
        log.info("代码检索结果推理节点正在运行～");
        Map<String, Object> updated = Maps.newConcurrentMap();
        String nextNode = END;
        updated.put(Consts.ConstsState.REASONING_NEXT_NODE, nextNode);
        return updated;
    }

}
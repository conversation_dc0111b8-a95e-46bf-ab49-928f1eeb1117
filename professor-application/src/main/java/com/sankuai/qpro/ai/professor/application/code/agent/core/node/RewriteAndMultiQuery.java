package com.sankuai.qpro.ai.professor.application.code.agent.core.node;

import com.google.common.collect.Maps;
import com.sankuai.qpro.ai.professor.application.code.agent.consts.Consts;
import com.sankuai.qpro.ai.professor.application.code.agent.state.ResearchState;
import com.sankuai.qpro.ai.professor.application.code.agent.utils.StateUtils;
import lombok.extern.slf4j.Slf4j;
import org.bsc.langgraph4j.action.NodeAction;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.rag.Query;
import org.springframework.ai.rag.preretrieval.query.expansion.MultiQueryExpander;
import org.springframework.ai.rag.preretrieval.query.expansion.QueryExpander;
import org.springframework.ai.rag.preretrieval.query.transformation.QueryTransformer;
import org.springframework.ai.rag.preretrieval.query.transformation.RewriteQueryTransformer;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


/**
 * 查询改写和扩写
 *
 * <AUTHOR>
 * @since 2025/8/9
 */
@Service
@Slf4j
public class RewriteAndMultiQuery implements NodeAction<ResearchState> {

    private final ChatClient.Builder builder;

    private final QueryTransformer queryTransformer;

    public RewriteAndMultiQuery(ChatClient.Builder builder) {
        this.builder = builder;
        // 查询重写
        this.queryTransformer = RewriteQueryTransformer.builder()
                .chatClientBuilder(builder)
                .build();
    }

    @Override
    public Map<String, Object> apply(ResearchState state) {
        log.info("查询改写扩写节点正在运行～");
        Map<String, Object> updated = Maps.newConcurrentMap();
        // todo：要感知上下文对用户的查询进行改写
        // 原始查询词
        Query query = Query.builder().text(StateUtils.query(state)).build();
        // 查询重写
        Query rewriteQuery = queryTransformer.transform(query);
        // 查询拓展
        int optimizeQueryNum = 3;
        QueryExpander queryExpander = MultiQueryExpander.builder()
                .chatClientBuilder(builder)
                .includeOriginal(true)
                .numberOfQueries(optimizeQueryNum)
                .build();
        // 结果构造
        List<Query> multiQueries = queryExpander.expand(rewriteQuery);
        List<String> newQueries = multiQueries.stream().map(Query::text).collect(Collectors.toList());
        updated.put(Consts.ConstsState.OPTIMIZE_QUERIES, newQueries);
        return updated;
    }

}
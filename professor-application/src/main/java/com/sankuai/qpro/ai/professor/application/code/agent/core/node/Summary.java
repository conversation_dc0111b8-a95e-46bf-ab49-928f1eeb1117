package com.sankuai.qpro.ai.professor.application.code.agent.core.node;

import com.sankuai.qpro.ai.professor.application.code.agent.consts.Consts;
import com.sankuai.qpro.ai.professor.application.code.agent.state.ResearchState;
import lombok.extern.slf4j.Slf4j;
import org.bsc.langgraph4j.action.NodeAction;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.chat.prompt.ChatOptions;
import org.springframework.ai.model.tool.ToolCallingChatOptions;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.Resource;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * 结果汇总节点 - 智能结果整合与格式化输出
 *
 * <AUTHOR>
 * @since 2025/8/5
 */
@Slf4j
@Service
public class Summary implements NodeAction<ResearchState> {

    private final Resource systemText;

    private final ChatClient chatClient;

    private final ChatOptions chatOptions;

    public Summary(ChatClient.Builder builder, @Value("classpath:/prompts/system_prompt_summary.st") Resource systemText) {
        this.chatClient = builder.build();
        this.systemText = systemText;
        this.chatOptions = ToolCallingChatOptions.builder()
                .model("gpt-4.1")
                .build();
    }

    @Override
    public Map<String, Object> apply(ResearchState state) {
        log.info("代码检索结果合成节点正在运行～");
        return Map.of(Consts.ConstsState.SEARCH_SUMMARY, "SEARCH_SUMMARY");
    }

}
package com.sankuai.qpro.ai.professor.application.code.agent.state;

import lombok.Getter;
import lombok.Setter;
import org.bsc.langgraph4j.prebuilt.MessagesState;
import org.springframework.ai.chat.messages.Message;

import java.util.Map;

/**
 * 渐进式精准搜索研究状态 - 扩展的上下文管理
 *
 * <AUTHOR>
 * @since 2025/8/5
 */
@Getter
@Setter
public class ResearchState extends MessagesState<Message> {

    public ResearchState(Map<String, Object> initData) {
        super(initData);
    }

}
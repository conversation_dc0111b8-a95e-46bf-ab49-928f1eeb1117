package com.sankuai.qpro.ai.professor.application.code.agent.utils;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Map;

/**
 * JsonUtils
 *
 * <AUTHOR>
 * @since 2025/8/5
 */
@Slf4j
public class JsonUtils {

    private static final ObjectMapper OBJECT_MAPPER;

    static {
        OBJECT_MAPPER = new ObjectMapper();
        OBJECT_MAPPER.registerModule(new JavaTimeModule());
        OBJECT_MAPPER.disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS);
        OBJECT_MAPPER.disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES);
        OBJECT_MAPPER.enable(DeserializationFeature.ACCEPT_EMPTY_STRING_AS_NULL_OBJECT);
    }

    /**
     * 对象序列化为JSON字符串
     */
    public static String toJson(Object object) {
        if (object == null) {
            return null;
        }
        try {
            return OBJECT_MAPPER.writeValueAsString(object);
        } catch (JsonProcessingException e) {
            log.error("对象序列化为JSON失败: {}", object, e);
            throw new RuntimeException("JSON序列化失败", e);
        }
    }

    /**
     * 对象序列化为格式化的JSON字符串
     */
    public static String toPrettyJson(Object object) {
        if (object == null) {
            return null;
        }
        try {
            return OBJECT_MAPPER.writerWithDefaultPrettyPrinter().writeValueAsString(object);
        } catch (JsonProcessingException e) {
            log.error("对象序列化为格式化JSON失败: {}", object, e);
            throw new RuntimeException("JSON序列化失败", e);
        }
    }

    /**
     * JSON字符串反序列化为指定类型对象
     */
    public static <T> T fromJson(String json, Class<T> clazz) {
        if (json == null || json.trim().isEmpty()) {
            return null;
        }
        try {
            return OBJECT_MAPPER.readValue(json, clazz);
        } catch (JsonProcessingException e) {
            log.error("JSON反序列化失败: json={}, targetClass={}", json, clazz.getName(), e);
            throw new RuntimeException("JSON反序列化失败", e);
        }
    }

    /**
     * JSON字符串反序列化为指定类型对象（使用TypeReference）
     */
    public static <T> T fromJson(String json, TypeReference<T> typeReference) {
        if (json == null || json.trim().isEmpty()) {
            return null;
        }
        try {
            return OBJECT_MAPPER.readValue(json, typeReference);
        } catch (JsonProcessingException e) {
            log.error("JSON反序列化失败: json={}, typeReference={}", json, typeReference.getType(), e);
            throw new RuntimeException("JSON反序列化失败", e);
        }
    }

    /**
     * JSON字符串反序列化为List
     */
    public static <T> List<T> fromJsonToList(String json, Class<T> clazz) {
        if (json == null || json.trim().isEmpty()) {
            return null;
        }
        try {
            JavaType listType = OBJECT_MAPPER.getTypeFactory().constructCollectionType(List.class, clazz);
            return OBJECT_MAPPER.readValue(json, listType);
        } catch (JsonProcessingException e) {
            log.error("JSON反序列化为List失败: json={}, elementClass={}", json, clazz.getName(), e);
            throw new RuntimeException("JSON反序列化失败", e);
        }
    }

    /**
     * JSON字符串反序列化为Map
     */
    public static Map<String, Object> fromJsonToMap(String json) {
        if (json == null || json.trim().isEmpty()) {
            return null;
        }
        try {
            return OBJECT_MAPPER.readValue(json, new TypeReference<Map<String, Object>>() {
            });
        } catch (JsonProcessingException e) {
            log.error("JSON反序列化为Map失败: json={}", json, e);
            throw new RuntimeException("JSON反序列化失败", e);
        }
    }

    /**
     * 对象转换为另一种类型的对象
     */
    public static <T> T convert(Object fromValue, Class<T> toClass) {
        if (fromValue == null) {
            return null;
        }
        try {
            return OBJECT_MAPPER.convertValue(fromValue, toClass);
        } catch (IllegalArgumentException e) {
            log.error("对象转换失败: fromValue={}, toClass={}", fromValue, toClass.getName(), e);
            throw new RuntimeException("对象转换失败", e);
        }
    }

    /**
     * 对象转换为另一种类型的对象（使用TypeReference）
     */
    public static <T> T convert(Object fromValue, TypeReference<T> typeReference) {
        if (fromValue == null) {
            return null;
        }
        try {
            return OBJECT_MAPPER.convertValue(fromValue, typeReference);
        } catch (IllegalArgumentException e) {
            log.error("对象转换失败: fromValue={}, typeReference={}", fromValue, typeReference.getType(), e);
            throw new RuntimeException("对象转换失败", e);
        }
    }

    /**
     * 验证字符串是否为有效的JSON格式
     */
    public static boolean isValidJson(String json) {
        if (json == null || json.trim().isEmpty()) {
            return false;
        }
        try {
            OBJECT_MAPPER.readTree(json);
            return true;
        } catch (JsonProcessingException e) {
            return false;
        }
    }

    /**
     * 获取ObjectMapper实例（如需自定义配置）
     */
    public static ObjectMapper getObjectMapper() {
        return OBJECT_MAPPER;
    }

}
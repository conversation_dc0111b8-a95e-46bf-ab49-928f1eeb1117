package com.sankuai.qpro.ai.professor.application.code.agent.utils;

import com.sankuai.qpro.ai.professor.application.code.agent.consts.Consts;
import com.sankuai.qpro.ai.professor.application.code.agent.state.ResearchState;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Optional;

/**
 * StateUtils
 *
 * <AUTHOR>
 * @since 2025/8/5
 */
@Slf4j
public class StateUtils {

    /**
     * 获取用户输入的查询词
     *
     * @param state 状态
     * @return query
     */
    public static String query(ResearchState state) {
        Optional<String> query = state.value(Consts.ConstsState.QUERY);
        return query.orElse("你好，深度研究助手～");
    }

    /**
     * 获取改写的查询词
     *
     * @param state 状态
     * @return query
     */
    public static List<String> optimizeQueries(ResearchState state) {
        Optional<List<String>> optimizeQueries = state.value(Consts.ConstsState.OPTIMIZE_QUERIES);
        return optimizeQueries.orElse(List.of());
    }

    public static String defaultStr(ResearchState state, String key, String defaultValue) {
        Optional<String> bool = state.value(key);
        return bool.orElse(defaultValue);
    }

    public static boolean defaultBool(ResearchState state, String key, boolean defaultValue) {
        Optional<Boolean> bool = state.value(key);
        return bool.orElse(defaultValue);
    }

    public static int defaultInt(ResearchState state, String key, int defaultValue) {
        Optional<Integer> number = state.value(key);
        return number.orElse(defaultValue);
    }

}
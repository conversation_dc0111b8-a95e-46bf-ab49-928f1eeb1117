package com.sankuai.qpro.ai.professor.application.code.ast.node;

import lombok.Data;

import java.util.Set;

/**
 * ClassNode
 *
 * <AUTHOR>
 * @since 2025/8/4
 */
@Data
public class ClassNode {

    /**
     * 唯一约束，全路径类名
     */
    private String key;

    /**
     * 方法
     */
    private Set<String> methods;

    /**
     * 源码
     */
    private String sourceCode;

    /**
     * 文件相对路径
     */
    private String relativePath;

    /**
     * 源码起始行
     */
    private Integer sourceStartLine;

    /**
     * 源码结束行
     */
    private Integer sourceEndLine;

}
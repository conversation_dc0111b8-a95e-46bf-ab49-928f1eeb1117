package com.sankuai.qpro.ai.professor.application.code.ast.node;

import lombok.Data;
import lombok.NonNull;

import java.util.List;
import java.util.Optional;
import java.util.Set;

/**
 * ClassNode
 *
 * <AUTHOR>
 * @since 2025/8/4
 */
@Data
public class MethodNode {

    /**
     * 唯一约束，(全路径类名+方法签名)
     */
    private String key;

    /**
     * 类名
     */
    private String className;

    /**
     * 简单签名
     */
    private String methodName;

    /**
     * 方法签名
     */
    private String methodSignature;

    /**
     * 变量名
     */
    private List<ParameterNode> parameters;

    /**
     * 返回类型
     */
    private String returnType;

    /**
     * 源码起始行
     */
    private Integer sourceStartLine;

    /**
     * 源码结束行
     */
    private Integer sourceEndLine;

    /**
     * 源码
     */
    private String sourceCode;

    /**
     * 文件相对路径
     */
    private String relativePath;

    /**
     * 继承
     */
    private Set<String> interfaceList;

    /**
     * 实现
     */
    private Set<String> implementsList;

    /**
     * 调用方法（下游）
     */
    private Set<String> invocation;

    /**
     * 方法被调用（上游）
     */
    private Set<String> called;

    /**
     * 根据参数名获取参数信息
     *
     * @param name 参数名
     * @return 参数信息
     */
    public Optional<ParameterNode> getParameterNodeByName(@NonNull String name) {
        return parameters.stream().filter(parameter -> parameter.getName().equals(name)).findFirst();
    }

}
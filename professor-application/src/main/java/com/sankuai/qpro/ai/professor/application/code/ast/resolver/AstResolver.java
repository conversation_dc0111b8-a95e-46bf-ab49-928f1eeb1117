package com.sankuai.qpro.ai.professor.application.code.ast.resolver;

import com.sankuai.qpro.ai.professor.application.code.ast.node.ClassNode;
import com.sankuai.qpro.ai.professor.application.code.ast.node.MethodNode;
import com.sankuai.qpro.ai.professor.application.code.ast.node.ParameterNode;
import com.sankuai.qpro.ai.professor.application.code.ast.node.Project;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;
import spoon.Launcher;
import spoon.reflect.CtModel;
import spoon.reflect.code.*;
import spoon.reflect.declaration.CtElement;
import spoon.reflect.declaration.CtMethod;
import spoon.reflect.declaration.CtParameter;
import spoon.reflect.declaration.CtType;
import spoon.reflect.reference.CtExecutableReference;
import spoon.reflect.reference.CtTypeReference;
import spoon.reflect.visitor.filter.TypeFilter;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * ClassNode
 *
 * <AUTHOR>
 * @since 2025/8/4
 */
@Slf4j
@Getter
public class AstResolver {

    private static volatile AstResolver instance;

    private final Map<String, Project> cache = new ConcurrentHashMap<>();

    /**
     * 获取单例 AstResolver 实例
     *
     * @return AstResolver 实例
     */
    public static AstResolver getInstance() {
        if (instance == null) {
            synchronized (AstResolver.class) {
                if (instance == null) {
                    instance = new AstResolver();
                }
            }
        }
        return instance;
    }

    private AstResolver() {
    }

    /**
     * 对指定项目路径进行解析，解析成抽象语法树(AST)
     *
     * @param projectDirectory 项目路径
     * @return 方法列表
     */
    public Project parseProjectAst(String projectDirectory) {
        if (!CollectionUtils.isEmpty(cache) && cache.containsKey(projectDirectory)) {
            return cache.get(projectDirectory);
        }
        log.info("init projectDirectory [{}]", projectDirectory);
        Launcher launcher = new Launcher();
        launcher.addInputResource(projectDirectory);
        launcher.buildModel();
        // 获取模型
        CtModel model = launcher.getModel();
        Project project = new Project();
        project.setProjectDirectory(projectDirectory);
        Map<String, MethodNode> methodNodeMap = new HashMap<>(16);
        Map<String, ClassNode> classNodeMap = new HashMap<>(16);
        project.setClassNodeMap(classNodeMap);
        project.setMethodNodeMap(methodNodeMap);
        for (CtType<?> ctType : model.getAllTypes()) {
            registerClass(ctType, project);
        }
        for (CtType<?> ctType : model.getAllTypes()) {
            registerMethod(ctType, project);
        }
        for (CtType<?> ctType : model.getAllTypes()) {
            findMethodTrace(ctType, methodNodeMap);
            for (CtTypeReference<?> interfaceRef : ctType.getSuperInterfaces()) {
                CtType<?> interfaceType = interfaceRef.getDeclaration();
                if (interfaceType != null) {
                    for (CtMethod<?> interfaceMethod : interfaceType.getMethods()) {
                        String interfaceDeclaration = interfaceType.getQualifiedName() + "." + interfaceMethod.getSignature();
                        MethodNode invocationMethodNode = methodNodeMap.get(interfaceDeclaration);
                        if (invocationMethodNode == null) {
                            continue;
                        }
                        for (MethodNode sourceMethodNode : methodNodeMap.values()) {
                            if (sourceMethodNode.getMethodSignature().equals(invocationMethodNode.getMethodSignature()) && !sourceMethodNode.getKey().equals(invocationMethodNode.getKey())) {
                                sourceMethodNode.getInterfaceList().add(invocationMethodNode.getKey());
                                invocationMethodNode.getImplementsList().add(sourceMethodNode.getKey());
                            }
                        }

                    }
                }
            }
        }

        cache.put(projectDirectory, project);
        return project;
    }

    /**
     * 注册类到全局缓存
     *
     * @param ctType ctType
     * @param ast    ast
     */
    private void registerClass(CtType<?> ctType, Project ast) {
        try {
            Map<String, ClassNode> classNodeMap = ast.getClassNodeMap();
            if (Objects.isNull(ctType.getPosition()) || Objects.isNull(ctType.getPosition().getCompilationUnit()) || Objects.isNull(ctType.getPosition().getCompilationUnit().getOriginalSourceCode()) || Objects.isNull(ctType.getPosition().getFile())) {
                return;
            }
            ClassNode classNode = new ClassNode();
            classNode.setMethods(new HashSet<>());
            classNode.setKey(ctType.getQualifiedName());
            classNode.setSourceCode(ctType.getPosition().getCompilationUnit().getOriginalSourceCode());
            classNode.setRelativePath(ctType.getPosition().getFile().getPath().replace(ast.getProjectDirectory(), "."));
            classNode.setSourceStartLine(ctType.getPosition().getLine());
            classNode.setSourceEndLine(ctType.getPosition().getEndLine());
            classNodeMap.put(ctType.getQualifiedName(), classNode);
            for (CtMethod<?> ctMethod : ctType.getMethods()) {
                String key = ctType.getQualifiedName() + "." + ctMethod.getSignature();
                classNode.getMethods().add(key);
            }
            for (CtType<?> nestedType : ctType.getNestedTypes()) {
                registerClass(nestedType, ast);
            }
        } catch (Exception e) {
            log.error("registerClass error {}", ctType.getQualifiedName(), e);
        }

    }

    /**
     * 注册方法到全局缓存
     *
     * @param ctType ctType
     * @param ast    ast
     */
    private void registerMethod(CtType<?> ctType, Project ast) {
        try {
            for (CtMethod<?> ctMethod : ctType.getMethods()) {
                String key = ctType.getQualifiedName() + "." + ctMethod.getSignature();
                MethodNode methodNode = new MethodNode();
                methodNode.setMethodSignature(ctMethod.getSignature());
                methodNode.setSourceStartLine(ctMethod.getPosition().getLine());
                methodNode.setSourceEndLine(ctMethod.getPosition().getEndLine());
                methodNode.setReturnType(ctMethod.getType().toString());
                methodNode.setClassName(ctType.getQualifiedName());
                try {
                    methodNode.setSourceCode(ctMethod.getOriginalSourceFragment().getSourceCode());
                } catch (Exception e) {
                    log.warn("Failed to get original source fragment for method {}.{}, using toString() as fallback. Error: {}", ctType.getQualifiedName(), ctMethod.getSignature(), e.getMessage());
                    methodNode.setSourceCode(ctMethod.toString());
                }
                methodNode.setParameters(new ArrayList<>());
                for (int i = 0; i < ctMethod.getParameters().size(); i++) {
                    CtParameter<?> parameter = ctMethod.getParameters().get(i);
                    ParameterNode parameterNode = new ParameterNode();
                    parameterNode.setName(parameter.getSimpleName());
                    parameterNode.setType(parameter.getType().toString());
                    parameterNode.setIndex(i);
                    methodNode.getParameters().add(parameterNode);
                }
                methodNode.setMethodName(ctMethod.getSimpleName());
                methodNode.setRelativePath(ctType.getPosition().getFile().getPath().replace(ast.getProjectDirectory(), "."));
                methodNode.setKey(key);
                methodNode.setInvocation(new HashSet<>());
                methodNode.setCalled(new HashSet<>());
                methodNode.setInterfaceList(new HashSet<>());
                methodNode.setImplementsList(new HashSet<>());
                ast.getMethodNodeMap().put(key, methodNode);
            }
            for (CtType<?> nestedType : ctType.getNestedTypes()) {
                registerMethod(nestedType, ast);
            }
        } catch (Exception e) {
            log.error("registerMethod error for class {} - failed to process methods. Error: {}", ctType.getQualifiedName(), e.getMessage(), e);
        }
    }

    /**
     * 查找方法调用
     *
     * @param ctType        ctType
     * @param methodNodeMap methodNodeMap
     */
    private void findMethodTrace(CtType<?> ctType, Map<String, MethodNode> methodNodeMap) {
        for (CtMethod<?> ctMethod : ctType.getMethods()) {
            String key = ctType.getQualifiedName() + "." + ctMethod.getSignature();
            MethodNode sourceMethodNode = methodNodeMap.get(key);
            if (sourceMethodNode == null) {
                log.warn("Source method node not found for key: {}", key);
                continue;
            }

            // 处理普通方法调用
            processMethodInvocations(ctMethod, sourceMethodNode, methodNodeMap, key);

            // 处理构造函数调用
            processConstructorCalls(ctMethod, sourceMethodNode, methodNodeMap, key);

            // 处理Lambda表达式中的调用
            processLambdaExpressions(ctMethod, sourceMethodNode, methodNodeMap, key);

            // 处理方法引用
            processMethodReferences(ctMethod, sourceMethodNode, methodNodeMap, key);
        }

        // 递归处理嵌套类型
        for (CtType<?> nestedType : ctType.getNestedTypes()) {
            findMethodTrace(nestedType, methodNodeMap);
        }
    }

    /**
     * 处理普通方法调用
     */
    private void processMethodInvocations(CtMethod<?> ctMethod, MethodNode sourceMethodNode, Map<String, MethodNode> methodNodeMap, String sourceKey) {
        boolean isTargetMethod = sourceKey.contains("ProductActivitiesFetcher.build") || sourceKey.contains("testMethod");
        
        ctMethod.getElements(new TypeFilter<>(CtInvocation.class)).forEach(invocation -> {
            try {
                CtExecutableReference<?> executable = invocation.getExecutable();
                if (executable == null) {
                    log.warn("Null executable reference in method invocation for method: {}", sourceKey);
                    return;
                }

                String targetKey = buildMethodKey(executable);
                if (targetKey != null && !targetKey.equals(sourceKey)) {
                    addMethodInvocation(sourceMethodNode, targetKey, methodNodeMap);
                    
                    if (isTargetMethod) {
                        log.info("Found method invocation: {} -> {}", sourceKey, targetKey);
                    }
                }
                
                // 特别处理 thenApply 等方法的参数，检查其中是否包含 lambda 表达式
                // 除了检查targetKey，还要检查方法名本身
                String methodName = executable.getSimpleName();
                if ((targetKey != null && targetKey.contains("thenApply")) || 
                    "thenApply".equals(methodName)) {
                    if (isTargetMethod) {
                        log.info("Processing thenApply method call - targetKey: {}, methodName: {}", targetKey, methodName);
                    }
                    processMethodInvocationArguments(invocation, sourceMethodNode, methodNodeMap, sourceKey);
                }
                
            } catch (Exception e) {
                log.warn("Error processing method invocation in {}: {}", sourceKey, e.getMessage());
            }
        });
    }

    /**
     * 处理方法调用参数中的 lambda 表达式
     */
    private void processMethodInvocationArguments(CtInvocation<?> invocation, MethodNode sourceMethodNode, Map<String, MethodNode> methodNodeMap, String sourceKey) {
        boolean isTargetMethod = sourceKey.contains("ProductActivitiesFetcher.build") || sourceKey.contains("testMethod");
        
        try {
            if (isTargetMethod) {
                log.info("🔍 Processing arguments for thenApply call in method: {}", sourceKey);
                log.info("Invocation: {}", invocation.toString());
                log.info("Arguments count: {}", invocation.getArguments().size());
            }
            
            // 检查方法调用的每个参数
            for (int i = 0; i < invocation.getArguments().size(); i++) {
                try {
                    CtExpression<?> argument = invocation.getArguments().get(i);
                    
                    if (isTargetMethod) {
                        log.info("Argument {}: type={}, content={}", i, argument.getClass().getSimpleName(), argument.toString());
                    }
                    
                    // 查找参数中的所有方法调用
                    java.util.List<CtInvocation<?>> argumentInvocations = argument.getElements(new TypeFilter<>(CtInvocation.class));
                    if (isTargetMethod && !argumentInvocations.isEmpty()) {
                        log.info("Found {} method invocations in argument {}", argumentInvocations.size(), i);
                    }
                    
                    for (CtInvocation<?> argInvocation : argumentInvocations) {
                        CtExecutableReference<?> executable = argInvocation.getExecutable();
                        if (isTargetMethod) {
                            log.info("Processing argInvocation: {}, executable: {}", argInvocation, executable);
                        }
                        
                        if (executable != null) {
                            String targetKey = buildMethodKeyWithContext(executable, sourceKey, methodNodeMap);
                            if (isTargetMethod) {
                                log.info("Built targetKey: {} from executable: {}", targetKey, executable.getSignature());
                            }
                            
                            if (targetKey != null && !targetKey.equals(sourceKey)) {
                                addMethodInvocation(sourceMethodNode, targetKey, methodNodeMap);
                                if (isTargetMethod) {
                                    log.info("✅ Found method call in lambda argument: {} -> {}", sourceKey, targetKey);
                                }
                            } else if (isTargetMethod) {
                                log.warn("Skipped targetKey: {} (null={}, equals={})", targetKey, (targetKey == null), targetKey != null ? targetKey.equals(sourceKey) : false);
                            }
                        } else if (isTargetMethod) {
                            log.warn("Executable is null for argInvocation: {}", argInvocation);
                        }
                    }
                    
                    // 特别处理方法引用参数
                    java.util.List<CtExecutableReferenceExpression<?,?>> methodRefs = argument.getElements(new TypeFilter<>(CtExecutableReferenceExpression.class));
                    if (isTargetMethod && !methodRefs.isEmpty()) {
                        log.info("Found {} method references in argument {}", methodRefs.size(), i);
                    }
                    
                    for (CtExecutableReferenceExpression<?,?> methodRef : methodRefs) {
                        CtExecutableReference<?> executable = methodRef.getExecutable();
                        if (executable != null) {
                            String targetKey = buildMethodKey(executable);
                            if (targetKey != null && !targetKey.equals(sourceKey)) {
                                addMethodInvocation(sourceMethodNode, targetKey, methodNodeMap);
                                if (isTargetMethod) {
                                    log.info("✅ Found method reference in lambda argument: {} -> {}", sourceKey, targetKey);
                                }
                            }
                        }
                    }
                    
                } catch (Exception e) {
                    log.warn("Error processing argument {} in method {}: {}", i, sourceKey, e.getMessage());
                }
            }
            
        } catch (Exception e) {
            log.warn("Error processing method invocation arguments in {}: {}", sourceKey, e.getMessage());
        }
    }
    
    /**
     * 处理构造函数调用
     */
    private void processConstructorCalls(CtMethod<?> ctMethod, MethodNode sourceMethodNode, Map<String, MethodNode> methodNodeMap, String sourceKey) {
        ctMethod.getElements(new TypeFilter<>(CtConstructorCall.class)).forEach(constructorCall -> {
            try {
                CtExecutableReference<?> executable = constructorCall.getExecutable();
                if (executable == null) {
                    return;
                }

                String targetKey = buildMethodKey(executable);
                if (targetKey != null) {
                    addMethodInvocation(sourceMethodNode, targetKey, methodNodeMap);
                    log.debug("Found constructor call: {} -> {}", sourceKey, targetKey);
                }
            } catch (Exception e) {
                log.warn("Error processing constructor call in {}: {}", sourceKey, e.getMessage());
            }
        });
    }

    /**
     * 处理Lambda表达式中的方法调用
     */
    private void processLambdaExpressions(CtMethod<?> ctMethod, MethodNode sourceMethodNode, Map<String, MethodNode> methodNodeMap, String sourceKey) {
        try {
            // 先检查方法中是否有lambda表达式
            java.util.List<CtLambda<?>> lambdas = ctMethod.getElements(new TypeFilter<>(CtLambda.class));

            // 为ProductActivitiesFetcher.build方法添加强制调试
            boolean isTargetMethod = sourceKey.contains("ProductActivitiesFetcher.build") || sourceKey.contains("testMethod");

            if (isTargetMethod) {
                log.info("=== Lambda Analysis for method: {} ===", sourceKey);
                log.info("Method signature check: contains ProductActivitiesFetcher.build = {}", sourceKey.contains("ProductActivitiesFetcher.build"));
                log.info("Method signature check: contains testMethod = {}", sourceKey.contains("testMethod"));
                log.info("Found {} lambda expressions", lambdas.size());
            }

            // 如果这是ProductActivitiesFetcher.build但没有找到lambda，强制输出调试信息
            if (sourceKey.contains("ProductActivitiesFetcher.build") && lambdas.size() == 0) {
                log.error("❌ ProductActivitiesFetcher.build method found but NO lambda expressions detected!");
                log.error("Full method key: {}", sourceKey);
                log.error("Method body: {}", ctMethod.getBody());

                // 尝试更深入的分析
                analyzeLambdaDetectionFailure(ctMethod, sourceKey);
            }

            lambdas.forEach(lambda -> {
                try {
                    // 检查Lambda体是否为空
                    if (lambda == null || lambda.getBody() == null) {
                        if (isTargetMethod) {
                            log.error("Lambda or lambda body is null for method: {}", sourceKey);
                        }
                        return;
                    }

                    // 添加详细调试信息  
                    if (isTargetMethod) {
                        log.info("🔍 Processing lambda in method {}: lambda={}", sourceKey, lambda);
                        log.info("Lambda body type: {}", lambda.getBody().getClass().getSimpleName());
                        log.info("Lambda body content: {}", lambda.getBody().toString());
                        log.info("Lambda parameters: {}", lambda.getParameters());
                        log.info("Lambda expression: {}", lambda.getExpression());
                        log.info("Lambda type: {}", lambda.getType());
                        
                        // 强制打印出 lambda 表达式的所有属性
                        log.info("Lambda toString: {}", lambda.toString());
                        log.info("Lambda parent: {}", lambda.getParent());
                    }

                    // 多种方式尝试提取lambda中的方法调用
                    processLambdaBody(lambda.getBody(), sourceMethodNode, methodNodeMap, sourceKey);

                } catch (Exception e) {
                    String errorMsg = e.getMessage() != null ? e.getMessage() : e.getClass().getSimpleName();
                    log.error("Error processing lambda expression in {}: {}", sourceKey, errorMsg, e);
                }
            });
        } catch (Exception e) {
            String errorMsg = e.getMessage() != null ? e.getMessage() : e.getClass().getSimpleName();
            log.warn("Error getting lambda elements from method {}: {}", sourceKey, errorMsg);
        }
    }

    /**
     * 处理Lambda体中的方法调用
     */
    private void processLambdaBody(CtElement body, MethodNode sourceMethodNode, Map<String, MethodNode> methodNodeMap, String sourceKey) {
        boolean isTargetMethod = sourceKey.contains("testMethod") || sourceKey.contains("ProductActivitiesFetcher.build");
        
        try {
            if (isTargetMethod) {
                log.info("🔍 Processing lambda body: type={}, content={}", body.getClass().getSimpleName(), body.toString());
            }
            
            // 方法1: 使用getElements搜索所有子元素中的调用
            java.util.List<CtInvocation<?>> invocations = body.getElements(new TypeFilter<>(CtInvocation.class));
            if (isTargetMethod) {
                log.info("Lambda body contains {} invocations", invocations.size());
            }
            
            invocations.forEach(invocation -> {
                try {
                    if (isTargetMethod) {
                        log.info("  Processing lambda invocation: {}", invocation.toString());
                        log.info("    Invocation target: {}", invocation.getTarget());
                        log.info("    Invocation executable: {}", invocation.getExecutable());
                        log.info("    Arguments: {}", invocation.getArguments());
                    }
                    
                    CtExecutableReference<?> executable = invocation.getExecutable();
                    if (executable != null) {
                        String targetKey = buildMethodKey(executable);
                        if (targetKey != null && !targetKey.equals(sourceKey)) {
                            addMethodInvocation(sourceMethodNode, targetKey, methodNodeMap);
                            if (isTargetMethod) {
                                log.info("✅ Found lambda invocation (method 1): {} -> {}", sourceKey, targetKey);
                            }
                        }
                    }
                } catch (Exception e) {
                    String errorMsg = e.getMessage() != null ? e.getMessage() : e.getClass().getSimpleName();
                    log.warn("Error processing lambda invocation in {}: {}", sourceKey, errorMsg);
                }
            });

            // 方法2: 如果lambda体本身就是方法调用
            if (body instanceof CtInvocation) {
                CtInvocation<?> invocation = (CtInvocation<?>) body;
                try {
                    if (isTargetMethod) {
                        log.info("Lambda body is direct invocation: {}", invocation);
                    }
                    
                    CtExecutableReference<?> executable = invocation.getExecutable();
                    if (executable != null) {
                        String targetKey = buildMethodKey(executable);
                        if (targetKey != null && !targetKey.equals(sourceKey)) {
                            addMethodInvocation(sourceMethodNode, targetKey, methodNodeMap);
                            if (isTargetMethod) {
                                log.info("✅ Found direct lambda invocation (method 2): {} -> {}", sourceKey, targetKey);
                            }
                        }
                    }
                } catch (Exception e) {
                    String errorMsg = e.getMessage() != null ? e.getMessage() : e.getClass().getSimpleName();
                    log.warn("Error processing direct lambda invocation in {}: {}", sourceKey, errorMsg);
                }
            }
            
            // 方法3: 处理复杂的 lambda 表达式（如链式调用）
            processComplexLambdaExpressions(body, sourceMethodNode, methodNodeMap, sourceKey);

        } catch (Exception e) {
            String errorMsg = e.getMessage() != null ? e.getMessage() : e.getClass().getSimpleName();
            log.warn("Error processing lambda body in {}: {}", sourceKey, errorMsg);
        }
    }

    /**
     * 处理复杂的 lambda 表达式（如链式调用、方法引用等）
     */
    private void processComplexLambdaExpressions(CtElement body, MethodNode sourceMethodNode, Map<String, MethodNode> methodNodeMap, String sourceKey) {
        boolean isTargetMethod = sourceKey.contains("testMethod") || sourceKey.contains("ProductActivitiesFetcher.build");
        
        try {
            // 查找所有类型的表达式
            java.util.List<CtExpression<?>> expressions = body.getElements(new TypeFilter<>(CtExpression.class));
            
            if (isTargetMethod && !expressions.isEmpty()) {
                log.info("Found {} expressions in lambda body", expressions.size());
            }
            
            for (CtExpression<?> expr : expressions) {
                try {
                    if (isTargetMethod) {
                        log.info("  Expression: type={}, content={}", expr.getClass().getSimpleName(), expr.toString());
                    }
                    
                    // 特别处理方法引用表达式
                    if (expr instanceof CtExecutableReferenceExpression) {
                        CtExecutableReferenceExpression<?,?> methodRef = (CtExecutableReferenceExpression<?,?>) expr;
                        CtExecutableReference<?> executable = methodRef.getExecutable();
                        if (executable != null) {
                            String targetKey = buildMethodKey(executable);
                            if (targetKey != null && !targetKey.equals(sourceKey)) {
                                addMethodInvocation(sourceMethodNode, targetKey, methodNodeMap);
                                if (isTargetMethod) {
                                    log.info("✅ Found method reference in lambda: {} -> {}", sourceKey, targetKey);
                                }
                            }
                        }
                    }
                    
                    // 处理字段访问表达式（可能包含方法调用）
                    if (expr instanceof CtFieldAccess || expr instanceof CtVariableAccess) {
                        // 检查是否有嵌套的方法调用
                        java.util.List<CtInvocation<?>> nestedInvocations = expr.getElements(new TypeFilter<>(CtInvocation.class));
                        for (CtInvocation<?> nestedInvocation : nestedInvocations) {
                            CtExecutableReference<?> executable = nestedInvocation.getExecutable();
                            if (executable != null) {
                                String targetKey = buildMethodKey(executable);
                                if (targetKey != null && !targetKey.equals(sourceKey)) {
                                    addMethodInvocation(sourceMethodNode, targetKey, methodNodeMap);
                                    if (isTargetMethod) {
                                        log.info("✅ Found nested invocation in lambda expression: {} -> {}", sourceKey, targetKey);
                                    }
                                }
                            }
                        }
                    }
                    
                } catch (Exception e) {
                    if (isTargetMethod) {
                        log.warn("Error processing expression {} in lambda: {}", expr, e.getMessage());
                    }
                }
            }
            
            // 特别处理方法引用（:: 语法）
            java.util.List<CtExecutableReferenceExpression<?,?>> methodRefs = body.getElements(new TypeFilter<>(CtExecutableReferenceExpression.class));
            if (isTargetMethod && !methodRefs.isEmpty()) {
                log.info("Found {} method references in lambda body", methodRefs.size());
            }
            
            for (CtExecutableReferenceExpression<?,?> methodRef : methodRefs) {
                try {
                    CtExecutableReference<?> executable = methodRef.getExecutable();
                    if (executable != null) {
                        String targetKey = buildMethodKey(executable);
                        if (targetKey != null && !targetKey.equals(sourceKey)) {
                            addMethodInvocation(sourceMethodNode, targetKey, methodNodeMap);
                            if (isTargetMethod) {
                                log.info("✅ Found method reference (:: syntax) in lambda: {} -> {}", sourceKey, targetKey);
                            }
                        }
                    }
                } catch (Exception e) {
                    if (isTargetMethod) {
                        log.warn("Error processing method reference {} in lambda: {}", methodRef, e.getMessage());
                    }
                }
            }
            
        } catch (Exception e) {
            log.warn("Error in processComplexLambdaExpressions for {}: {}", sourceKey, e.getMessage());
        }
    }
    
    /**
     * 处理方法引用
     */
    private void processMethodReferences(CtMethod<?> ctMethod, MethodNode sourceMethodNode, Map<String, MethodNode> methodNodeMap, String sourceKey) {
        ctMethod.getElements(new TypeFilter<>(CtExecutableReferenceExpression.class)).forEach(methodRef -> {
            try {
                CtExecutableReference<?> executable = methodRef.getExecutable();
                if (executable != null) {
                    String targetKey = buildMethodKey(executable);
                    if (targetKey != null && !targetKey.equals(sourceKey)) {
                        addMethodInvocation(sourceMethodNode, targetKey, methodNodeMap);
                        log.debug("Found method reference: {} -> {}", sourceKey, targetKey);
                    }
                }
            } catch (Exception e) {
                log.warn("Error processing method reference in {}: {}", sourceKey, e.getMessage());
            }
        });
    }

    /**
     * 构建方法唯一标识符（带上下文信息）
     */
    private String buildMethodKeyWithContext(CtExecutableReference<?> executable, String sourceMethodKey, Map<String, MethodNode> methodNodeMap) {
        try {
            boolean isTargetMethod = executable.getSignature() != null && 
                (executable.getSignature().contains("convertActivityMsFromResponse") || 
                 executable.getSignature().startsWith("convertActivityMsFromResponse"));
                 
            CtTypeReference<?> declaringType = executable.getDeclaringType();
            
            if (isTargetMethod) {
                log.info("🔍 buildMethodKeyWithContext for: {}", executable.getSignature());
                log.info("Original declaring type: {}", declaringType);
            }
            
            // 如果 declaringType 为 null，尝试从源方法的上下文推断
            if (declaringType == null && sourceMethodKey != null) {
                if (isTargetMethod) {
                    log.info("Attempting to extract class name from source key: {}", sourceMethodKey);
                }
                String sourceClassName = extractClassNameFromMethodKey(sourceMethodKey);
                if (isTargetMethod) {
                    log.info("Extracted class name: {}", sourceClassName);
                }
                if (sourceClassName != null) {
                    if (isTargetMethod) {
                        log.info("Inferring declaring type from source method: {}", sourceClassName);
                    }
                    String signature = executable.getSignature();
                    if (signature != null) {
                        // 尝试在methodNodeMap中查找匹配的方法
                        String methodName = signature.substring(0, signature.indexOf('('));
                        if (isTargetMethod) {
                            log.info("Looking for method name '{}' in class '{}'", methodName, sourceClassName);
                        }
                        
                        // 查找所有匹配类名和方法名的方法
                        for (String key : methodNodeMap.keySet()) {
                            if (key.startsWith(sourceClassName + ".") && 
                                key.contains(methodName + "(")) {
                                if (isTargetMethod) {
                                    log.info("✅ Found matching method in methodNodeMap: {}", key);
                                }
                                return key;
                            }
                        }
                        
                        // 如果没找到匹配的，使用简单构建的key
                        String result = sourceClassName + "." + signature;
                        if (isTargetMethod) {
                            log.info("✅ Built method key with inferred type (fallback): {}", result);
                        }
                        return result;
                    }
                }
            }
            
            // 如果能够推断出类型，使用原始逻辑
            return buildMethodKey(executable);
            
        } catch (Exception e) {
            log.warn("Error in buildMethodKeyWithContext for {}: {}", executable, e.getMessage());
            return buildMethodKey(executable);  // fallback to original method
        }
    }

    /**
     * 从方法key中提取类名
     */
    private String extractClassNameFromMethodKey(String methodKey) {
        if (methodKey == null) {
            return null;
        }
        
        // 方法key格式：com.example.Class.method(params)
        // 需要找到方法名开始的位置，方法名后面跟着 (
        int openParenIndex = methodKey.indexOf('(');
        if (openParenIndex == -1) {
            return null;
        }
        
        // 从 ( 往前找最后一个点，该点之前就是类名
        String beforeParen = methodKey.substring(0, openParenIndex);
        int lastDotIndex = beforeParen.lastIndexOf('.');
        
        if (lastDotIndex > 0) {
            return beforeParen.substring(0, lastDotIndex);
        }
        
        return null;
    }

    /**
     * 构建方法唯一标识符
     */
    private String buildMethodKey(CtExecutableReference<?> executable) {
        try {
            boolean isTargetMethod = executable.getSignature() != null && 
                (executable.getSignature().contains("convertActivityMsFromResponse") || 
                 executable.getSignature().startsWith("convertActivityMsFromResponse"));
            
            if (isTargetMethod) {
                log.info("🔍 Building method key for: {}", executable.getSignature());
                log.info("Executable toString: {}", executable.toString());
                log.info("Executable type: {}", executable.getClass().getSimpleName());
            }
            
            CtTypeReference<?> declaringType = executable.getDeclaringType();
            if (isTargetMethod) {
                log.info("Declaring type: {}", declaringType);
            }
            
            if (declaringType == null) {
                if (isTargetMethod) {
                    log.error("❌ Declaring type is NULL for executable: {}", executable.getSignature());
                }
                log.debug("Declaring type is null for executable: {}", executable.getSignature());
                return null;
            }

            String typeQualifiedName = declaringType.getQualifiedName();
            String signature = executable.getSignature();
            
            if (isTargetMethod) {
                log.info("Type qualified name: {}", typeQualifiedName);
                log.info("Method signature: {}", signature);
            }

            if (typeQualifiedName == null || signature == null) {
                if (isTargetMethod) {
                    log.error("❌ Null type name or signature: type={}, signature={}", typeQualifiedName, signature);
                }
                log.debug("Null type name or signature: type={}, signature={}", typeQualifiedName, signature);
                return null;
            }

            String result = typeQualifiedName + "." + signature;
            if (isTargetMethod) {
                log.info("✅ Built method key: {}", result);
            }
            
            return result;
        } catch (Exception e) {
            log.warn("Error building method key for executable {}: {}", executable, e.getMessage());
            return null;
        }
    }

    /**
     * 添加方法调用关系
     */
    private void addMethodInvocation(MethodNode sourceMethodNode, String targetKey, Map<String, MethodNode> methodNodeMap) {
        MethodNode invocationMethodNode = methodNodeMap.get(targetKey);
        if (invocationMethodNode != null) {
            // 项目内部调用
            sourceMethodNode.getInvocation().add(invocationMethodNode.getKey());
            invocationMethodNode.getCalled().add(sourceMethodNode.getKey());
            log.debug("Added internal invocation: {} -> {}", sourceMethodNode.getKey(), targetKey);
        } else {
            // 外部调用（JDK、第三方库等）
            sourceMethodNode.getInvocation().add(targetKey);
            log.debug("Added external invocation: {} -> {}", sourceMethodNode.getKey(), targetKey);
        }
    }

    /**
     * 深入分析lambda检测失败的原因
     */
    private void analyzeLambdaDetectionFailure(CtMethod<?> ctMethod, String sourceKey) {
        try {
            log.error("🔍 Deep analysis for lambda detection failure in: {}", sourceKey);

            // 分析所有表达式
            java.util.List<CtExpression<?>> allExpressions = ctMethod.getElements(new TypeFilter<>(CtExpression.class));
            log.error("Total expressions found: {}", allExpressions.size());

            int index = 0;
            for (CtExpression<?> expr : allExpressions) {
                log.error("Expression #{}: type={}, content={}", index++, expr.getClass().getSimpleName(), expr.toString());
                if (expr.toString().contains("thenApply")) {
                    log.error("  ⭐ Found thenApply expression: {}", expr);
                    // 检查这个表达式的子元素
                    java.util.List<CtElement> subElements = expr.getElements(element -> true);
                    for (CtElement subElem : subElements) {
                        log.error("    Sub-element: type={}, content={}", subElem.getClass().getSimpleName(), subElem.toString());
                    }
                }
            }

            // 检查所有类型的代码块
            java.util.List<CtBlock<?>> blocks = ctMethod.getElements(new TypeFilter<>(CtBlock.class));
            log.error("Code blocks found: {}", blocks.size());
            for (CtBlock<?> block : blocks) {
                log.error("Block: {}", block.toString());
            }

            // 检查所有变量访问
            java.util.List<CtVariableAccess<?>> varAccesses = ctMethod.getElements(new TypeFilter<>(CtVariableAccess.class));
            log.error("Variable accesses found: {}", varAccesses.size());
            for (CtVariableAccess<?> varAccess : varAccesses) {
                log.error("Variable access: {}", varAccess.toString());
            }

            // 检查直接在方法内的所有调用
            log.error("All direct invocations in method:");
            java.util.List<CtInvocation<?>> directInvocations = ctMethod.getElements(new TypeFilter<>(CtInvocation.class));
            for (CtInvocation<?> inv : directInvocations) {
                log.error("  Direct invocation: {} (target: {})", inv.toString(), inv.getTarget());
                if (inv.toString().contains("convertActivityMsFromResponse")) {
                    log.error("    🎯 FOUND convertActivityMsFromResponse invocation!");
                    log.error("    Parent: {}", inv.getParent());
                    log.error("    Root parent: {}", findRootParent(inv));
                }
            }

        } catch (Exception e) {
            log.error("Error in deep lambda analysis for {}: {}", sourceKey, e.getMessage(), e);
        }
    }

    /**
     * 找到元素的根父节点
     */
    private CtElement findRootParent(CtElement element) {
        CtElement parent = element.getParent();
        while (parent != null && !(parent instanceof CtMethod)) {
            element = parent;
            parent = element.getParent();
        }
        return element;
    }

}
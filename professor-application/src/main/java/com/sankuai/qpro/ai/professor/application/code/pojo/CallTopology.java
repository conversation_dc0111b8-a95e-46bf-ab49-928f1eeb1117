package com.sankuai.qpro.ai.professor.application.code.pojo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;

import java.io.Serializable;
import java.util.*;

/**
 * CallTopology
 *
 * <AUTHOR>
 * @since 2025/3/5
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class CallTopology implements Serializable {

    private String callName;

    private List<CallTopology> children;

    public Map<String, Set<String>> getAllNode(String basePackage) {
        Map<String, Set<String>> nodes = new HashMap<>(16);
        iterationNode(this, nodes);
        if (StringUtils.isNotBlank(basePackage)) {
            nodes.keySet().removeIf(key -> !key.startsWith(basePackage));
        }
        return nodes;
    }

    private void iterationNode(CallTopology topology, Map<String, Set<String>> nodes) {
        if (topology == null) {
            return;
        }
        Pair<String, String> split = split(topology.getCallName());
        if (split != null && split.getKey() != null && split.getValue() != null) {
            if (!"<init>".equals(split.getValue())) {
                Set<String> methods = new HashSet<>(nodes.getOrDefault(split.getKey(), new HashSet<>()));
                methods.add(split.getValue());
                nodes.put(split.getKey(), methods);
            }
        }

        if (CollectionUtils.isNotEmpty(topology.getChildren())) {
            topology.getChildren().forEach(topologyChild -> {
                iterationNode(topologyChild, nodes);
            });
        }
    }

    private Pair<String, String> split(String callName) {
        // 截取类名和方法名
        if (StringUtils.isBlank(callName)) {
            return Pair.of(null, null);
        }
        int leftParenIndex = callName.indexOf('(');
        String fullMethodName = leftParenIndex > 0 ? callName.substring(0, leftParenIndex) : callName;

        int lastDotIndex = fullMethodName.lastIndexOf('.');
        if (lastDotIndex < 0) {
            return null;
        }
        String className = fullMethodName.substring(0, lastDotIndex);
        String methodName = fullMethodName.substring(lastDotIndex + 1);
        return Pair.of(className, methodName);
    }

}
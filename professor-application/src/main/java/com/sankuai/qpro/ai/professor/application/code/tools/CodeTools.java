package com.sankuai.qpro.ai.professor.application.code.tools;

import com.dianping.lion.client.Lion;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.meituan.mdp.boot.starter.MdpContextUtils;
import com.sankuai.ee.mcode.analyze.api.thrift.dto.ClassInfoDTO;
import com.sankuai.ee.mcode.analyze.api.thrift.dto.MethodDefWithoutInvocationsDTO;
import com.sankuai.ee.mcode.analyze.api.thrift.exception.ThriftApiException;
import com.sankuai.ee.mcode.analyze.api.thrift.response.ProjectClassesResponse;
import com.sankuai.ee.mcode.analyze.api.thrift.service.AnalysisProjectThriftService;
import com.sankuai.qpro.ai.professor.api.response.RemoteResponse;
import com.sankuai.qpro.ai.professor.application.code.TopologyService;
import com.sankuai.qpro.ai.professor.application.code.ast.node.ClassNode;
import com.sankuai.qpro.ai.professor.application.code.ast.node.MethodNode;
import com.sankuai.qpro.ai.professor.application.code.ast.node.Project;
import com.sankuai.qpro.ai.professor.application.code.ast.resolver.AstResolver;
import com.sankuai.qpro.ai.professor.application.code.pojo.CallTopology;
import com.sankuai.qpro.ai.professor.application.code.tools.dto.CallTopologyDTO;
import com.sankuai.qpro.ai.professor.application.code.tools.dto.ClassCodeDTO;
import com.sankuai.qpro.ai.professor.application.code.tools.dto.MethodCodeDTO;
import com.sankuai.qpro.ai.professor.application.utils.CodeBlockUtils;
import com.sankuai.qpro.ai.professor.application.utils.ExpressionMatcherUtils;
import com.sankuai.qpro.ai.professor.application.utils.ProjectMappingUtils;
import lombok.SneakyThrows;
import org.apache.commons.collections.MapUtils;
import org.apache.thrift.TException;
import org.springframework.ai.tool.annotation.Tool;
import org.springframework.ai.tool.annotation.ToolParam;
import org.springframework.util.StringUtils;
import org.springframework.web.reactive.function.client.WebClient;

import java.time.Duration;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * CodeTools
 *
 * <AUTHOR>
 * @since 2025/8/4
 */
public class CodeTools {

    private final TopologyService topologyService;
    private final AnalysisProjectThriftService analysisProjectThriftService;
    private static final String BASEURL = "http://10.98.14.201:8080/ai/probe/chat/api/code";
    private final WebClient webClient = WebClient.builder()
            .codecs(configurer -> configurer.defaultCodecs().maxInMemorySize(20 * 1024 * 1024))
            .build();
    private final ObjectMapper objectMapper = new ObjectMapper();


    public CodeTools(TopologyService topologyService, AnalysisProjectThriftService analysisProjectThriftService) {
        this.topologyService = topologyService;
        this.analysisProjectThriftService = analysisProjectThriftService;
    }

    @SneakyThrows
    @Tool(name = "find_classes", description = """
            方法说明：在指定Git仓库中根据表达式模糊检索类定义，支持包名、类名的模糊匹配
            
            参数要求：
            - gitSsh: 必须是有效的SSH格式地址，如ssh://*******************/group/project.git
            - expression: 检索表达式，支持%通配符进行模糊匹配，如%Service%匹配包含Service的类名
            - limit: 正整数，限制返回结果数量，建议设置合理值避免结果过多
            
            返回值：
            匹配的类信息列表，每项包含类的完整限定名、文件路径等详细信息
            
            示例：
            findClasses("ssh://*******************/project/demo.git", "%Controller%", 10)
            查找demo项目中所有包含Controller的类，最多返回10个结果
            
            """)
    public List<String> findClasses(@ToolParam(description = "Git仓库SSH地址，格式如ssh://*******************/group/project.git") String gitSsh,
                                    @ToolParam(description = "检索的条件表达式，类似SQL中的like，百分号%表示零个、一个或多个字符") String expression,
                                    @ToolParam(description = "限制返回的结果集中的行数") int limit) {
        if (StringUtils.hasText(gitSsh) && StringUtils.hasText(expression)) {
            boolean isOpenRPC = Lion.getBoolean(MdpContextUtils.getAppKey(), "isOpenRPC", false);
            ProjectClassesResponse projectClassesResponse = null;
            if(isOpenRPC){
                projectClassesResponse = analysisProjectThriftService.listJavaProjectClasses(gitSsh, null);
            }else{
                String url = BASEURL + "/project/classes?repo=" + gitSsh;
                String jsonResponse = getHttpResponse(url);
                if (jsonResponse == null || jsonResponse.startsWith("HTTP请求失败")) {
                    return List.of();
                }
                JsonNode root = objectMapper.readTree(jsonResponse);
                JsonNode dataNode = root.get("data");
                projectClassesResponse = objectMapper.convertValue(dataNode,new TypeReference<ProjectClassesResponse>() {});

            }
            Map<String, ClassInfoDTO> filteredClasses = new HashMap<>();
            projectClassesResponse.getClasses().forEach((k, v)-> {
                if(ExpressionMatcherUtils.matcher(k, expression)){
                    filteredClasses.put(k, v);
                }
            });
            return filteredClasses.keySet().stream().limit(limit).toList();
        }
        return List.of();
    }


    @SneakyThrows
    @Tool(name = "find_methods", description = """
            方法说明：在指定Git仓库中根据表达式模糊检索方法定义，支持方法名、类名的模糊匹配
            
            参数要求：
            - gitSsh: 必须是有效的SSH格式地址，如ssh://*******************/group/project.git
            - expression: 检索表达式，支持%通配符进行模糊匹配，如%execute%匹配包含execute的方法名
            - limit: 正整数，限制返回结果数量，建议设置合理值避免结果过多
            
            返回值：
            匹配的方法信息列表，每项包含方法签名、所属类名、文件路径和行号等详细信息
            
            示例：
            findMethods("ssh://*******************/project/demo.git", "%process%", 20)
            查找demo项目中所有包含process的方法，最多返回20个结果
            
            """)
    public List<String> findMethods(
            @ToolParam(description = "Git仓库SSH地址，格式如ssh://*******************/group/project.git") String gitSsh,
            @ToolParam(description = "检索的条件表达式，类似SQL中的like，百分号%表示零个、一个或多个字符") String expression,
            @ToolParam(description = "限制返回的结果集中的行数") int limit) {
        if (StringUtils.hasText(gitSsh) && StringUtils.hasText(expression)) {
            boolean isOpenRPC = Lion.getBoolean(MdpContextUtils.getAppKey(), "isOpenRPC", false);
            Map<String, MethodDefWithoutInvocationsDTO> stringMethodDef = new HashMap<>();
            if(isOpenRPC){
                stringMethodDef = analysisProjectThriftService.listJavaProjectMethodsWithoutInvocations(gitSsh, null);
            }else{
                String url = BASEURL + "/project/methods?repo=" + gitSsh;
                String jsonResponse = getHttpResponse(url);
                if (jsonResponse == null || jsonResponse.startsWith("HTTP请求失败")) {
                    return List.of();
                }
                JsonNode root = objectMapper.readTree(jsonResponse);
                JsonNode dataNode = root.get("data");
                stringMethodDef = objectMapper.convertValue(dataNode,new TypeReference<Map<String, MethodDefWithoutInvocationsDTO>>() {});

            }
            Map<String, MethodDefWithoutInvocationsDTO> filteredMethods = new HashMap<>();
            stringMethodDef.forEach((k, v) -> {
                if (ExpressionMatcherUtils.matcher(k, expression)) {
                    filteredMethods.put(k, v);
                }
            });
            return filteredMethods.keySet().stream().limit(limit).toList();
        }
        return List.of();
    }

    @Tool(name = "get_class_code", description = """
            方法说明：在指定Git仓库中获取指定Java类。适用于代码分析、理解类结构、查看类实现/方法列表等场景。
            
            参数要求：
            - gitSsh: Git仓库的SSH地址，用于定位项目
            - classKey: Java类的完整限定名（包名+类名）
            
            返回值：
            - 成功：返回类的完整信息（类名、方法列表、行数、代码块）
            - 失败：返回空（类不存在或项目未解析）
            
            示例：
            gitSsh: ssh://*******************/vc/dzviewscene-dealshelf-home.git
            classKey: com.sankuai.dzviewscene.product.dealstruct.options.skuList.bar.barSkuCreate.AbstractBarSkuCreator
            """)
    public ClassCodeDTO getClassCode(@ToolParam(description = "Git仓库SSH地址，格式如ssh://*******************/group/project.git") String gitSsh,
                                     @ToolParam(description = "Java类的完整限定名，包含包名和类名，如com.example.MyClass") String classKey) {
        if (StringUtils.hasText(gitSsh) && StringUtils.hasText(classKey)) {
            Project project = AstResolver.getInstance().getCache().get(ProjectMappingUtils.getPathByGit(gitSsh));
            if (Objects.nonNull(project)) {
                ClassNode classNode = project.getClassNodeMap().get(classKey);
                if (Objects.nonNull(classNode)) {
                    return new ClassCodeDTO(classNode.getKey(),classNode.getMethods().stream().toList(),classNode.getSourceEndLine()-classNode.getSourceStartLine(),classNode.getSourceCode());
                }
            }
        }
        return null;
    }

    @Tool(name = "get_class_code_line", description = """
            方法说明：在指定Git仓库中获取指定Java类的指定行的代码。适用于代码分析、理解类结构、查看类实现/方法列表等场景。
            
            参数要求：
            - gitSsh: Git仓库的SSH地址，用于定位项目
            - classKey: Java类的完整限定名（包名+类名）
            - startLine: 代码块的起始行
            - endLine: 代码块的结束行
            
            返回值：
            - 成功：返回类的完整源代码字符串
            - 失败：返回空字符串（类不存在或项目未解析）
            
            示例：
            gitSsh: ssh://*******************/vc/dzviewscene-dealshelf-home.git
            classKey: com.sankuai.dzviewscene.product.dealstruct.options.skuList.bar.barSkuCreate.AbstractBarSkuCreator
            """)
    public String getClassCodeBlock(@ToolParam(description = "Git仓库SSH地址，格式如ssh://*******************/group/project.git") String gitSsh,
                                          @ToolParam(description = "Java类的完整限定名，包含包名和类名，如com.example.MyClass") String classKey,
                                          @ToolParam(description = "代码块的起始行") Integer startLine,
                                          @ToolParam(description = "代码块的结束行") Integer endLine

    ) {
        if (StringUtils.hasText(gitSsh) && StringUtils.hasText(classKey)) {
            Project project = AstResolver.getInstance().getCache().get(ProjectMappingUtils.getPathByGit(gitSsh));
            if (Objects.nonNull(project)) {
                ClassNode classNode = project.getClassNodeMap().get(classKey);
                if (Objects.nonNull(classNode)) {
                    return CodeBlockUtils.getCodeBlock(classNode.getSourceCode(), startLine, endLine);
                }
            }
        }
        return "";
    }

    @Tool(name = "get_method_code", description = """
            方法说明：在指定Git仓库中获取指定Java方法的完整源代码实现。适用于分析方法逻辑、理解业务实现、代码审查等场景
            
            参数要求：
            - gitSsh: Git仓库的SSH地址，用于定位项目
            - methodKey: 方法的完整唯一标识，格式为：完整类名.方法名(参数类型列表)
            
            返回值：
            - 成功：返回方法的信息（包含方法签名、源码行数、源码字符串）
            - 失败：返回空（方法不存在或项目未解析）
            
            示例：
            gitSsh: ssh://*******************/vc/dzviewscene-dealshelf-home.git
            methodKey: com.sankuai.dzviewscene.product.dealstruct.options.skuList.bar.barSkuCreate.AbstractBarSkuCreator.buildSubtitle(java.lang.String,java.lang.String,java.util.List,boolean)
            
            注意：methodKey必须包含完整的参数类型信息以准确定位重载方法
            """)
    public MethodCodeDTO getMethodCode(@ToolParam(description = "Git仓库SSH地址，格式如ssh://*******************/group/project.git") String gitSsh,
                                            @ToolParam(description = "方法的完整签名，格式为：完整类名.方法名(参数类型)，如com.example.MyClass.myMethod(java.lang.String,int)") String methodKey
    ) {
        if (StringUtils.hasText(gitSsh) && StringUtils.hasText(methodKey)) {
            Project project = AstResolver.getInstance().getCache().get(ProjectMappingUtils.getPathByGit(gitSsh));
            if (Objects.nonNull(project)) {
                MethodNode methodNode = project.getMethodNodeMap().get(methodKey);
                if (Objects.nonNull(methodNode)) {
                    return new MethodCodeDTO(methodNode.getKey(),methodNode.getSourceEndLine()-methodNode.getSourceStartLine(), methodNode.getSourceCode());
                }
            }
        }
        return null;
    }
    @Tool(name = "get_call_topology_by_depth", description = """
            方法说明：获取对应仓库指定方法节点下的调用关系图谱，适用于分析方法逻辑、理解业务实现、代码审查等场景。
            
            参数要求：
            - gitSsh: Git仓库的SSH地址，用于定位项目
            - methodKey: 方法的完整唯一标识，格式为：完整类名.方法名(参数类型列表)
            - depth: 调用深度
            
            返回值：
            - 成功：返回仓库根方法节点下depth层之内的调用链路里所有的完整类名及其方法名（参数类型列表）
            - 失败：返回空（方法不存在或项目无调用链路）
            
            示例：
            gitSsh: ssh://*******************/vc/dzviewscene-dealshelf-home.git
            methodKey: com.sankuai.dzviewscene.product.dealstruct.options.skuList.bar.barSkuCreate.AbstractBarSkuCreator.buildSubtitle(java.lang.String,java.lang.String,java.util.List,boolean)
            depth: 3
            
            注意：methodKey必须包含完整的参数类型信息以准确定位重载方法
            """)
    public CallTopologyDTO getCallTopologyByDepth(@ToolParam(description = "Git仓库SSH地址，格式如ssh://*******************/group/project.git") String gitSsh,
            @ToolParam(description = "方法的完整签名，格式为：完整类名.方法名(参数类型)，如com.example.MyClass.myMethod(java.lang.String,int)") String methodKey,
            @ToolParam(description = "调用深度") Integer depth) {
        if (StringUtils.hasText(gitSsh) && StringUtils.hasText(methodKey)) {
            CallTopology callTopology = topologyService.queryCallTopologyWithDepth(gitSsh, methodKey, depth);
            CallTopologyDTO res = new CallTopologyDTO(callTopology.getCallName(),callTopology.getChildren());
            if (Objects.nonNull(res)) {
                return res;
            }
        }
        return null;
    }

    public String getHttpResponse(String url) {
        try {
            String response = webClient.get()
                    .uri(url)
                    .retrieve()
                    .bodyToMono(String.class)
                    .timeout(Duration.ofSeconds(30))
                    .block();
            return response;

        } catch (Exception e) {
            return "HTTP请求失败: " + e.getMessage();
        }
    }

}
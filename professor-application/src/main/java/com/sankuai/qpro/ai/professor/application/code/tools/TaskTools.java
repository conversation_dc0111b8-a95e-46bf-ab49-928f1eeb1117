package com.sankuai.qpro.ai.professor.application.code.tools;

import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.tool.annotation.Tool;
import org.springframework.ai.tool.annotation.ToolParam;

/**
 * TaskTools
 *
 * <AUTHOR>
 * @since 2025/8/4
 */
@Slf4j
public class TaskTools {

    @Tool(name = "handoff_to_task_executor", description = "Handoff to task executor agent to do.")
    public void handoffTaskExecutor(@ToolParam(description = "task title") String taskTitle) {
        log.info("Handoff to task executor: {}", taskTitle);
    }

}
package com.sankuai.qpro.ai.professor.application.code.tools.dto;

import com.sankuai.qpro.ai.professor.application.code.pojo.CallTopology;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 调用拓扑代码
 *
 * <AUTHOR>
 * @since 2025/8/10
 */

@Data
@AllArgsConstructor
@NoArgsConstructor
public class CallTopologyDTO {
    private String callName;

    private List<CallTopology> children;

}

package com.sankuai.qpro.ai.professor.application.code.tools.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * class 代码
 *
 * <AUTHOR>
 * @since 2025/8/8
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ClassCodeDTO implements Serializable {

    /**
     * 类签名
     */
    private String classKey;

    /**
     * 方法列表
     */
    private List<String> methods;

    /**
     * 代码总行数
     */
    private Integer totalLine;

    /**
     * 类的源码
     */
    private String sourceCode;

}
package com.sankuai.qpro.ai.professor.application.code.tools.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Map;

/**
 * class 代码
 *
 * <AUTHOR>
 * @since 2025/8/8
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ClassCodeLineDTO implements Serializable {

    /**
     * 代码块及其对应的行
     */
    private Map<String, Integer> codeBlock;

}
package com.sankuai.qpro.ai.professor.application.code.tools.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * method 代码
 *
 * <AUTHOR>
 * @since 2025/8/8
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class MethodCodeDTO implements Serializable {

    /**
     * 方法签名
     */
    private String methodKey;

    /**
     * 方法的代码块行数
     */
    private Integer methodCodeBlockLine;

    /**
     * 方法的代码块
     */
    private String codeBlock;

}
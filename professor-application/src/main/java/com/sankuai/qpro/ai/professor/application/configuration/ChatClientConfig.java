package com.sankuai.qpro.ai.professor.application.configuration;

import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.chat.client.advisor.SimpleLoggerAdvisor;
import org.springframework.ai.openai.OpenAiChatModel;
import org.springframework.ai.openai.OpenAiChatOptions;
import org.springframework.ai.openai.api.OpenAiApi;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * @author: wuwenqiang
 * @create: 2025-05-23
 * @description:
 */
@Configuration
public class ChatClientConfig {

    @Bean("gpt4Dot1Client")
    public ChatClient Gpt4Dot1Client(OpenAiChatModel openAiChatModel) {
        OpenAiChatModel gpt4Dot1Model = openAiChatModel.mutate()
                .defaultOptions(OpenAiChatOptions.builder()
                        .model(OpenAiApi.ChatModel.GPT_4_1)
                        .temperature(0.4d)
                        .topP(1d)
                        .frequencyPenalty(0.1d)
                        .presencePenalty(0.1d)
                        .maxTokens(30000)
                        .build()).build();
        ChatClient.Builder builder = ChatClient.builder(gpt4Dot1Model);
        return builder
                .defaultAdvisors(new SimpleLoggerAdvisor())
                .build();
    }
}

package com.sankuai.qpro.ai.professor.application.configuration;

import com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy;
import com.sankuai.dxenterprise.open.gateway.service.dx.api.DxService;
import com.sankuai.xm.openplatform.api.service.open.XmOpenMessageServiceI;
import com.sankuai.xm.openplatform.api.service.open.XmOpenUserServiceI;
import com.sankuai.xm.openplatform.auth.service.XmAuthServiceI;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @date 2024/12/4
 */
@Configuration
public class DxConfiguration {
    @Bean
    public ThriftClientProxy xmAuthService(){
        ThriftClientProxy thriftClientProxy = new ThriftClientProxy();
        thriftClientProxy.setServiceInterface(XmAuthServiceI.class);
        thriftClientProxy.setRemoteAppkey("com.sankuai.dxenterprise.open.gateway");
        thriftClientProxy.setTimeout(1000);
        thriftClientProxy.setFilterByServiceName(true);
        return thriftClientProxy;
    }

    //大象开放平台消息服务
    @Bean
    public ThriftClientProxy xmOpenMsgService(){
        ThriftClientProxy thriftClientProxy = new ThriftClientProxy();
        thriftClientProxy.setServiceInterface(XmOpenMessageServiceI.class);
        thriftClientProxy.setRemoteAppkey("com.sankuai.dxenterprise.open.gateway");
        thriftClientProxy.setTimeout(1000);
        thriftClientProxy.setFilterByServiceName(true);
        return thriftClientProxy;
    }

    @Bean
    public ThriftClientProxy xmOpenUserService() {
        ThriftClientProxy thriftClientProxy = new ThriftClientProxy();
        thriftClientProxy.setServiceInterface(XmOpenUserServiceI.class);
        thriftClientProxy.setRemoteAppkey("com.sankuai.dxenterprise.open.gateway");
        thriftClientProxy.setTimeout(1000);
        thriftClientProxy.setFilterByServiceName(true);
        return thriftClientProxy;
    }


    @Bean
    public DxService dxService() {
        return new DxService();
    }
}

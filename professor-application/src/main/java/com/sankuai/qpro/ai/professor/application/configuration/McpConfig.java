package com.sankuai.qpro.ai.professor.application.configuration;

import com.sankuai.qpro.ai.professor.application.service.codegenerate.tools.ExtractKmDocMcpTool;
import com.sankuai.qpro.ai.professor.application.service.codegenerate.tools.ReadKmImageTool;
import org.springframework.ai.tool.ToolCallbackProvider;
import org.springframework.ai.tool.method.MethodToolCallbackProvider;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * @author: wu<PERSON><PERSON><PERSON>
 * @create: 2025-06-11
 * @description:
 */
@Configuration
public class McpConfig {

    @Bean
    public ToolCallbackProvider kmImageTool(ExtractKmDocMcpTool extractKmDocMcpTool) {
        return MethodToolCallbackProvider.builder().toolObjects(extractKmDocMcpTool).build();
    }
}
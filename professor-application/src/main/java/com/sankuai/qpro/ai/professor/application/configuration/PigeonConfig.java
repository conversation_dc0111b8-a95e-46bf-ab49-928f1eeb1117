package com.sankuai.qpro.ai.professor.application.configuration;

import com.dianping.general.category.service.GeneralCategoryService;
import com.meituan.mdp.boot.starter.pigeon.annotation.MdpPigeonClient;
import com.sankuai.dzviewscene.unifiedshelf.operator.api.service.UnifiedShelfOperatorMService;
import org.springframework.context.annotation.Configuration;
import com.dianping.poi.cateproperty.api.service.POICategoryService;

/**
 * @author: wuwenqiang
 * @create: 2024-12-12
 * @description:
 */
@Configuration
public class PigeonConfig {

    @MdpPigeonClient(timeout = 3000, url = "com.sankuai.dzviewscene.unifiedshelf.operator.api.service.UnifiedShelfOperatorMService")
    private UnifiedShelfOperatorMService unifiedShelfOperatorMService;

    @MdpPigeonClient(timeout = 500, testTimeout = 5000, remoteAppKey = "tuangou-category-service", url = "com.dianping.general.category.service.GeneralCategoryService")
    private GeneralCategoryService generalCategoryService;

    @MdpPigeonClient(timeout = 1000, testTimeout = 5000, url = "http://service.dianping.com/poi-cateproperty-service/poiCategoryService_1.0.0")
    private POICategoryService poiCategoryService;
}

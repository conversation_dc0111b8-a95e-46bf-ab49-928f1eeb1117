package com.sankuai.qpro.ai.professor.application.configuration;

import com.meituan.mdp.boot.starter.thrift.annotation.MdpThriftClient;
import com.sankuai.ee.mcode.analyze.api.thrift.service.AnalysisProjectThriftService;
import com.sankuai.dxenterprise.open.gateway.common.domain.Config;
import com.sankuai.dxenterprise.open.gateway.service.citadel.api.CitadelService;
import com.sankuai.nibscp.cpv.api.remote.service.CpvQueryService;
import com.sankuai.spt.ark.api.remote.service.exp.ArkExpCheckService;
import com.sankuai.spt.ark.api.remote.service.exp.ArkExpInfoQueryService;
import com.sankuai.spt.gray.api.service.ProbeStrategyUnitCheckService;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class ThriftConfig {

    @MdpThriftClient(
            timeout = 2000,
            testTimeout = 5000,
            remoteAppKey = "com.sankuai.spt.ark"
    )
    private ArkExpInfoQueryService arkExpInfoQueryService;

    @MdpThriftClient(
            timeout = 2000,
            testTimeout = 5000,
            remoteAppKey = "com.sankuai.spt.ark"
    )
    private ArkExpCheckService arkExpCheckService;

    @MdpThriftClient(
            timeout = 2000,
            testTimeout = 5000,
            remoteAppKey = "com.sankuai.spt.gray"
    )
    private ProbeStrategyUnitCheckService probeStrategyUnitCheckService;

    @MdpThriftClient(remoteAppKey = "com.sankuai.nibscp.framework.metadata", timeout = 3000)
    private CpvQueryService cpvQueryService;

    @Bean
    public CitadelService citadelService() {
        Config config = new Config();
        config.setTimeOut(2000);
        return new CitadelService(config);
    }

    @MdpThriftClient(
            remoteAppKey = "com.sankuai.code.analyze.dispatcher",
            timeout = 3000
    )
    private AnalysisProjectThriftService analysisProjectThriftService;

}
package com.sankuai.qpro.ai.professor.application.converter;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.dzviewscene.unifiedshelf.operator.api.dto.OperatorShelfConfigDTO;
import com.sankuai.dzviewscene.unifiedshelf.operator.api.enums.OperatorShelfConfigSource;
import com.sankuai.dzviewscene.unifiedshelf.operator.api.enums.OperatorShelfConfigStableTag;
import com.sankuai.dzviewscene.unifiedshelf.operator.api.enums.OperatorShelfConfigStrategyUnit;
import com.sankuai.qpro.ai.professor.api.enums.ConfigUnitEnum;
import com.sankuai.qpro.ai.professor.application.model.operator.probe.agent.gene.*;
import com.sankuai.qpro.ai.professor.application.model.operator.probe.agent.gene.param.ProbeGeneParam;
import com.sankuai.qpro.ai.professor.application.model.operator.probe.agent.exp.ExpGenerateInfoModel;
import com.sankuai.qpro.ai.professor.application.model.operator.probe.agent.exp.ExpModel;
import com.sankuai.qpro.ai.professor.application.model.operator.probe.agent.exp.ExpStrategyConfigModel;
import com.sankuai.qpro.ai.professor.application.model.operator.probe.agent.exp.ExpStrategyModel;
import com.sankuai.qpro.ai.professor.application.utils.SerializeUtils;
import com.sankuai.qpro.ai.professor.application.utils.TimeUtils;
import com.sankuai.qpro.ai.professor.entity.ConfigUnitEntity;
import com.sankuai.spt.ark.common.enums.exp.ArkExpStrategyTypeEnum;
import com.sankuai.spt.gray.api.enums.probe.ProbeStrategyCategoryTypeEnum;
import com.sankuai.spt.gray.api.enums.probe.ProbeStrategyUnitSceneEnum;
import org.apache.commons.collections.CollectionUtils;

import org.apache.commons.lang.StringUtils;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * @author: wuwenqiang
 * @create: 2025-01-15
 * @description:
 */
public class ProbeGeneConverter {

    public static StructureInfoModel convert2StructureInfoModel(SimpleStructInfoModel model) {
        if (model == null || model.getExpStructModel() == null) {
            return null;
        }
        StructureInfoModel result = new StructureInfoModel();
        result.setNeedAttrs(model.getNeedAttrs());
        List<String> expIds = model.getExpStructModel().getExpIds();
        if (CollectionUtils.isEmpty(expIds)) {
            return null;
        }
        List<ExpModel> expModels = Lists.newArrayList();
        for (String expId : expIds) {
            ExpModel expModel = createExpModel(expId, model);
            expModels.add(expModel);
        }
        result.setExpModels(expModels);
        return result;
    }

    private static ExpModel createExpModel(String expId, SimpleStructInfoModel model) {
        ExpModel expModel = new ExpModel();
        expModel.setExpId(expId);
        expModel.setExpStrategyModels(
            convertExpStrategyModel(expId, model.getExpStructModel().getExpStructStrategyModels())
        );
        return expModel;
    }

    private static List<ExpStrategyModel> convertExpStrategyModel(String expId, List<ExpStructStrategyModel> expStructStrategyModels) {
        if (CollectionUtils.isEmpty(expStructStrategyModels)) {
            return null;
        }

        List<ExpStrategyModel> expStrategyModels = Lists.newArrayList();
        for (ExpStructStrategyModel expStructStrategyModel : expStructStrategyModels) {
            ExpStrategyModel expStrategyModel = createExpStrategyModel(expId, expStructStrategyModel);
            expStrategyModels.add(expStrategyModel);
        }
        return expStrategyModels;
    }

    private static ExpStrategyModel createExpStrategyModel(String expId, ExpStructStrategyModel expStructStrategyModel) {
        ExpStrategyModel expStrategyModel = new ExpStrategyModel();
        expStrategyModel.setExpStrategyId(convertStrategyId(expId, expStructStrategyModel));
        expStrategyModel.setExpStrategyShortName(expStructStrategyModel.getExpStrategyShortName());
        expStrategyModel.setStrategy(expStructStrategyModel.getStrategy());
        return expStrategyModel;
    }

    private static String convertStrategyId(String expId, ExpStructStrategyModel model) {
        if (StringUtils.isBlank(expId) || model == null || CollectionUtils.isEmpty(model.getExpStrategyIds())) {
            return null;
        }
        for (String expStrategyId : model.getExpStrategyIds()) {
            if (StringUtils.isNotBlank(expStrategyId) && expStrategyId.contains(expId)) {
                return expStrategyId;
            }
        }
        return null;
    }

    public static String convertPoiCategoryNames(ProbeGeneParam param) {
        if (param == null || CollectionUtils.isEmpty(param.getPoiCategoryNames())) {
            return null;
        }
        return param.getPoiCategoryNames().stream()
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.joining(","));
    }

    /**
     * 转换货架策略单元名称
     * @param param
     * @return
     */
    public static OperatorShelfConfigStrategyUnit convertStrategyUnit(ProbeGeneParam param) {
        if (ConfigUnitEnum.SHELF_SUBTITLE.getCode().equals(param.getConfigUnit())) {
            return OperatorShelfConfigStrategyUnit.subTitle;
        } else if (ConfigUnitEnum.TITLE_PREFIX.getCode().equals(param.getConfigUnit())) {
            return OperatorShelfConfigStrategyUnit.preTitle;
        }
        return null;
    }

    /**
     * 转换货架配置信息
     * @param param 生成Agent入参
     * @param structureInfoModel 结构信息模型
     * @param code 生成代码
     * @return
     */
    public static OperatorShelfConfigDTO convertShelfConfig(ProbeGeneParam param, StructureInfoModel structureInfoModel, String code) {
        OperatorShelfConfigDTO config = new OperatorShelfConfigDTO();
        config.setGroovyContent(code);
        config.setMtExperimentId(getFirstDouHuExpId(structureInfoModel));
        config.setNeedAttrs(structureInfoModel.getNeedAttrs());
        config.setShopBackCategories(param.getPoiCategoryList());
        config.setProductCategories(Lists.newArrayList(param.getProductCategoryId()));
        config.setStrategyUnit(convertStrategyUnit(param));
        config.setStableTag(OperatorShelfConfigStableTag.preview);
        config.setSource(OperatorShelfConfigSource.agent);
        return config;
    }

    private static String getFirstDouHuExpId(StructureInfoModel structureInfoModel) {
        if (structureInfoModel == null || CollectionUtils.isEmpty(structureInfoModel.getExpModels())) {
            return null;
        }
        List<String> expIds = structureInfoModel.getExpModels().stream()
                .map(ExpModel::getExpId)
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toList());
        return CollectionUtils.isNotEmpty(expIds) ? expIds.get(0) : null;
    }

    /**
     * 转换生成模型
     * @param param 生成Agent入参
     * @param structureInfoModel 结构信息模型
     * @param configUnitInfo 配置单元信息
     * @param code 生成代码
     * @return
     */
    public static GenerateOutModel buildGenerateOutModel(ProbeGeneParam param, StructureInfoModel structureInfoModel, ConfigUnitEntity configUnitInfo, String code) {
        GenerateOutModel result = new GenerateOutModel();
        result.setConversionId(param.getConversationId());
        result.setShelfConfigUnitKey(convertStrategyUnit(param).toString());
        result.setQzUnitKey(convertQZUnitKey(param));
        result.setBaseStrategy(buildBaseStrategyUnitModel(param, configUnitInfo));
        result.setExpInfos(buildExpGenerateInfoModels(structureInfoModel));
        result.setStrategyInfos(buildUnitStrategyModelsMap(structureInfoModel));
        result.setSummaryStrategy(buildUnitSummaryStrategy(structureInfoModel));
        result.setDsl(String.format("```\n%s\n```", code));
        result.setIsExp(true);
        result.setCategoryInfos(convertCategoryList(param));
        result.setNeedAttrs(structureInfoModel.getNeedAttrs());
        result.setStrategyData(SerializeUtils.toJsonStr(convertShelfConfig(param, structureInfoModel, code)));
        String[] strategyTimes = computeStrategyTime(structureInfoModel);
        if (strategyTimes != null) {
            result.setStrategyStartTime(strategyTimes[0]);
            result.setStrategyEndTime(strategyTimes[1]);
            result.setStrategyStartTimeLong(TimeUtils.convertStringToMillSecond(strategyTimes[0]));
            result.setStrategyEndTimeLong(TimeUtils.convertStringToMillSecond(strategyTimes[1]));
        }
        return result;
    }

    private static Integer convertQZUnitKey(ProbeGeneParam param) {
        if (ConfigUnitEnum.SHELF_SUBTITLE.getCode().equals(param.getConfigUnit())) {
            return ProbeStrategyUnitSceneEnum.SHELF_SUBTITLE.getCode();
        } else if (ConfigUnitEnum.TITLE_PREFIX.getCode().equals(param.getConfigUnit())) {
            return ProbeStrategyUnitSceneEnum.SHELF_TITLE_PREFIX.getCode();
        }
        return null;
    }

    private static BaseStrategyUnitModel buildBaseStrategyUnitModel(ProbeGeneParam param, ConfigUnitEntity configUnitInfo) {
        if (configUnitInfo == null) {
            return null;
        }
        BaseStrategyUnitModel result = new BaseStrategyUnitModel();
        result.setSceneName(configUnitInfo.getScene());
        result.setConfigUnitName(configUnitInfo.getDescription());
        // 转换ID
        result.setPoiCategory(ProbeGeneConverter.convertPoiCategoryNames(param));
        result.setProductCategory(param.getProductCategoryName());
        return result;
    }

    private static List<ExpGenerateInfoModel> buildExpGenerateInfoModels(StructureInfoModel structureInfoModel) {
        if (structureInfoModel == null || CollectionUtils.isEmpty(structureInfoModel.getExpModels())) {
            return null;
        }
        return buildExpGenerateInfoModels(structureInfoModel.getExpModels());
    }

    private static Map<String, List<UnitStrategyModel>> buildUnitStrategyModelsMap(StructureInfoModel structureInfoModel) {
        if (structureInfoModel == null || CollectionUtils.isEmpty(structureInfoModel.getExpModels())) {
            return Maps.newHashMap();
        }
        List<ExpModel> expModels = structureInfoModel.getExpModels();
        return expModels.stream().collect(Collectors.toMap(ExpModel::getExpId, ProbeGeneConverter::buildUnitStrategyModels, (a, b) -> a));
    }

    private static String buildUnitSummaryStrategy(StructureInfoModel structureInfoModel) {
        if (structureInfoModel == null || CollectionUtils.isEmpty(structureInfoModel.getExpModels())) {
            return null;
        }
        ExpModel expModel = structureInfoModel.getExpModels().get(0);
        List<UnitStrategyModel> unitStrategyModels = buildUnitStrategyModels(expModel);
        return buildUnitSummaryStrategy(unitStrategyModels);
    }

    private static List<ExpGenerateInfoModel> buildExpGenerateInfoModels(List<ExpModel> expModels) {
        if (CollectionUtils.isEmpty(expModels)) {
            return Lists.newArrayList();
        }
        return expModels.stream().map(ProbeGeneConverter::buildExpGenerateInfoModel).filter(Objects::nonNull).collect(Collectors.toList());
    }

    private static ExpGenerateInfoModel buildExpGenerateInfoModel(ExpModel expModel) {
        if (expModel == null) {
            return null;
        }
        ExpGenerateInfoModel result = new ExpGenerateInfoModel();
        result.setExpId(expModel.getExpId());
        result.setPlatform(expModel.getPlatform());
        result.setExpStartTime(expModel.getStartTime());
        result.setExpEndTime(expModel.getEndTime());
        result.setExpNumsWithoutControl(getExpWithoutControlNum(expModel.getExpStrategyModels()));
        result.setExpStrategyGroups(buildExpStrategyModels(expModel.getExpStrategyModels()));
        return result;
    }

    private static List<ExpStrategyConfigModel> buildExpStrategyModels(List<ExpStrategyModel> expStrategyModels) {
        if (CollectionUtils.isEmpty(expStrategyModels)) {
            return Lists.newArrayList();
        }
        return expStrategyModels.stream().map(ProbeGeneConverter::buildExpStrategyConfigModel)
                .filter(Objects::nonNull).collect(Collectors.toList());
    }

    private static ExpStrategyConfigModel buildExpStrategyConfigModel(ExpStrategyModel expStrategyModel) {
        if (expStrategyModel == null) {
            return null;
        }
        ExpStrategyConfigModel result = new ExpStrategyConfigModel();
        result.setStrategyKey(expStrategyModel.getExpStrategyId());
        result.setStrategyName(expStrategyModel.getExpStrategyName());
        result.setStrategyShortName(convertStrategyShortNameWithType(expStrategyModel));
        result.setStrategyType(expStrategyModel.getStrategyType());
        result.setRatio(String.valueOf(Optional.ofNullable(expStrategyModel.getRatio()).orElse(0)));
        return result;
    }

    private static int getExpWithoutControlNum(List<ExpStrategyModel> expStrategyModels) {
        if (CollectionUtils.isEmpty(expStrategyModels)) {
            return 0;
        }
        return (int) expStrategyModels.stream()
                .filter(Objects::nonNull)
                .filter(ProbeGeneConverter::isExpStrategy).count();
    }

    private static List<UnitStrategyModel> buildUnitStrategyModels(ExpModel expModel) {
        if (expModel == null || CollectionUtils.isEmpty(expModel.getExpStrategyModels())) {
            return Lists.newArrayList();
        }
        return expModel.getExpStrategyModels().stream()
                .filter(ProbeGeneConverter::isExpStrategy)
                .map(ProbeGeneConverter::buildUnitStrategyModel)
                .filter(Objects::nonNull).collect(Collectors.toList());
    }

    private static UnitStrategyModel buildUnitStrategyModel(ExpStrategyModel expStrategyModel) {
        if (expStrategyModel == null) {
            return null;
        }
        UnitStrategyModel result = new UnitStrategyModel();
        result.setExpStrategyKey(expStrategyModel.getExpStrategyId());
        result.setExpStrategyName(expStrategyModel.getExpStrategyName());
        result.setExpStrategyShortName(convertStrategyShortNameWithType(expStrategyModel));
        result.setStrategyDesc(expStrategyModel.getStrategy());
        return result;
    }

    private static String convertStrategyShortNameWithType(ExpStrategyModel expStrategyModel) {
        if (expStrategyModel == null || StringUtils.isBlank(expStrategyModel.getExpStrategyShortName())) {
            return null;
        }
        if (StringUtils.isBlank(expStrategyModel.getStrategyType())) {
            return expStrategyModel.getExpStrategyShortName();
        }
        int type = Integer.parseInt(expStrategyModel.getStrategyType());
        if (type == ArkExpStrategyTypeEnum.EXPERIMENTAL.getCode()) {
            return "实验组" + expStrategyModel.getExpStrategyShortName();
        } else if (type == ArkExpStrategyTypeEnum.CONTROL.getCode()) {
            return "对照组" + expStrategyModel.getExpStrategyShortName();
        } else if (type == ArkExpStrategyTypeEnum.BLANK_CONTROL.getCode()) {
            return "空白对照组" + expStrategyModel.getExpStrategyShortName();
        }
        return expStrategyModel.getExpStrategyShortName();
    }

    private static String[] computeStrategyTime(StructureInfoModel model) {
        if (model == null || CollectionUtils.isEmpty(model.getExpModels())) {
            return null;
        }
        List<ExpModel> expModels = model.getExpModels();
        String minTime = expModels.get(0).getStartTime();
        String maxTime = expModels.get(0).getEndTime();
        for (ExpModel expModel : expModels) {
            if (compareTime(minTime, expModel.getStartTime()) > 0) {
                minTime = expModel.getStartTime();
            }
            if (compareTime(maxTime, expModel.getEndTime()) < 0) {
                maxTime = expModel.getEndTime();
            }
        }
        return new String[]{minTime, maxTime};
    }

    private static int compareTime(String time1, String time2) {
        if (StringUtils.isBlank(time1) || StringUtils.isBlank(time2)) {
            return 0;
        }
        LocalDateTime time1Local = TimeUtils.convertStringToLocalDateTime(time1);
        LocalDateTime time2Local = TimeUtils.convertStringToLocalDateTime(time2);
        return time1Local.compareTo(time2Local);
    }

    private static boolean isExpStrategy(ExpStrategyModel expStrategyModel) {
        if (StringUtils.isBlank(expStrategyModel.getStrategyType())) {
            return false;
        }
        int type = Integer.parseInt(expStrategyModel.getStrategyType());
        return ArkExpStrategyTypeEnum.EXPERIMENTAL.getCode() == type;
    }

    private static String buildUnitSummaryStrategy(List<UnitStrategyModel> unitStrategyModels) {
        if (CollectionUtils.isEmpty(unitStrategyModels)) {
            return "";
        }
        StringBuilder result = new StringBuilder();
        for (UnitStrategyModel unitStrategyModel : unitStrategyModels) {
            if (unitStrategyModel == null || StringUtils.isBlank(unitStrategyModel.getExpStrategyShortName())
                    || StringUtils.isBlank(unitStrategyModel.getStrategyDesc())) {
                continue;
            }
            result.append(String.format("【实验组%s】\n %s\n", unitStrategyModel.getExpStrategyShortName(), unitStrategyModel.getStrategyDesc()));
        }
        return result.toString();
    }

    private static List<CategoryInfoModel> convertCategoryList(ProbeGeneParam param) {
        if (param == null) {
            return Lists.newArrayList();
        }
        List<Integer> poiCategoryIds = param.getPoiCategoryList();
        Long productCategoryId = param.getProductCategoryId();
        if (CollectionUtils.isEmpty(poiCategoryIds) && productCategoryId == null) {
            return Lists.newArrayList();
        }
        List<CategoryInfoModel> result = Lists.newArrayList();
        for (Integer poiCategoryId : poiCategoryIds) {
            if (poiCategoryId == null) {
                continue;
            }
            result.add(new CategoryInfoModel(poiCategoryId.longValue(), ProbeStrategyCategoryTypeEnum.POI_CATEGORY.getCode()));
        }
        if (productCategoryId != null && productCategoryId > 0) {
            result.add(new CategoryInfoModel(productCategoryId, ProbeStrategyCategoryTypeEnum.DEAL_GROUP_PLATFORM_CATEGORY.getCode()));
        }
        return result;
    }
}

package com.sankuai.qpro.ai.professor.application.enums;

import org.springframework.util.MimeType;
import org.springframework.util.MimeTypeUtils;

/**
 * @author: w<PERSON><PERSON><PERSON><PERSON>
 * @create: 2025-07-01
 * @description: MIME类型枚举，与ImageTypeEnum建立关联
 */
public enum MimeTypeEnum {
    
    IMAGE_PNG("image/png", MimeTypeUtils.IMAGE_PNG),
    IMAGE_JPEG("image/jpeg", MimeTypeUtils.IMAGE_JPEG),
    IMAGE_GIF("image/gif", MimeTypeUtils.IMAGE_GIF),
    IMAGE_WEBP("image/webp", MimeTypeUtils.parseMimeType("image/webp"));
    
    private final String mimeTypeString;
    private final MimeType mimeType;
    
    MimeTypeEnum(String mimeTypeString, MimeType mimeType) {
        this.mimeTypeString = mimeTypeString;
        this.mimeType = mimeType;
    }
    
    public String getMimeTypeString() {
        return mimeTypeString;
    }
    
    public MimeType getMimeType() {
        return mimeType;
    }
    
    /**
     * 根据MIME类型字符串获取对应的枚举
     * @param mimeTypeString MIME类型字符串
     * @return 对应的枚举值，如果没有找到则返回null
     */
    public static MimeTypeEnum getByMimeTypeString(String mimeTypeString) {
        for (MimeTypeEnum mimeTypeEnum : values()) {
            if (mimeTypeEnum.getMimeTypeString().equalsIgnoreCase(mimeTypeString)) {
                return mimeTypeEnum;
            }
        }
        return null;
    }
    

}

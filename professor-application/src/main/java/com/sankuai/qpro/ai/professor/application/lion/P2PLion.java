package com.sankuai.qpro.ai.professor.application.lion;

import com.meituan.mdp.boot.starter.config.annotation.MdpConfig;
import org.springframework.stereotype.Repository;

/**
 * P2PLion
 *
 * <AUTHOR>
 * @since 2025/5/29
 */
@Repository
public class P2PLion {

    /**
     * POJO方法调用阈值，当一个类的get/set/is方法被调用次数超过此阈值时，视为POJO类
     * 默认值为5
     */
    @MdpConfig("TOPOLOGY_POJO_METHOD_THRESHOLD:5")
    public Integer TOPOLOGY_POJO_METHOD_THRESHOLD;

}
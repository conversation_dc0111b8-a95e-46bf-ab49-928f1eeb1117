package com.sankuai.qpro.ai.professor.application.lion;

import com.meituan.mdp.boot.starter.config.annotation.MdpConfig;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2025/1/16
 */
@Service
public class ProbeCacheInLion {

    @MdpConfig("CONF_UNIT_DESC:我要调整货架商品卡片副标题展示")
    public String CONF_UNIT_DESC;

    @MdpConfig("CHOOSE_CATEGORY_DESC:POI分类是休闲娱乐/按摩/足疗(48)，团购分类是休闲娱乐/按摩/足疗/艾灸(84101019)")
    public String CHOOSE_CATEGORY_DESC;

    @MdpConfig("POI_CATEGORY_ID:48")
    public Integer POI_CATEGORY_ID;

    @MdpConfig("POI_CATEGORY_NAME:休闲娱乐/按摩/足疗")
    public String POI_CATEGORY_NAME;

    @MdpConfig("PRODUCT_CATEGORY_ID:84101019")
    public Long PRODUCT_CATEGORY_ID;

    @MdpConfig("PRODUCT_CATEGORY_NAME:休闲娱乐/按摩/足疗/艾灸")
    public String PRODUCT_CATEGORY_NAME;
}

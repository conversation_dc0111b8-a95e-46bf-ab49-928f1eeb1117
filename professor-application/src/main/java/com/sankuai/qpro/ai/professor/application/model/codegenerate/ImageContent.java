package com.sankuai.qpro.ai.professor.application.model.codegenerate;

import com.sankuai.qpro.ai.professor.application.enums.MimeTypeEnum;
import lombok.Data;
import org.apache.commons.lang.StringUtils;
import org.springframework.ai.content.Media;

import java.io.Serializable;

/**
 * @author: w<PERSON><PERSON><PERSON><PERSON>
 * @create: 2025-07-01
 * @description:
 */
@Data
public class ImageContent implements Serializable {
    private String type = "image";
    // base64编码
    private String data;
    // MIME类型，如image/jpeg
    private String mimeType;

    public ImageContent(String data, String mimeType) {
        this.data = data;
        this.mimeType = mimeType;
    }

    public static ImageContent of(String data, String formatType) {
        String lowerFormatType = StringUtils.isEmpty(formatType) ? null : formatType.toLowerCase();
        String mimeType = StringUtils.isBlank(lowerFormatType) ? null : "image/" + lowerFormatType;
        return new ImageContent(data, mimeType);
    }

    public static Media buildMedia(ImageContent imageContent) {
        if (imageContent == null || org.apache.commons.lang3.StringUtils.isBlank(imageContent.getData())) {
            return null;
        }
        String urlData = String.format("data:%s;base64,%s", imageContent.getMimeType(), imageContent.getData());
        MimeTypeEnum mimeTypeEnum = MimeTypeEnum.getByMimeTypeString(imageContent.getMimeType());
        if (mimeTypeEnum == null) {
            return null;
        }
        return Media.builder()
                .mimeType(mimeTypeEnum.getMimeType())
                .data(urlData)
                .build();
    }
}

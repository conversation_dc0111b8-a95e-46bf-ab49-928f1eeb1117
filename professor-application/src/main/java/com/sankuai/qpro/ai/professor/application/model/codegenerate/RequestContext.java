package com.sankuai.qpro.ai.professor.application.model.codegenerate;

import com.google.common.collect.Maps;
import lombok.Data;

import java.util.Map;

/**
 * @author: w<PERSON><PERSON><PERSON><PERSON>
 * @create: 2025-05-23
 * @description: 请求上下文
 */
@Data
public class RequestContext {
    private final static ThreadLocal<Map<Object, Object>> attribute = new ThreadLocal<>();

    public static void init() {
        attribute.set(Maps.newHashMap());
    }

    public static void setAttribute(Object key, Object value) {
        Map<Object, Object> map = attribute.get();
        if (map != null) {
            attribute.get().put(key, value);
        }
    }

    public static <T> T getAttribute(Object key) {
        Map<Object, Object> map = attribute.get();
        if (map == null) {
            return null;
        }
        return (T) map.get(key);
    }

    public static<T> T getAllAttribute() {
        return (T) attribute.get();
    }

    public static void setAllAttribute(Map<Object, Object> map) {
        attribute.set(map);
    }

    public static void clear() {
        Map<Object, Object> map = attribute.get();
        if (map != null) {
            map.clear();
        }
        attribute.remove();
    }
}

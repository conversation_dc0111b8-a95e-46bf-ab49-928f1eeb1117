package com.sankuai.qpro.ai.professor.application.model.codegenerate;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyDescription;
import lombok.Data;

import java.util.List;

/**
 * @author: w<PERSON><PERSON><PERSON><PERSON>
 * @create: 2025-05-27
 * @description: 需求评审信息
 */
@Data
public class RequirementReviewInfo {
    @JsonProperty(required = true)
    @JsonPropertyDescription("仓库链接")
    private String codeRepoUrl;

    @JsonProperty(required = true)
    @JsonPropertyDescription("appkey")
    private String appkey;

    @JsonProperty(required = true)
    @JsonPropertyDescription("接口列表")
    private List<String> interfaces;
}

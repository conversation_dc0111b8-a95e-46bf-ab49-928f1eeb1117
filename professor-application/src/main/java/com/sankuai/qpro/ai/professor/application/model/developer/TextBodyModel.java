package com.sankuai.qpro.ai.professor.application.model.developer;

import com.alibaba.fastjson.JSONObject;
import lombok.Data;
import lombok.ToString;

@Data
@ToString
public class TextBodyModel {
    private boolean bold = true;

    private String fontName = "宋体";

    private int fontSize = 12;

    private String text;

    private short cipherType = 0;

    public static TextBodyModel fromJson(String json) {
        TextBodyModel textBody = JSONObject.parseObject(json, TextBodyModel.class);
        return textBody;
    }
}

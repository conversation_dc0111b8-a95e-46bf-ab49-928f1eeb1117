package com.sankuai.qpro.ai.professor.application.model.operator.exception.execute;

/**
 * 程序执行异常（非模型异常）
 * <AUTHOR>
 * @date 2025/1/20
 */
public class ExecuteException extends RuntimeException {

    public ExecuteException() {
        super();
    }

    public ExecuteException(String message) {
        super(message);
    }

    public ExecuteException(String message, Throwable cause) {
        super(message, cause);
    }

    public ExecuteException(Throwable cause) {
        super(cause);
    }

    protected ExecuteException(String message, Throwable cause, boolean enableSuppression, boolean writableStackTrace) {
        super(message, cause, enableSuppression, writableStackTrace);
    }
}

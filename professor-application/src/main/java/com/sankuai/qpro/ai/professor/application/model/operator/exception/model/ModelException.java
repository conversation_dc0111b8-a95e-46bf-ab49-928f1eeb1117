package com.sankuai.qpro.ai.professor.application.model.operator.exception.model;

/**
 * 模型异常
 * <AUTHOR>
 * @date 2025/1/20
 */
public class ModelException extends RuntimeException {

    // 默认构造函数
    public ModelException() {
        super();
    }

    // 带有详细信息的构造函数
    public ModelException(String message) {
        super(message);
    }

    // 带有详细信息和原因的构造函数
    public ModelException(String message, Throwable cause) {
        super(message, cause);
    }

    // 带有原因的构造函数
    public ModelException(Throwable cause) {
        super(cause);
    }
}

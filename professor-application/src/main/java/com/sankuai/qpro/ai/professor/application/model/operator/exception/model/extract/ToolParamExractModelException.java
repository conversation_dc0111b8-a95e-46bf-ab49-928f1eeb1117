package com.sankuai.qpro.ai.professor.application.model.operator.exception.model.extract;

/**
 * 工具参数提取模型异常
 * <AUTHOR>
 * @date 2025/1/20
 */
public class ToolParamExractModelException extends ExtractModelException {

    public ToolParamExractModelException() {
        super();
    }

    public ToolParamExractModelException(String message) {
        super(message);
    }

    public ToolParamExractModelException(String message, Throwable cause) {
        super(message, cause);
    }

    public ToolParamExractModelException(Throwable cause) {
        super(cause);
    }
}

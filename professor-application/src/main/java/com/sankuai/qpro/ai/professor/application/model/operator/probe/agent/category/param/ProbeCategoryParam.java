package com.sankuai.qpro.ai.professor.application.model.operator.probe.agent.category.param;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyDescription;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/1/14
 */
@Data
public class ProbeCategoryParam {

    @JsonProperty(required = true)
    @JsonPropertyDescription("配置单元")
    private String configUnit;

    @JsonProperty(required = true)
    @JsonPropertyDescription("会话Id")
    private Long conversationId;

    @JsonProperty(required = true)
    @JsonPropertyDescription("poi分类ID")
    private List<Integer> poiCategoryIdList;

    @JsonProperty(required = true)
    @JsonPropertyDescription("poi分类名称")
    private List<String> poiCategoryNameList;

    @JsonProperty(required = true)
    @JsonPropertyDescription("团购分类ID")
    private Long productCategoryId;

    @JsonProperty(required = true)
    @JsonPropertyDescription("团购分类名称")
    private String productCategoryName;
}

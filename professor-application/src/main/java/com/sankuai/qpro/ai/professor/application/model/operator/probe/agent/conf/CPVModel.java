package com.sankuai.qpro.ai.professor.application.model.operator.probe.agent.conf;

import com.google.common.collect.Sets;
import lombok.Data;

import java.util.Set;

/**
 * CPV 模型 https://www.toutiao.com/article/7272795638990422540/?wid=1736216696467
 * <AUTHOR>
 * @date 2025/1/8
 */
@Data
public class CPVModel {

    private Long category;

    private String property;

    private String propertyName;

    private Set<String> value = Sets.newHashSet();

    public static CPVModel from(Long category, String property, String propertyName) {
        CPVModel cpvModel = new CPVModel();
        cpvModel.setCategory(category);
        cpvModel.setProperty(property);
        cpvModel.setPropertyName(propertyName);
        return cpvModel;
    }
}

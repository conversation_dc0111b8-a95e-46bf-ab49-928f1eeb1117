package com.sankuai.qpro.ai.professor.application.model.operator.probe.agent.conf;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyDescription;
import lombok.Data;

import java.util.List;

/**
 * 点位Position、属性Property、属性Value、展示Display
 * <AUTHOR>
 * @date 2025/1/15
 */
@Data
public class PPVDList {
    @JsonProperty(required = true)
    @JsonPropertyDescription("点位配置信息列表")
    private List<Position> positionList;
}

package com.sankuai.qpro.ai.professor.application.model.operator.probe.agent.conf;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/1/12
 */
@Data
public class PVCheckResult {
    /**
     * 是否有效
     */
    private boolean valid;
    /**
     * 不正确时，请写明原因
     */
    private String msg;

    public static PVCheckResult of(boolean valid, String msg) {
        PVCheckResult result = new PVCheckResult();
        result.setValid(valid);
        result.setMsg(msg);
        return result;
    }
}

package com.sankuai.qpro.ai.professor.application.model.operator.probe.agent.conf;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyDescription;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/1/13
 */
@Data
public class PVD {

    @JsonProperty(required = true)
    @JsonPropertyDescription("属性中文名")
    private String propertyName;

    @JsonProperty(required = true)
    @JsonPropertyDescription("属性英文名")
    private String property;

    @JsonProperty(required = true)
    @JsonPropertyDescription("属性value和展示信息列表")
    private List<VD> vdList;
}

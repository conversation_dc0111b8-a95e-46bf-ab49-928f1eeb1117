package com.sankuai.qpro.ai.professor.application.model.operator.probe.agent.conf;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyDescription;
import lombok.Data;

import java.util.List;

/**
 * 属性、属性value、展示
 * <AUTHOR>
 * @date 2025/1/13
 */
@Data
public class Position {

    @JsonProperty(required = true)
    @JsonPropertyDescription("待配置点位，忽略被替换的点位")
    private String position;

    @JsonProperty(required = true)
    @JsonPropertyDescription("属性配置信息列表")
    private List<PVD> pvdList;
}

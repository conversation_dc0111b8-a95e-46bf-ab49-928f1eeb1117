package com.sankuai.qpro.ai.professor.application.model.operator.probe.agent.conf;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyDescription;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/1/14
 */
@Data
public class ProbeConfParam {

    @JsonProperty(required = true)
    @JsonPropertyDescription("配置单元")
    private String configUnit;

    @JsonProperty(required = true)
    @JsonPropertyDescription("会话Id")
    private Long conversationId;

    /**
     * 通过db读取
     */
    private List<Integer> poiCategoryList;

    /**
     * 通过db读取
     */
    private Long productCategoryId;
}

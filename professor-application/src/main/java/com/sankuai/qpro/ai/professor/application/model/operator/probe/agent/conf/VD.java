package com.sankuai.qpro.ai.professor.application.model.operator.probe.agent.conf;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyDescription;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/1/13
 */
@Data
public class VD {

    @JsonProperty(required = true)
    @JsonPropertyDescription("属性枚举值，如果是拼接的则无")
    private String value;

    @JsonProperty(required = true)
    @JsonPropertyDescription("展示值或规则")
    private String display;
}

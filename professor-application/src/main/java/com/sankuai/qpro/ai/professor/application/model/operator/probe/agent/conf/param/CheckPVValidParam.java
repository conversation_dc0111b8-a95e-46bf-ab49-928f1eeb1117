package com.sankuai.qpro.ai.professor.application.model.operator.probe.agent.conf.param;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyDescription;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/1/8
 */
@Data
public class CheckPVValidParam {

    @JsonProperty(required = true)
    @JsonPropertyDescription("团购分类ID")
    private Long productCategoryId;

    @JsonProperty(required = true)
    @JsonPropertyDescription("配置信息")
    private String confStr;

    @JsonProperty(required = true)
    @JsonPropertyDescription("会话Id")
    private Long conversationId;

    public static CheckPVValidParam of(Long productCategoryId, String confStr) {
        CheckPVValidParam result = new CheckPVValidParam();
        result.setProductCategoryId(productCategoryId);
        result.setConfStr(confStr);
        return result;
    }

    public static CheckPVValidParam of(Long productCategoryId, String confStr, Long conversationId) {
        CheckPVValidParam result = new CheckPVValidParam();
        result.setProductCategoryId(productCategoryId);
        result.setConfStr(confStr);
        result.setConversationId(conversationId);
        return result;
    }
}

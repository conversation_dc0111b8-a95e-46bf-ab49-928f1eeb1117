package com.sankuai.qpro.ai.professor.application.model.operator.probe.agent.exp;

import lombok.Data;

import java.util.List;

/**
 * @author: w<PERSON><PERSON><PERSON><PERSON>
 * @create: 2024-12-14
 * @description:
 */
@Data
public class ExpBaseInfoModel {
    // 实验ID
    private String expId;
    // 实验名称
    private String expName;
    // 实验组数（不含对照组）
    private Integer expGroupNumWithoutControl;
    // 实验开始时间
    private String expStartTime;
    // 实验结束时间
    private String expEndTime;
    // 实验分组信息列表
    private List<ExpStrategyConfigModel> expStrategyGroups;
}

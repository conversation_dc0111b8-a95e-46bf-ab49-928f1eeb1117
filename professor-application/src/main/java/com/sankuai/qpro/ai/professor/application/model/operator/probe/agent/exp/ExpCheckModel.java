package com.sankuai.qpro.ai.professor.application.model.operator.probe.agent.exp;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @author: w<PERSON><PERSON><PERSON><PERSON>
 * @create: 2024-12-14
 * @description: 实验检测模型
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ExpCheckModel {
    // 检测结果：true为通过，false为不通过
    private boolean checkResult;
    // 是否可跳过
    private boolean canSkip;
    // 检测不通过时的错误信息
    private String checkErrorMsg;
    // 检测通过时，会返回第一个实验的详细信息
    private ExpModel expInfoModel;
    // 检测通过时，会返回全部实验的详细信息
    private List<ExpModel> expInfoModelList;
}

package com.sankuai.qpro.ai.professor.application.model.operator.probe.agent.exp;

import lombok.Data;

import java.util.List;

/**
 * @author: w<PERSON><PERSON><PERSON><PERSON>
 * @create: 2025-01-04
 * @description:
 */
@Data
public class ExpGenerateInfoModel {
    // 实验ID列表
//    private List<String> expIds;
    // 实验ID
    private String expId;
    // 实验平台
    private Integer platform;
    // 实验组数不含对照组
    private Integer expNumsWithoutControl;
    // 实验开始时间
    private String expStartTime;
    // 实验结束时间
    private String expEndTime;
    // 实验分组信息列表
    private List<ExpStrategyConfigModel> expStrategyGroups;
}

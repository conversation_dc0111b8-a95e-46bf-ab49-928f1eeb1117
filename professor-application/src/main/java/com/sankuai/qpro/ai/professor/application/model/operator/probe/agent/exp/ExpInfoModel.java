package com.sankuai.qpro.ai.professor.application.model.operator.probe.agent.exp;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * @author: w<PERSON><PERSON><PERSON><PERSON>
 * @create: 2024-12-14
 * @description:
 */
@Data
public class ExpInfoModel {
    // 实验标识
    private String expId;
    // 实验名称
    private String expName;
    // 实验状态
    private Integer expStatus;
    // 实验平台
    private Integer platform;
    // 开始时间
    private LocalDateTime startTime;
    // 结束时间
    private LocalDateTime endTime;
    // 实验策略分组
    private List<ExpStrategyModel> expStrategyModels;
}

package com.sankuai.qpro.ai.professor.application.model.operator.probe.agent.exp;

import lombok.Data;

import java.util.List;

/**
 * @author: w<PERSON><PERSON><PERSON><PERSON>
 * @create: 2024-12-14
 * @description:
 */
@Data
public class ExpModel {
    /**
     * 斗斛实验号
     */
    private String expId;

    /**
     * 实验名称
     */
    private String expName;

    /**
     * 实验平台 0:位置，1:点评，2:美团
     */
    private Integer platform;

    /**
     * 开始时间
     */
    private String startTime;

    /**
     * 结束时间
     */
    private String endTime;

    /**
     * 实验分组列表
     */
    private List<ExpStrategyModel> expStrategyModels;
}

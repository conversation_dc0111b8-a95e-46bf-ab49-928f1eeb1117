package com.sankuai.qpro.ai.professor.application.model.operator.probe.agent.exp;

import lombok.Data;

/**
 * @author: wu<PERSON><PERSON><PERSON>
 * @create: 2024-12-14
 * @description:
 */
@Data
public class ExpStrategyModel {
    /**
     * 斗斛分组ID
     */
    private String expStrategyId;

    /**
     * 斗斛分组简称，比如EXP2024123100001_A取A
     */
    private String expStrategyShortName;

    /**
     * 斗斛分组名称
     */
    private String expStrategyName;

    /**
     * 斗斛分组类型 (1:实验组，2和3:对照组)
     */
    private String strategyType;

    /**
     * 比例
     */
    private Integer ratio;

    /**
     * 斗斛分组策略描述，生成结果必须是字符串，请勿返回结构化的信息，以markdown格式展示
     */
    private String strategy;

    /**
     * 当前斗斛分组策略是否基于线上逻辑修改，非直接覆盖线上逻辑时为true，覆盖线上逻辑时为false
     */
    private boolean isModifyOnline;
}

package com.sankuai.qpro.ai.professor.application.model.operator.probe.agent.exp.param;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyDescription;
import lombok.Data;

import java.util.List;

/**
 * @author: w<PERSON><PERSON><PERSON><PERSON>
 * @create: 2024-12-14
 * @description:
 */
@Data
public class ProbeExpParam {

    @JsonProperty(required = true)
    @JsonPropertyDescription("会话Id")
    private Long conversationId;

    @JsonProperty(required = true)
    @JsonPropertyDescription("配置单元")
    private String configUnit;

    @JsonProperty(required = true)
    @JsonPropertyDescription("实验号")
    private List<String> expIdList;

    /**
     * 通过db读取
     */
    private Long productCategoryId;

    /**
     * 通过db读取
     */
    private String productCategoryName;
}

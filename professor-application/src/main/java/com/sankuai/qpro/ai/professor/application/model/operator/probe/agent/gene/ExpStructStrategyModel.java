package com.sankuai.qpro.ai.professor.application.model.operator.probe.agent.gene;

import lombok.Data;

import java.util.List;

/**
 * @author: w<PERSON><PERSON><PERSON><PERSON>
 * @create: 2025-01-15
 * @description:
 */
@Data
public class ExpStructStrategyModel {
    /**
     * 斗斛分组ID集合，相同斗斛分组简称对应ID放在一起
     */
    private List<String> expStrategyIds;

    /**
     * 斗斛分组简称，用英文字母表示。比如EXP2024123100001_A取A
     */
    private String expStrategyShortName;

    /**
     * 斗斛分组策略描述，请参考【斗斛分组策略描述要求】生成
     */
    private String strategy;
}

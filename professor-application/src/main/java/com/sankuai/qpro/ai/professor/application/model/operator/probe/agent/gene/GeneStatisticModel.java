package com.sankuai.qpro.ai.professor.application.model.operator.probe.agent.gene;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

/**
 * @author: wu<PERSON><PERSON><PERSON>
 * @create: 2025-02-11
 * @description: 生成统计模型
 */
@Data
public class GeneStatisticModel {
    private int[] timesCount;
    private int[] executeSuccessCount;
    private int[] resultSuccessCount;
    private long[] timesStartTime;
    private long[] timesEndTime;
    private int error;
    @JsonIgnore
    private String dsl;

    public GeneStatisticModel(int size) {
        this.timesCount = new int[size];
        this.executeSuccessCount = new int[size];
        this.resultSuccessCount = new int[size];
        this.timesStartTime = new long[size];
        this.timesEndTime = new long[size];
        this.error = 0;
    }
}

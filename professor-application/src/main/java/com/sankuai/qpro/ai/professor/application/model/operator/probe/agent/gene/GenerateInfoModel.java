package com.sankuai.qpro.ai.professor.application.model.operator.probe.agent.gene;

import com.sankuai.qpro.ai.professor.application.model.operator.probe.agent.exp.ExpBaseInfoModel;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * @author: w<PERSON><PERSON><PERSON><PERSON>
 * @create: 2025-01-04
 * @description:
 */
@Data
public class GenerateInfoModel {
    /**
     * 会话id
     */
    private Long conversionId;
    /**
     * 货架配置单元标识
     */
    private String shelfConfigUnitKey;
    /**
     * 基础信息
     */
    private BaseStrategyUnitModel baseStrategy;
    /**
     * 实验信息
     */
    private List<ExpBaseInfoModel> expInfos;
    /**
     * 实验方案(key为实验ID，value为实验组策略)
     */
    private Map<String, List<UnitStrategyModel>> strategyInfos;
    /**
     * 生成的代码，比如货架是groovy表达式
     */
    private String dsl;
    /**
     * 策略开始时间
     */
    private String strategyStartTime;
    /**
     * 策略结束时间
     */
    private String strategyEndTime;
    /**
     * 是否是实验策略
     */
    private Boolean isExp;
}

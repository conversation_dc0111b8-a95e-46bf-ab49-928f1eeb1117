package com.sankuai.qpro.ai.professor.application.model.operator.probe.agent.gene;

import com.sankuai.qpro.ai.professor.application.model.operator.probe.agent.exp.ExpGenerateInfoModel;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * @author: w<PERSON><PERSON><PERSON><PERSON>
 * @create: 2025-01-04
 * @description:
 */
@Data
public class GenerateOutModel {
    /**
     * 会话id
     */
    private Long conversionId;
    /**
     * 货架配置单元标识
     */
    private String shelfConfigUnitKey;
    /**
     * 轻舟单元标识
     */
    private Integer qzUnitKey;
    /**
     * 基础信息
     */
    private BaseStrategyUnitModel baseStrategy;
    /**
     * 实验信息
     */
//    private ExpGenerateInfoModel expInfo;
    private List<ExpGenerateInfoModel> expInfos;
//    /**
//     * 实验方案
//     */
//    private List<UnitStrategyModel> strategyInfos;
    /**
     * 实验ID映射实验方案
     */
    private Map<String, List<UnitStrategyModel>> strategyInfos;

    /**
     * 生成的代码，比如货架是groovy表达式
     */
    private String dsl;
    /**
     * 策略总结（汇总所有实验组的策略小结）
     */
    private String summaryStrategy;
    /**
     * 覆盖属性
     */
    private List<String> needAttrs;
    /**
     * 类目信息
     */
    private List<CategoryInfoModel> categoryInfos;
    /**
     * 策略数据
     */
    private String strategyData;
    /**
     * 策略开始时间
     */
    private String strategyStartTime;
    /**
     * 策略结束时间
     */
    private String strategyEndTime;
    /**
     * 策略开始时间Long时间戳
     */
    private Long strategyStartTimeLong;
    /**
     * 策略结束时间Long时间戳
     */
    private Long strategyEndTimeLong;
    /**
     * 是否是实验策略
     */
    private Boolean isExp;
}

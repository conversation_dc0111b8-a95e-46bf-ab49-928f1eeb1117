package com.sankuai.qpro.ai.professor.application.model.operator.probe.agent.gene;

import lombok.Data;

import java.util.List;

/**
 * @author: wu<PERSON><PERSON><PERSON>
 * @create: 2025-01-15
 * @description:
 */
@Data
public class SimpleStructInfoModel {
    /**
     * 属性key列表，用户在聊天记录中描述的所有属性key的集合。如用户描述"取属性时长duration"，duration就是一个属性key
     */
    private List<String> needAttrs;
    /**
     * 斗斛实验信息
     */
    private ExpStructModel expStructModel;
}

package com.sankuai.qpro.ai.professor.application.model.operator.probe.agent.gene;

import com.sankuai.qpro.ai.professor.application.model.operator.probe.agent.exp.ExpModel;
import lombok.Data;

import java.util.List;

/**
 * @author: w<PERSON><PERSON><PERSON><PERSON>
 * @create: 2025-01-04
 * @description:
 */
@Data
public class StructureInfoModel {
    /**
     * 会话id
     */
    private Long conversionId;
    /**
     * 属性key列表
     */
    private List<String> needAttrs;
    /**
     * 斗斛实验信息
     */
    private List<ExpModel> expModels;
}

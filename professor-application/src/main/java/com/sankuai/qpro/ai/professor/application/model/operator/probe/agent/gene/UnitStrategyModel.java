package com.sankuai.qpro.ai.professor.application.model.operator.probe.agent.gene;

import lombok.Data;

/**
 * @author: w<PERSON><PERSON><PERSON><PERSON>
 * @create: 2025-01-04
 * @description:
 */
@Data
public class UnitStrategyModel {
    // 实验策略ID
    private String expStrategyKey;
    // 实验策略名称
    private String expStrategyName;
    // 斗斛分组简称，比如EXP2024123100001_A取实验组A（不存在对照组）
    private String expStrategyShortName;
    // 策略描述
    private String strategyDesc;
}

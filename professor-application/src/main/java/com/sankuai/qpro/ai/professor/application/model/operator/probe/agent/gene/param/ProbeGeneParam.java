package com.sankuai.qpro.ai.professor.application.model.operator.probe.agent.gene.param;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyDescription;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/1/14
 */
@Data
public class ProbeGeneParam {

    @JsonProperty(required = true)
    @JsonPropertyDescription("配置单元")
    private String configUnit;

    @JsonProperty(required = true)
    @JsonPropertyDescription("会话Id")
    private Long conversationId;

    /**
     * 通过db读取
     */
    @JsonIgnore
    private List<Integer> poiCategoryList;

    /**
     * 通过db读取
     */
    @JsonProperty
    private List<String> poiCategoryNames;

    /**
     * 通过db读取
     */
    @JsonIgnore
    private Long productCategoryId;

    /**
     * 通过db读取
     */
    @JsonIgnore
    private String productCategoryName;

    /**
     * 通过db读取
     */
    @JsonIgnore
    private String strategy;
}

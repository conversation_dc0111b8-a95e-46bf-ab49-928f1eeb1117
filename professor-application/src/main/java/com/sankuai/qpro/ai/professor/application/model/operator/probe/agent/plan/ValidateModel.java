package com.sankuai.qpro.ai.professor.application.model.operator.probe.agent.plan;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @author: w<PERSON><PERSON><PERSON><PERSON>
 * @create: 2025-01-06
 * @description:
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ValidateModel {
    /**
     * 校验是否通过
     */
    private boolean passValidate;

    /**
     * 校验不通过的原因
     */
    private String reason;

    public static ValidateModel passValidate() {
        return new ValidateModel(true, "");
    }

    public static ValidateModel failedValidate(String reason) {
        return new ValidateModel(false, reason);
    }
}

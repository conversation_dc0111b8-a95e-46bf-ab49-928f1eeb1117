package com.sankuai.qpro.ai.professor.application.model.operator.probe.chat;

import com.sankuai.qpro.ai.professor.api.enums.ConversationStageEnum;
import com.sankuai.qpro.ai.professor.api.enums.MessageStatusEnum;
import com.sankuai.qpro.ai.professor.api.enums.SegmentEnum;
import com.sankuai.qpro.ai.professor.application.prompt.ProbeOutputPrompt;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/1/2
 */
@Data
public class AgentOutputModel {
    /**
     * 消息
     */
    private String msg;

    /**
     * 对话环节
     */
    private SegmentEnum segment;

    /**
     * 消息状态
     * {@link com.sankuai.qpro.ai.professor.api.enums.MessageStatusEnum}
     */
    private int status;

    /**
     * 异常信息
     */
    private Exception e;

    public static AgentOutputModel from(String msg, SegmentEnum segment) {
        AgentOutputModel agentOutputModel = new AgentOutputModel();
        agentOutputModel.setMsg(msg);
        agentOutputModel.setSegment(segment);
        return agentOutputModel;
    }

    public static AgentOutputModel from(String msg, SegmentEnum segment, int status) {
        AgentOutputModel agentOutputModel = new AgentOutputModel();
        agentOutputModel.setMsg(msg);
        agentOutputModel.setSegment(segment);
        agentOutputModel.setStatus(status);
        return agentOutputModel;
    }

    public static AgentOutputModel from(Exception e) {
        AgentOutputModel agentOutputModel = new AgentOutputModel();
        agentOutputModel.setE(e);
        return agentOutputModel;
    }

    public static AgentOutputModel from(int stage, SegmentEnum segment, ProbeOutputPrompt probeOutputPrompt) {
        if (segment.getOrder() >= SegmentEnum.EXP.getOrder()) {
            if (stage < ConversationStageEnum.CATEGORY_SELECTED.getCode()) {
                return AgentOutputModel.from("别着急哦，我们可以一步一步来！请先选择分类，让我们一起开始吧！" + probeOutputPrompt.SHELF_PARAM_COMPONENT, segment);
            }
        }
        if (segment.getOrder() >= SegmentEnum.CONF.getOrder()) {
            if (stage < ConversationStageEnum.EXP_ENTERED.getCode()) {
                return AgentOutputModel.from("别着急，我们可以慢慢来哦！请先告诉我斗斛实验号是什么呢？这样我们就可以继续了！" + probeOutputPrompt.PROBE_EXP_EXAMPLE, segment);
            }
            if (stage < ConversationStageEnum.EXP_CHECKED_FAILED.getCode()) {
                return AgentOutputModel.from("为了继续，请您提供一个有效的斗斛实验号哦！请问您能告知一下斗斛实验号是什么吗？" + probeOutputPrompt.PROBE_EXP_EXAMPLE, segment);
            }
        }
        if (segment.getOrder() >= SegmentEnum.GENERATE.getOrder()) {
            if (stage < ConversationStageEnum.CONF_ENTERED.getCode()) {
                return AgentOutputModel.from("请先配置属性，" + probeOutputPrompt.PROBE_CONF_EXAMPLE, segment);
            }
        }
        return null;
    }
}

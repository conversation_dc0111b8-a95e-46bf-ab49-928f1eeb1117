package com.sankuai.qpro.ai.professor.application.model.operator.task;

import lombok.Data;

import java.util.List;

/**
 * @author: wu<PERSON><PERSON><PERSON>
 * @create: 2024-12-30
 * @description:
 */
@Data
public class TaskItem {
    private Integer index;
    private String taskName;
    private String taskCondition;
    private String taskDesc;
    private String toolName;
    private List<TaskItem> childTasks;
}

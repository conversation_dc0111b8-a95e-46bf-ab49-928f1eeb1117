package com.sankuai.qpro.ai.professor.application.model.operator.task;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @author: wu<PERSON><PERSON><PERSON>
 * @create: 2024-12-30
 * @description:
 */
@Data
public class TaskList implements Serializable {
    // 会话ID
    private Long conversationId;
    // task-agent ID
    private String taskAgentId;
    // 基础提示词
    private String basePrompt;
    // 任务列表
    private List<TaskItem> taskItems;
    private DynamicTaskConfig dynamicTaskConfig;
}

package com.sankuai.qpro.ai.professor.application.model.operator.task;

import com.google.common.collect.Maps;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import java.util.Map;
import java.util.Objects;

/**
 * @author: wu<PERSON><PERSON><PERSON>
 * @create: 2024-12-30
 * @description:
 */
public class TaskListRepository {

    private static Map<Long, TaskList> conversTaskListMap = Maps.newConcurrentMap();
    private static Map<String, TaskList> taskAgentTaskListMap = Maps.newConcurrentMap();

    public static void addByConversationId(TaskList taskList) {
        if (Objects.isNull(taskList) || Objects.isNull(taskList.getConversationId())) {
            return;
        }
        conversTaskListMap.put(taskList.getConversationId(), taskList);
    }

    public static void addByTaskAgentId(TaskList taskList) {
        if (Objects.isNull(taskList) || StringUtils.isEmpty(taskList.getTaskAgentId())) {
            return;
        }
        taskAgentTaskListMap.put(taskList.getTaskAgentId(), taskList);
    }

    public static void addTaskList(String taskAgentName, TaskList taskList) {
        if (Objects.isNull(taskList) || StringUtils.isEmpty(taskAgentName)) {
            return;
        }
        taskAgentTaskListMap.put(taskAgentName, taskList);
    }

    public static TaskList getByConversationId(Long conversationId) {
        return conversTaskListMap.get(conversationId);
    }

    public static TaskList getByTaskAgentId(String taskAgentId) {
        return taskAgentTaskListMap.get(taskAgentId);
    }

    public static StringBuilder convertTaskListToString(TaskList taskList) {
        StringBuilder msg = new StringBuilder(taskList.getBasePrompt() + "\n");
        if (CollectionUtils.isNotEmpty(taskList.getTaskItems())) {
            msg.append("=======\n");
            msg.append("任务列表：\n");
            int index = 1;
            for (TaskItem taskItem : taskList.getTaskItems()) {
                appendTaskList(msg, taskItem, String.valueOf(index++));
            }
            msg.append("=======\n");
        }
        return msg;
    }

    private static void appendTaskList(StringBuilder msg, TaskItem taskItem, String index) {
        if (Objects.isNull(taskItem)) {
            return;
        }
        msg.append(String.format("任务%s \n", index));
        msg.append(String.format("任务名称:%s", taskItem.getTaskName()) + "\n");
//        msg.append("触发条件:");
//        msg.append(taskItem.getTaskCondition() + "\n");
        msg.append("任务详情：");
        msg.append(taskItem.getTaskDesc() + "\n");
        if (CollectionUtils.isNotEmpty(taskItem.getChildTasks())) {
            msg.append("子任务列表：" + "\n");
            int childIndex = 1;
            for (TaskItem childTask : taskItem.getChildTasks()) {
                appendTaskList(msg, childTask, String.format("%s.%s", index, childIndex++));
            }
        }
    }
}

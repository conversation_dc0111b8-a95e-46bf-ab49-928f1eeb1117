package com.sankuai.qpro.ai.professor.application.prompt;

import com.meituan.mdp.boot.starter.config.annotation.MdpConfig;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2025/1/1
 */
@Component
public class ProbeAgentPrompt {

    @MdpConfig("PROBE_ROUTE_REASONING_PROMPT")
    public String PROBE_ROUTE_REASONING_PROMPT;

    @MdpConfig("PROBE_ROUTE_REASONING_REVISE_PROMPT")
    public String PROBE_ROUTE_REASONING_REVISE_PROMPT;

    @MdpConfig("PROBE_ROUTE_PARAM_EXTRACT_PROMPT")
    public String PROBE_ROUTE_PARAM_EXTRACT_PROMPT;

    @MdpConfig("PROBE_PARAM_PROMPT")
    public String PROBE_PARAM_PROMPT;

    @MdpConfig("PROBE_PARAM_ONLINE_STRATEGY_CONCLUSION_PROMPT")
    public String PROBE_PARAM_ONLINE_STRATEGY_CONCLUSION_PROMPT;

    @MdpConfig("PROBE_CONF_PROMPT")
    public String PROBE_CONF_PROMPT;

    @MdpConfig("PROBE_CONF_STRUCTURE_PROMPT")
    public String PROBE_CONF_STRUCTURE_PROMPT;

    @MdpConfig("SHELF_SUB_TITLE_GENERATE_PROMPT")
    public String SHELF_SUB_TITLE_GENERATE_PROMPT;

    @MdpConfig("PROBE_STRUCTURE_PROMPT")
    public String PROBE_STRUCTURE_PROMPT;

    @MdpConfig("PROBE_VALIDATE_DSL_PROMPT")
    public String PROBE_VALIDATE_DSL_PROMPT;

    @MdpConfig("PROBE_REGENERATE_PROMPT")
    public String PROBE_REGENERATE_PROMPT;

    @MdpConfig("PROBE_GENERATE_SUMMARY_PROMPT")
    public String PROBE_GENERATE_SUMMARY_PROMPT;

    @MdpConfig("PROBE_GENERATE_TEST_CASE_PROMPT")
    public String PROBE_GENERATE_TEST_CASE_PROMPT;
}

package com.sankuai.qpro.ai.professor.application.prompt;

import com.meituan.mdp.boot.starter.config.annotation.MdpConfig;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2025/1/2
 */
@Component
public class ProbeOutputPrompt {

    @MdpConfig("SHELF_PARAM_COMPONENT")
    public String SHELF_PARAM_COMPONENT;

    @MdpConfig("PROBE_EXP_EXAMPLE")
    public String PROBE_EXP_EXAMPLE;

    @MdpConfig("PROBE_CONF_EXAMPLE")
    public String PROBE_CONF_EXAMPLE;

    @MdpConfig("SHELF_GENERATE_COMPONENT")
    public String SHELF_GENERATE_COMPONENT;

    @MdpConfig("PROBE_GENERATE_SUMMARY_INTRODUCE")
    public String PROBE_GENERATE_SUMMARY_INTRODUCE;
}

package com.sankuai.qpro.ai.professor.application.service.codegenerate.core;

import com.google.common.collect.Lists;
import com.sankuai.meituan.auth.util.UserUtils;
import com.sankuai.meituan.auth.vo.User;
import com.sankuai.qpro.ai.professor.api.enums.CodePromptSourceEnum;
import com.sankuai.qpro.ai.professor.api.enums.CodePromptStageEnum;
import com.sankuai.qpro.ai.professor.api.request.CreateCodePromptTaskReq;
import com.sankuai.qpro.ai.professor.api.response.CodePromptTaskModel;
import com.sankuai.qpro.ai.professor.api.constants.PromptTaskAttrConstant;
import com.sankuai.qpro.ai.professor.api.response.RequirementReviewInfoModel;
import com.sankuai.qpro.ai.professor.application.utils.SerializeUtils;
import com.sankuai.qpro.ai.professor.application.utils.TimeUtils;
import com.sankuai.qpro.ai.professor.entity.CodePromptTaskEntity;
import com.sankuai.qpro.ai.professor.repository.CodePromptTaskRepository;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * @author: wuwenqiang
 * @create: 2025-06-03
 * @description:
 */
@Service
@Slf4j
public class CodePromptTaskManageService {

    @Resource
    private CodePromptTaskRepository codePromptTaskRepository;

    @Transactional(rollbackFor = Exception.class)
    public Long createCodePromptTask(String userId, CreateCodePromptTaskReq request) {
        int source = getSource(request);
        CodePromptTaskEntity taskEntity = buildBaseTaskEntity(userId, source, request.getTitle());
        boolean result = codePromptTaskRepository.insertCodePromptTask(taskEntity);
        if (!result) {
            throw new RuntimeException("创建代码生成任务失败");
        }
        return taskEntity.getId();
    }

    @Transactional(rollbackFor = Exception.class)
    public boolean updateCodePromptTask(Long taskId, Integer stage, Map<String, String> extraData) {
        if (taskId == null || taskId <= 0) {
            return false;
        }
        // 1. 查询任务信息
        CodePromptTaskEntity taskEntity = codePromptTaskRepository.queryCodePromptTaskById(taskId);
        if (taskEntity == null) {
            throw new RuntimeException("任务不存在");
        }
        // 2. 更新任务所处阶段
        if (isStageChanged(taskEntity, stage)) {
            taskEntity.setStage(stage);
        }
        // 3. 更新任务关联数据
        taskEntity.setExtraData(extraData);
        return codePromptTaskRepository.updateCodePromptTask(taskEntity);
    }

    public CodePromptTaskModel queryCodePromptTaskById(Long taskId, String userId) {
        if (taskId == null || taskId <= 0 || StringUtils.isBlank(userId)) {
            return null;
        }
        // 1. 查询任务信息
        CodePromptTaskEntity taskEntity = codePromptTaskRepository.queryCodePromptTaskById(taskId);
        // 2. 校验任务和用户关系
        if (taskEntity == null || !userId.equals(taskEntity.getUserId())) {
            return null;
        }
        return buildCodePromptTaskModel(taskEntity);
    }

    public Long queryCodePromptTaskCountByUserId(String userId) {
        if (StringUtils.isBlank(userId)) {
            return 0l;
        }
        return codePromptTaskRepository.countCodePromptsTaskByUserId(userId);
    }

    public List<CodePromptTaskModel> queryCodePromptTaskByUserId(String userId, int page, int size) {
        if (StringUtils.isBlank(userId)) {
            return Lists.newArrayList();
        }
        int offset = page * size;
        List<CodePromptTaskEntity> taskEntities = codePromptTaskRepository.queryCodePromptsTaskByUserIdAndPage(userId, offset, size);
        if (CollectionUtils.isEmpty(taskEntities)) {
            return Lists.newArrayList();
        }
        return taskEntities.stream().map(this::buildCodePromptTaskModel).filter(Objects::nonNull).collect(Collectors.toList());
    }

    private CodePromptTaskEntity buildBaseTaskEntity(String userId, int source, String title) {
        CodePromptTaskEntity result = new CodePromptTaskEntity();
        result.setUserId(userId);
        result.setTitle(title);
        result.setSource(source);
        result.setStage(CodePromptStageEnum.EXTRACT_REQUIREMENT.getOrder());
        return result;
    }

    private int getSource(CreateCodePromptTaskReq request) {
        if (request == null || StringUtils.isBlank(request.getSource())) {
            return CodePromptSourceEnum.WEB.getType();
        }
        return Optional.ofNullable(CodePromptSourceEnum.getByTag(request.getSource())).
                orElse(CodePromptSourceEnum.WEB).getType();
    }

    private boolean isStageChanged(CodePromptTaskEntity taskEntity, Integer stage) {
        if (taskEntity == null || stage == null || stage <= 0) {
            return false;
        }
        return taskEntity.getStage() != stage;
    }

    private CodePromptTaskModel buildCodePromptTaskModel(CodePromptTaskEntity taskEntity) {
        if (taskEntity == null || taskEntity.getId() == null || taskEntity.getId() <= 0) {
            return null;
        }
        CodePromptTaskModel result = new CodePromptTaskModel();
        result.setTaskId(taskEntity.getId());
        result.setTitle(taskEntity.getTitle());
        result.setSource(getSourceType(taskEntity.getSource()));
        result.setStage(getStageType(taskEntity.getStage()));
        result.setCreateTime(TimeUtils.convertTimeStamp2DateStr(taskEntity.getCreateTime()));
        fillExtraData(result, taskEntity);
        return result;
    }

    private String getSourceType(int source) {
        CodePromptSourceEnum sourceEnum = CodePromptSourceEnum.getByType(source);
        if (sourceEnum == null) {
            return CodePromptSourceEnum.WEB.getTag();
        }
        return sourceEnum.getTag();
    }

    private String getStageType(int stage) {
        CodePromptStageEnum stageEnum = CodePromptStageEnum.getByOrder(stage);
        if (stageEnum == null) {
            return StringUtils.EMPTY;
        }
        return stageEnum.getTag();
    }

    private void fillExtraData(CodePromptTaskModel taskModel, CodePromptTaskEntity taskEntity) {
        if (taskModel == null || taskEntity == null || MapUtils.isEmpty(taskEntity.getExtraData())) {
            return;
        }
        Map<String, String> extraData = taskEntity.getExtraData();
        taskModel.setPrdUrl(extraData.get(PromptTaskAttrConstant.PRD_URL));
        taskModel.setReviewDocUrl(extraData.get(PromptTaskAttrConstant.REVIEW_DOC_URL));
        taskModel.setRequirementSummary(extraData.get(PromptTaskAttrConstant.REQUIREMENT_SUMMARY));
        taskModel.setKmCookieValue(extraData.get(PromptTaskAttrConstant.KM_COOKIE_VALUE));
        taskModel.setScene(extraData.get(PromptTaskAttrConstant.SCENE));
        taskModel.setUserInput(extraData.get(PromptTaskAttrConstant.USER_INPUT));
        if (StringUtils.isNotBlank(extraData.get(PromptTaskAttrConstant.REQUIREMENT_REVIEW_INFO))) {
            taskModel.setRequirementReviewInfo(SerializeUtils.toObj(extraData.get(PromptTaskAttrConstant.REQUIREMENT_REVIEW_INFO), RequirementReviewInfoModel.class));
        }
    }
}

package com.sankuai.qpro.ai.professor.application.service.codegenerate.core;

import com.dianping.zebra.util.StringUtils;
import com.sankuai.qpro.ai.professor.api.constants.PromptTaskAttrConstant;
import com.sankuai.qpro.ai.professor.api.enums.CodePromptStageEnum;
import com.sankuai.qpro.ai.professor.api.enums.ResponseCodeEnum;
import com.sankuai.qpro.ai.professor.api.request.ExtractRequirementDocRequest;
import com.sankuai.qpro.ai.professor.api.response.RemoteResponse;
import com.sankuai.qpro.ai.professor.application.constants.KmDocConstant;
import com.sankuai.qpro.ai.professor.application.constants.RequestContextConstant;
import com.sankuai.qpro.ai.professor.application.model.codegenerate.RequestContext;
import com.sankuai.qpro.ai.professor.application.model.codegenerate.RequirementReviewInfo;
import com.sankuai.qpro.ai.professor.application.service.codegenerate.proxy.CookieManager;
import com.sankuai.qpro.ai.professor.application.service.codegenerate.tools.ExtractRequirementDocTool;
import com.sankuai.qpro.ai.professor.application.service.codegenerate.tools.ExtractKmDocMcpTool;
import com.sankuai.qpro.ai.professor.api.request.ExtractKmDocWithImageReq;
import com.sankuai.qpro.ai.professor.application.utils.HeaderUtils;
import com.sankuai.qpro.ai.professor.application.utils.SerializeUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Map;

/**
 * @author: wuwenqiang
 * @create: 2025-05-29
 * @description:
 */
@Service
public class ExtractRequirementDocService {

    @Resource
    private ExtractRequirementDocTool extractRequirementDocTool;

    @Resource
    private CookieManager cookieManager;

    @Resource
    private CodePromptTaskManageService codePromptTaskManageService;

    @Resource
    private ExtractKmDocMcpTool extractKmDocMcpTool;

    public RemoteResponse<String> extractRequirementDoc(ExtractRequirementDocRequest req) {
        if (!checkTaskId(req)) {
            return RemoteResponse.invalidParam("taskId不能为空");
        }
        if (!checkParam(req)) {
            return RemoteResponse.invalidParam("PRD链接和需求评审记录不能同时为空");
        }
        // 1. 需求抽取
        // 设置学城cookie
        RequestContext.init();
        RequestContext.setAttribute(RequestContextConstant.TASK_ID, String.valueOf(req.getTaskId()));
        cookieManager.setCookie(String.valueOf(req.getTaskId()), KmDocConstant.KM_SSO_COOKIE_NAME, req.getKmCookieValue());
        // 输入需求评审记录为空
        if (StringUtils.isBlank(req.getReviewDocUrl())) {
            String result = extractRequirementDocTool.extractPRDSpecialSceneDoc(req.getPrdUrl(), req.getScene(), req.getUserInput());
            saveRequirementInfo(req, Pair.of(result, null));
            return processResult(result);
        }
        // 输入PRD链接为空
        if (StringUtils.isBlank(req.getPrdUrl())) {
            Pair<String, RequirementReviewInfo> result = extractRequirementDocTool.extractReviewDocAndInfo(req.getReviewDocUrl(), req.getUserInput());
            saveRequirementInfo(req, result);
            if (result == null || StringUtils.isBlank(result.getLeft()) || result.getRight() == null) {
                return RemoteResponse.fail(ResponseCodeEnum.SERVER_ERROR, "需求文档提取失败");
            }
            return processResult(result.getLeft());
        }
        Pair<String, RequirementReviewInfo> result = extractRequirementDocTool.generateRequirementPromptWithReviewInfo(req.getPrdUrl(), req.getReviewDocUrl(), req.getScene(), req.getUserInput());
        if (result == null || StringUtils.isBlank(result.getLeft()) || result.getRight() == null) {
            String msg = result != null && StringUtils.isNotBlank(result.getLeft()) ? result.getLeft() : "需求文档提取失败";
            return RemoteResponse.fail(ResponseCodeEnum.SERVER_ERROR, msg);
        }
        // 2. 写入数据库
        saveRequirementInfo(req, result);
        return processResult(result.getLeft());
    }

    private RemoteResponse<String> processResult(String result) {
        if (StringUtils.isNotBlank(result) && result.contains("异常")) {
            return RemoteResponse.fail(ResponseCodeEnum.SERVER_ERROR, result);
        }
        return RemoteResponse.success(result);
    }

    private boolean checkTaskId(ExtractRequirementDocRequest req) {
        return req != null && req.getTaskId() != null && req.getTaskId() > 0;
    }

    private boolean checkParam(ExtractRequirementDocRequest req) {
        return StringUtils.isNotBlank(req.getPrdUrl()) || StringUtils.isNotBlank(req.getReviewDocUrl());
    }

    private void saveRequirementInfo(ExtractRequirementDocRequest req, Pair<String, RequirementReviewInfo> result) {
        String resultStr = result.getLeft();
        String requirementDocReqStr = SerializeUtils.toJsonStr(req);
        Map<String, String> extraMap = SerializeUtils.toObj(requirementDocReqStr, Map.class);
        if (MapUtils.isEmpty(extraMap) || StringUtils.isBlank(resultStr) || resultStr.contains("异常")) {
            return;
        }
        String reviewInfoStr = result.getRight() == null ? "" : SerializeUtils.toJsonStr(result.getRight());
        extraMap.put(PromptTaskAttrConstant.REQUIREMENT_REVIEW_INFO, reviewInfoStr);
        extraMap.put(PromptTaskAttrConstant.REQUIREMENT_SUMMARY, resultStr);
        // 移除taskId
        extraMap.remove("taskId");
        codePromptTaskManageService.updateCodePromptTask(req.getTaskId(), CodePromptStageEnum.EXTRACT_REQUIREMENT.getOrder(), extraMap);
    }

    /**
     * 提取学城文档（包含图片）
     * @param req 提取请求
     * @return 提取结果
     */
    public RemoteResponse<String> extractKmDocWithImage(ExtractKmDocWithImageReq req) {
        try {
            return extractKmDocMcpTool.extractDocTool(req.getUrl(), req.getUserInput());
        } catch (Exception e) {
            return RemoteResponse.fail(ResponseCodeEnum.SERVER_ERROR, "提取学城文档异常: " + e.getMessage());
        }
    }
}

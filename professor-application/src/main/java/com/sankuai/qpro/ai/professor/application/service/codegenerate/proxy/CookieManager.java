package com.sankuai.qpro.ai.professor.application.service.codegenerate.proxy;

import com.google.common.collect.Maps;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;

import java.util.Map;
import org.springframework.stereotype.Component;

/**
 * @author: wu<PERSON><PERSON><PERSON>
 * @create: 2025-05-21
 * @description: cookie管理器
 */
@Component
@Slf4j
public class CookieManager {
    // Todo：后续将每个用户的cookies都存储在分布式缓存中，当前的临时解决方案是共用同一个
    // 业务id（可以为会话ID、也可以为userId） -> cookieName -> cookieValue
    private final Map<String, Map<String, CookieValue>> biz2CookieCache = Maps.newHashMap();
    // 设置cookie的过期时间为24小时。
    private final int ttl = 60 * 60 * 24;

    public String getCookie(String bizId, String cookieName) {
        if (MapUtils.isEmpty(biz2CookieCache.get(bizId)) || !biz2CookieCache.get(bizId).containsKey(cookieName)) {
            return null;
        }
        CookieValue cookieValue = biz2CookieCache.get(bizId).get(cookieName);
        if (cookieValue.getExpireTime() < System.currentTimeMillis()) {
            return null;
        }
        return cookieValue.getCookieValue();
    }

    public void setCookie(String bizId, String cookieName, String cookieValue) {
        if (MapUtils.isEmpty(biz2CookieCache.get(bizId))) {
            biz2CookieCache.put(bizId, Maps.newHashMap());
        }
        Map<String, CookieValue> cookieMap = biz2CookieCache.get(bizId);
        cookieMap.put(cookieName, new CookieValue(cookieValue, System.currentTimeMillis() + ttl * 1000L));
        biz2CookieCache.put(bizId, cookieMap);
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class CookieValue {
        private String cookieValue;
        private long expireTime;
    }
}

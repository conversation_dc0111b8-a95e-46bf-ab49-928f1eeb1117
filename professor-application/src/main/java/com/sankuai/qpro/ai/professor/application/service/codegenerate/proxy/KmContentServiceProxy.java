package com.sankuai.qpro.ai.professor.application.service.codegenerate.proxy;

import com.fasterxml.jackson.databind.JsonNode;
import com.google.common.collect.Maps;
import com.sankuai.dxenterprise.open.gateway.service.citadel.api.CitadelService;
import com.sankuai.qpro.ai.professor.api.enums.ResponseCodeEnum;
import com.sankuai.qpro.ai.professor.api.response.RemoteResponse;
import com.sankuai.qpro.ai.professor.application.constants.KmDocConstant;
import com.sankuai.qpro.ai.professor.application.model.codegenerate.KmJsonData;
import com.sankuai.qpro.ai.professor.application.utils.DocumentUtils;
import com.sankuai.qpro.ai.professor.application.utils.HeaderUtils;
import com.sankuai.qpro.ai.professor.application.utils.HttpUtils;
import com.sankuai.qpro.ai.professor.application.utils.SerializeUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Map;
import java.util.Optional;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * @author: wuwenqiang
 * @create: 2025-05-20
 * @description: 学城API
 */
@Component
@Slf4j
public class KmContentServiceProxy {
    @Resource
    private CitadelService citadelService;

    @Resource
    private CookieManager cookieManager;
    private static final String COOKIE_FOMAT = "com.sankuai.it.ead.citadel_ssoid=%s";
    private static final String QUERY_CONTENT_URL_FORMAT = "https://km.sankuai.com/api/pages/new/%s?queryType=0";

    public RemoteResponse<String> readContent(String bizId, String url) {
        // 1.验证URL格式
        String validateMsg = validateUrl(url);
        if (StringUtils.isNotBlank(validateMsg)) {
            return RemoteResponse.fail(ResponseCodeEnum.SERVER_ERROR, validateMsg);
        }
        // 2.读取KM系统的cookie
        String cookieValue = cookieManager.getCookie(bizId, KmDocConstant.KM_SSO_COOKIE_NAME);
        if (StringUtils.isBlank(cookieValue)) {
            return RemoteResponse.fail(ResponseCodeEnum.SERVER_ERROR, "读取cookie失败，请重新提供cookie");
        }
        // 3.构造headers
        Map<String, String> headers = HeaderUtils.buildSSOHeaders(cookieValue);
        // 3.提取数据
        return readDataFromUrlAndHeaders(url, headers);
    }

    public RemoteResponse<String> readContentByCookie(String url, String cookieValue) {
        // 1.验证URL格式
        String validateMsg = validateUrl(url);
        if (StringUtils.isNotBlank(validateMsg)) {
            return RemoteResponse.fail(ResponseCodeEnum.SERVER_ERROR, validateMsg);
        }
        // 2. 验证cookie
        if (StringUtils.isBlank(cookieValue)) {
            return RemoteResponse.fail(ResponseCodeEnum.SERVER_ERROR, "cookie不能为空");
        }
        Map<String, String> headers = HeaderUtils.buildNormalHeaders(cookieValue);
        return readDataFromUrlAndHeaders(url, headers);
    }

    private String validateUrl(String url) {
        if (StringUtils.isBlank(url)) {
            return "URL不能为空";
        }

        // 验证URL格式
        Pattern pattern = Pattern.compile("^https://km\\.sankuai\\.com/collabpage/\\d+(#.*)?$");
        Matcher matcher = pattern.matcher(url);
        if (!matcher.matches()) {
            return "URL格式不正确, 期望格式：https://km.sankuai.com/collabpage/{number}";
        }
        return null;
    }

    private RemoteResponse<String> readDataFromUrlAndHeaders(String url, Map<String, String> headers) {
        // 从URL中提取页面ID并直接访问KM API
        String baseUrl = url.split("#")[0];
        String contentId = extractContentId(baseUrl);
        String queryUrl = String.format(QUERY_CONTENT_URL_FORMAT, contentId);
        RemoteResponse resp = HttpUtils.get(queryUrl, headers);
        if (!resp.isSuccess()) {
            log.info("[KmContentServiceProxy] query km content error, queryUrl={}, resp={}", queryUrl, SerializeUtils.toJsonStr(resp));
            return RemoteResponse.fail(ResponseCodeEnum.SERVER_ERROR, "访问学城接口失败");
        }
        String data = (String) Optional.ofNullable(resp.getData()).orElse("");
        try {
            if (StringUtils.isNotBlank(data) && !data.startsWith("{")) {
                return RemoteResponse.fail(ResponseCodeEnum.SERVER_ERROR, "当前cookie已失效，请替换新的cookie后重试");
            }
            JsonNode jsonNode = SerializeUtils.readTree(data);
            JsonNode dataNode = jsonNode.get("data");
            if (dataNode == null) {
                return RemoteResponse.fail(ResponseCodeEnum.SERVER_ERROR, "查询学城文档返回数据为空");
            }
            return RemoteResponse.success(dataNode.toString());
        } catch (Exception e) {
            log.error("[KmContentServiceProxy] readDataFromUrlAndHeaders error, url={}, headers={}", url, SerializeUtils.toJsonStr(headers), e);
        }
        return RemoteResponse.fail(ResponseCodeEnum.SERVER_ERROR, "解析学城文档返回数据失败");
    }

    private String extractContentId(String baseUrl) {
        // 移除基础URL部分
        String remaining = baseUrl.replace("https://km.sankuai.com/collabpage/", "");

        // 处理可能的查询参数，提取数字部分
        String contentId;
        if (remaining.contains("?")) {
            // 如果包含查询参数，只取?之前的部分
            contentId = remaining.split("\\?")[0];
        } else {
            contentId = remaining;
        }

        // 移除可能的斜杠
        contentId = contentId.replaceAll("/", "");

        return contentId;
    }

    /**
     * 从学城文档中提取所有文本
     * @param data 学城文档json格式数据
     * @return
     */
    public String extractAllContent(String data) {
        KmJsonData rootNode = extractContentNode(data);
        if (rootNode == null) {
            return StringUtils.EMPTY;
        }
        return DocumentUtils.extractAllText(rootNode);
    }

    /**
     * 提取特定场景下的文本
     * @param data 学城文档json格式数据
     * @param scene 场景：货架/团详
     * @return
     */
    public String extractContentForSpecialScene(String data, String scene) {
        KmJsonData rootNode = extractContentNode(data);
        if (rootNode == null) {
            return StringUtils.EMPTY;
        }
        KmJsonData filteredNode = DocumentUtils.filterForSpecialScene(rootNode, scene);
        // 如果没有则降级为解析全量文档
        if (filteredNode == null) {
            return DocumentUtils.extractAllText(rootNode);
        }
        return DocumentUtils.extractAllText(filteredNode);
    }

    private KmJsonData extractContentNode(String data) {
        if (StringUtils.isBlank(data)) {
            return null;
        }
        try {
            JsonNode jsonNode = SerializeUtils.readTree(data);
            if (jsonNode == null) {
                return null;
            }
            String body = jsonNode.get("body") == null ? StringUtils.EMPTY : jsonNode.get("body").asText();
            KmJsonData rootNode = SerializeUtils.toObj(body, KmJsonData.class);
            return rootNode;
        } catch (Exception e) {
            log.error("[DocumentUtils] extractContentNode error, data={}", data, e);
        }
        return null;
    }



    public static void main(String[] args) throws Exception {
        String url = "https://km.sankuai.com/collabpage/2172773865";
        KmContentServiceProxy proxy = new KmContentServiceProxy();
        String cookieValue = "com.sankuai.it.ead.citadel_ssoid=eAGFjr1KBDEYAI3XHNgcllYp5Yol-ZJsEisvrouloCjY5edbWU53i11f4MrDSt9AEOxFS0sray19AgWRK-z0nsBqmmGYIRm9374O6NPP2-MXwEZsz7PON9MLX2d1n6FPWax7n_BsizJhPVMpqCokmVvuhQ9jqXTyUUSpK3dN1mkpnHOlLoyxSpWOuZ2iAGZ3hdUTCQWjd9_PLwvYXIV_e2Z5tr22t3i4_PiE_fn9EjOyckWGxxgOIjZ4QyhHKyGPggtdyQBgOCphKwgxaW2FOqGsEqhS7rkd_6lGGgaSW-G11kEB2hkZnWI_iRG77rCdYnMEczLouvYXLK1ahQ**eAENysEBwCAIA8CVIAIx40Cr-4_Q3vuU8GTLNNWH2EE_q57FmkO-5ml_QSyMxTYW5EpAmfK-H_lhD0w**UnFaqZFicNc_rYzgPsaXEi8Ya-U1fKUBRVhebY2zppqIy5Auno8KpA6cd_pLiqDRrXi_cf-pg7vmLx2OarPS7A**NzcwNTMxMSx3dXdlbnFpYW5nMDYs5LyN5rip5by6LHd1d2VucWlhbmcwNkBtZWl0dWFuLmNvbSwxLDM0MDAxNzA0LDE3NDg0MzQ4MTIwOTQ";
        RemoteResponse<String> contentResp = proxy.readContentByCookie(url, cookieValue);
        if (!contentResp.isSuccess()) {
            System.out.println(contentResp.getMsg());
            return;
        }
        String content = contentResp.getData();
//        String result = proxy.extractContentForSpecialScene(content, "货架");
        String result = proxy.extractAllContent(content);
        System.out.println(result);
    }

}

package com.sankuai.qpro.ai.professor.application.service.codegenerate.proxy;

import com.sankuai.qpro.ai.professor.api.enums.ResponseCodeEnum;
import com.sankuai.qpro.ai.professor.api.response.RemoteResponse;
import com.sankuai.qpro.ai.professor.application.model.codegenerate.ImageContent;
import com.sankuai.qpro.ai.professor.application.utils.HeaderUtils;
import com.sankuai.qpro.ai.professor.application.utils.HttpUtils;
import lombok.extern.slf4j.Slf4j;
import net.coobird.thumbnailator.Thumbnails;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.Base64;
import java.util.Map;
import java.util.Optional;

/**
 * @author: wuwenqiang
 * @create: 2025-07-01
 * @description: 学城图片服务代理
 */
@Component
@Slf4j
public class KmImageServiceProxy {

    @Resource
    private CookieManager cookieManager;

    public RemoteResponse<String> readImageBase64ByCookie(String url, String cookieValue) {
        RemoteResponse<Pair<String, String>> resp = readImageFormatAndDataByCookie(url, cookieValue);
        if (!validateResp(resp)) {
            return buildFailResp(resp);
        }
        return RemoteResponse.success(resp.getData().getRight());
    }

    public RemoteResponse<ImageContent> readImageContentByCookie(String url, String cookieValue) {
        RemoteResponse<Pair<String, String>> resp = readImageFormatAndDataByCookie(url, cookieValue);
        if (!validateResp(resp)) {
            return buildFailResp(resp);
        }
        String format = resp.getData().getLeft();
        String data = resp.getData().getRight();
        ImageContent imageContent = ImageContent.of(data, format);
        return RemoteResponse.success(imageContent);
    }

    /**
     * 读取图片内容并进行压缩
     * @param url 图片URL
     * @param cookieValue cookie值
     * @param compressionLevel 压缩级别 (1-3)
     * @return 压缩后的图片内容
     */
    public RemoteResponse<ImageContent> readImageContentByCookieWithCompression(String url, String cookieValue, int compressionLevel) {
        RemoteResponse<Pair<String, String>> resp = readImageFormatAndDataByCookieWithCompression(url, cookieValue, compressionLevel);
        if (!validateResp(resp)) {
            return buildFailResp(resp);
        }
        String format = resp.getData().getLeft();
        String data = resp.getData().getRight();
        ImageContent imageContent = ImageContent.of(data, format);
        return RemoteResponse.success(imageContent);
    }

    private RemoteResponse<Pair<String, String>> readImageFormatAndDataByCookie(String url, String cookieValue) {
        RemoteResponse<Pair<String, byte[]>> validatedResp = validateAndGetImageData(url, cookieValue);
        if (!validatedResp.isSuccess()) {
            return buildFailResp(validatedResp);
        }

        Pair<String, byte[]> data = validatedResp.getData();
        String format = extractImgFormat(data.getLeft());
        return RemoteResponse.success(Pair.of(format, encodeToBase64(data.getRight())));
    }

    private RemoteResponse<Pair<String, String>> readImageFormatAndDataByCookieWithCompression(String url, String cookieValue, int compressionLevel) {
        RemoteResponse<Pair<String, byte[]>> validatedResp = validateAndGetImageData(url, cookieValue);
        if (!validatedResp.isSuccess()) {
            return buildFailResp(validatedResp);
        }

        Pair<String, byte[]> data = validatedResp.getData();
        String format = extractImgFormat(data.getLeft());
        
        // 处理图片压缩
        byte[] compressedImageBytes = processImageCompression(data.getRight(), format, compressionLevel);
        return RemoteResponse.success(Pair.of(format, encodeToBase64(compressedImageBytes)));
    }

    /**
     * 验证并获取图片数据的公共方法
     * @param url 图片URL
     * @param cookieValue cookie值
     * @return 验证后的图片数据响应
     */
    private RemoteResponse<Pair<String, byte[]>> validateAndGetImageData(String url, String cookieValue) {
        RemoteResponse<Pair<String, byte[]>> resp = getImageBytes(url, cookieValue);

        if (!resp.isSuccess()) {
            return RemoteResponse.fail(ResponseCodeEnum.SERVER_ERROR, "访问学城图片接口失败");
        }

        Pair<String, byte[]> data = Optional.ofNullable(resp.getData()).orElse(null);
        if (data == null) {
            return RemoteResponse.fail(ResponseCodeEnum.SERVER_ERROR, "学城图片接口返回为空");
        }

        String format = extractImgFormat(data.getLeft());
        if (!validateFormat(format)) {
            return RemoteResponse.fail(ResponseCodeEnum.SERVER_ERROR, "学城图片接口返回格式不正确，当前格式为：" + format);
        }

        return RemoteResponse.success(data);
    }

    private RemoteResponse<Pair<String, byte[]>> getImageBytes(String url, String cookieValue) {
        Map<String, String> headers = HeaderUtils.buildNormalHeaders(cookieValue);
        return HttpUtils.getBytes(url, headers);
    }

    private String encodeToBase64(byte[] bytes) {
        return Base64.getEncoder().encodeToString(bytes);
    }

    private String extractImgFormat(String contentType) {
        boolean isImage = StringUtils.isNotEmpty(contentType) && contentType.startsWith("image/");
        return isImage ? contentType.split("/")[1].toUpperCase() : "";
    }

    private boolean validateFormat(String format) {
        if (StringUtils.isEmpty(format)) {
            return false;
        }
        boolean isSvgXml = format.toLowerCase().contains("svg") || format.toLowerCase().contains("xml");
        return !isSvgXml;
    }

    private boolean validateResp(RemoteResponse<Pair<String, String>> resp) {
        return resp != null && resp.isSuccess() && resp.getData() != null;
    }

    private <T> RemoteResponse<T> buildFailResp(RemoteResponse<?> resp) {
        String msg = resp == null ? "访问学城图片接口失败" : resp.getMsg();
        return RemoteResponse.fail(ResponseCodeEnum.SERVER_ERROR, msg);
    }

    /**
     * 处理图片压缩，参考Python的图片压缩逻辑
     * @param imageBytes 原始图片字节数组
     * @param format 图片格式
     * @param compressionLevel 压缩级别 (1-3)
     * @return 压缩后的图片字节数组
     */
    private byte[] processImageCompression(byte[] imageBytes, String format, int compressionLevel) {
        try {
            // 读取原始图片
            BufferedImage originalImage = ImageIO.read(new ByteArrayInputStream(imageBytes));
            if (originalImage == null) {
                log.warn("无法读取图片，返回原始字节数组");
                return imageBytes;
            }

            // 根据压缩级别设置参数
            float quality;
            int maxWidth, maxHeight;

            switch (compressionLevel) {
                case 1:
                    quality = 0.85f;
                    maxWidth = maxHeight = 1600;
                    break;
                case 2:
                    quality = 0.70f;
                    maxWidth = maxHeight = 1024;
                    break;
                default: // compressionLevel >= 3
                    quality = 0.50f;
                    maxWidth = maxHeight = 640;
                    break;
            }

            // 使用Thumbnailator进行图片压缩和缩放
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();

            // 判断是否需要缩放
            boolean needResize = originalImage.getWidth() > maxWidth || originalImage.getHeight() > maxHeight;

            if (needResize) {
                // 需要缩放
                if (isLossyFormat(format)) {
                    // 有损格式（JPEG、WEBP等）设置质量
                    Thumbnails.of(originalImage)
                            .size(maxWidth, maxHeight)
                            .outputQuality(quality)
                            .outputFormat(format.toLowerCase())
                            .toOutputStream(outputStream);
                } else {
                    // 无损格式（PNG等）不设置质量
                    Thumbnails.of(originalImage)
                            .size(maxWidth, maxHeight)
                            .outputFormat(format.toLowerCase())
                            .toOutputStream(outputStream);
                }
            } else {
                // 不需要缩放，只调整质量
                if (isLossyFormat(format)) {
                    Thumbnails.of(originalImage)
                            .scale(1.0)
                            .outputQuality(quality)
                            .outputFormat(format.toLowerCase())
                            .toOutputStream(outputStream);
                } else {
                    // 无损格式直接返回原图
                    return imageBytes;
                }
            }

            byte[] compressedBytes = outputStream.toByteArray();
            log.info("图片压缩完成，原始大小: {} bytes, 压缩后大小: {} bytes, 压缩比: {:.2f}%",
                    imageBytes.length, compressedBytes.length,
                    (1.0 - (double)compressedBytes.length / imageBytes.length) * 100);

            return compressedBytes;

        } catch (IOException e) {
            log.warn("图片压缩失败，返回原图，错误: {}", e.getMessage());
            return imageBytes;
        }
    }

    /**
     * 判断是否为有损格式
     * @param format 图片格式
     * @return true表示有损格式
     */
    private boolean isLossyFormat(String format) {
        if (StringUtils.isEmpty(format)) {
            return false;
        }
        String lowerFormat = format.toLowerCase();
        return lowerFormat.contains("jpeg") || lowerFormat.contains("jpg") || lowerFormat.contains("webp");
    }

}

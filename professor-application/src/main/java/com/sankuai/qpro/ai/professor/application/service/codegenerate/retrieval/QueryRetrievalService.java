package com.sankuai.qpro.ai.professor.application.service.codegenerate.retrieval;

import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.meituan.mdp.boot.starter.config.annotation.MdpConfig;
import com.sankuai.qpro.ai.professor.application.model.codegenerate.PreQueryData;
import com.sankuai.qpro.ai.professor.application.model.codegenerate.PreQueryResult;
import com.sankuai.qpro.ai.professor.application.utils.SerializeUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.chat.messages.Message;
import org.springframework.ai.chat.prompt.SystemPromptTemplate;
import org.springframework.ai.tool.annotation.Tool;
import org.springframework.context.annotation.Lazy;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Service;
import org.springframework.util.StreamUtils;

import javax.annotation.Resource;
import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @author: wuwenqiang
 * @create: 2025-08-05
 * @description:
 */
@Service
public class QueryRetrievalService {

    @Resource
    @Lazy
    private ChatClient gpt4Dot1Client;

    @MdpConfig("CODE_GENERATE_PRE_RETRIEVAL_PROMPT")
    private String queryPreRetrievalPrompt;

    // Pre-Query-Retrieval
    public PreQueryResult queryPreRetrieval(String requirement) {
        if (StringUtils.isBlank(requirement)) {
            return null;
        }
        List<PreQueryData> preQueryData = getPreQueryData(requirement);
        if (CollectionUtils.isEmpty(preQueryData)) {
            return null;
        }
        List<PreQueryData> queryResults = preQueryData.stream()
                .filter(data -> data != null && StringUtils.isNotBlank(data.getKeyCodeEntrance()))
                .collect(Collectors.toList());
        List<PreQueryData> notFoundQueryResults = preQueryData.stream()
                .filter(data -> data != null && StringUtils.isBlank(data.getKeyCodeEntrance()))
                .collect(Collectors.toList());
        PreQueryResult result = new PreQueryResult();
        result.setQueryResults(queryResults);
        result.setNotFoundQueryResults(notFoundQueryResults);
        return result;
    }


    private List<PreQueryData> getPreQueryData(String requirement) {
        // 1. 获取基础分层知识
        Map<String, String> baseKnowledge = getBaseKnowledge();
        return gpt4Dot1Client.prompt().messages(buildMessage(requirement, baseKnowledge))
                .tools(new QueryRetrievalService())
                .call().entity(new ParameterizedTypeReference<List<PreQueryData>>() {});
    }

    private List<Message> buildMessage(String requirement, Map<String, String> baseKnowledge) {
        List<Message> messages = Lists.newArrayList();
        SystemPromptTemplate systemPromptTemplate = new SystemPromptTemplate(queryPreRetrievalPrompt);
        Message systemMessage = systemPromptTemplate.createMessage(Map.of("layerBaseKnowledge", SerializeUtils.toJsonStr(baseKnowledge), "requirement", requirement));
        messages.add(systemMessage);
        return messages;
    }

    private Map<String, String> getBaseKnowledge() {
        Map<String, String> result = Maps.newHashMap();
        result.put("流程分层", readFileContent("base_layer.md"));
//        result.put("节点依赖关系", "");
        return result;
    }

    private String readFileContent(String filePath) {
        try {
            ClassPathResource resource = new ClassPathResource(filePath);
            InputStream inputStream = resource.getInputStream();
            return StreamUtils.copyToString(inputStream, StandardCharsets.UTF_8);
        } catch (IOException e) {
            // 如果读取失败，返回空字符串或默认值
            return "";
        }
    }

    @Tool(name = "getAbilityKnowledge", description = "获取指定能力点的知识")
    private String getAbilityKnowledge(List<String> abilities) {
        String layerDataKnowledgeStr = readFileContent("layer_data_info.json");
        Map<String, Object> layerDataKnowledgeMap = SerializeUtils.toObj(layerDataKnowledgeStr, new TypeReference<Map<String, Object>>() {});
        if (CollectionUtils.isEmpty(abilities) || MapUtils.isEmpty(layerDataKnowledgeMap)) {
            return null;
        }
        Set<String> layerLowerSet = Sets.newHashSet();
        abilities.forEach(layer -> layerLowerSet.add(layer.toLowerCase()));
        Map<String, Object> filterLayerDataKnowledge = layerDataKnowledgeMap.entrySet().stream()
                .filter(entry -> entry != null && StringUtils.isNotBlank(entry.getKey())
                        && layerLowerSet.contains(entry.getKey().toLowerCase()))
//                .map(this::getLayerDataKnowledge)
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
        return SerializeUtils.toJsonStr(filterLayerDataKnowledge);
    }

//    // 特殊处理
//    private Map.Entry<String, String> getLayerDataKnowledge(Map.Entry<String, String> entry) {
//        if (entry == null || StringUtils.isBlank(entry.getKey()) || StringUtils.isNotBlank(entry.getValue())) {
//            return entry;
//        }
//        String value = entry.getValue();
//        Map<String, String> layerDataKnow = SerializeUtils.toObj(value, new TypeReference<Map<String, String>>() {});
//        if (MapUtils.isEmpty(layerDataKnow)) {
//            return entry;
//        }
//
//    }
}

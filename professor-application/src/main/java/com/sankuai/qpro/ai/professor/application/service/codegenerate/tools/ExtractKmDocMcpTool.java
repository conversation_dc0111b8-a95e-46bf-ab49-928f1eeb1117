package com.sankuai.qpro.ai.professor.application.service.codegenerate.tools;

import com.dianping.rhino.Rhino;
import com.dianping.rhino.threadpool.DefaultThreadPoolProperties;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.meituan.mdp.boot.starter.config.annotation.MdpConfig;
import com.sankuai.qpro.ai.professor.api.enums.ResponseCodeEnum;
import com.sankuai.qpro.ai.professor.api.response.RemoteResponse;
import com.sankuai.qpro.ai.professor.application.utils.HeaderUtils;
import com.sankuai.qpro.ai.professor.application.utils.SerializeUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.chat.messages.Message;
import org.springframework.ai.chat.messages.UserMessage;
import org.springframework.ai.chat.prompt.SystemPromptTemplate;
import org.springframework.ai.tool.annotation.Tool;
import org.springframework.ai.tool.annotation.ToolParam;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.stream.Collectors;

/**
 * @author: wuwenqiang
 * @create: 2025-07-03
 * @description: 提取学城MCP工具
 */
@Component
public class ExtractKmDocMcpTool {

    @Resource
    private ParseKmDocTool parseKmDocTool;

    @Resource
    private ReadKmImageTool readKmImageTool;

    @Resource
    @Lazy
    private ChatClient gpt4Dot1Client;

    @MdpConfig("CODE_GENERATE_SPECIAL_SCENE_DOC_PROMPT")
    private String specialSceneDocPrompt;

    @MdpConfig("CODE_GENERATE_EXTRACT_IMAGE_URL_PROMPT")
    private String extractImageUrlPrompt;

    @MdpConfig("CODE_GENERATE_MERGE_DOC_AND_IMAGE_PROMPT")
    private String mergeDocAndImagePrompt;

    @MdpConfig("CODE_GENERATE_EXTRACT_DOC_CONTENT_PROMPT")
    private String extractDocPrompt;

    private static final ExecutorService EXTRACT_KM_DOC_THREAD_POOL = Rhino.newThreadPool("extractKmDocMcpTool", new DefaultThreadPoolProperties.Setter()
            .withCoreSize(10).withMaxQueueSize(100).withMaxSize(20)).getExecutor();

    @Tool(name = "extract_km_doc_with_input", description = "基于用户输入提取特定学城文档信息")
    public RemoteResponse<String> extractDocTool(@ToolParam(description = "学城文档链接, 前缀为https://km.sankuai.com/collabpage") String url,  @ToolParam(description = "用户输入消息") String userInput) {
        // 1. 获取文档内容
        String cookieValue = HeaderUtils.getCookie();
        RemoteResponse<String> docContentResp = parseKmDocTool.queryDocument(url, cookieValue);
        if (docContentResp == null || !docContentResp.isSuccess() || StringUtils.isEmpty(docContentResp.getData())) {
            String msg = docContentResp == null ? "文档内容获取失败" : docContentResp.getMsg();
            return RemoteResponse.fail(ResponseCodeEnum.SERVER_ERROR, msg);
        }
        // 2. 提取检索内容
        // 2.1 如果用户输入为空，则直接返回全部文档内容
        if (StringUtils.isEmpty(userInput)) {
            return RemoteResponse.success(docContentResp.getData());
        }
        // 2.2 提取文档记录
        String result = getSpecialDoc(docContentResp.getData(), userInput);
        if (StringUtils.isEmpty(result)) {
            return RemoteResponse.success("文档内容为空");
        }
        // 3. 提取学城图片链接
        List<String> imageUrls = extractKmImageUrls(result);
        if (CollectionUtils.isEmpty(imageUrls)) {
            return RemoteResponse.success(result);
        }
        // 4. 解读图片内容
        Map<String, String> imageContentMap = explainKmImageContents(imageUrls, result, cookieValue);
        // 4. 合并文档内容
        if (MapUtils.isEmpty(imageContentMap)) {
            return RemoteResponse.success(result);
        }
        result = mergeDocContent(result, imageContentMap);
        return RemoteResponse.success(result);
    }

    private String getSpecialDoc(String docContent, String userInput) {
        List<Message> messages = buildMessagesForExtractSpecialContent(docContent, userInput);
        return gpt4Dot1Client.prompt().messages(messages)
                .tools(readKmImageTool, parseKmDocTool)
                .call().content();
    }

    private List<String> extractKmImageUrls(String docContent) {
        List<Message> messages = buildMessagesForExtractImageUrl(docContent);
        return gpt4Dot1Client.prompt().messages(messages)
                .call().entity(List.class);
    }

    private Map<String, String> explainKmImageContents(List<String> imageUrls, String docContent, String cookieValue) {
        if (CollectionUtils.isEmpty(imageUrls)) {
            return Maps.newHashMap();
        }
        Map<String, CompletableFuture<String>> futureMap = Maps.newHashMap();
        imageUrls.forEach(url -> {
            CompletableFuture<String> future = CompletableFuture.supplyAsync(() -> getImageExplainContent(url, docContent, cookieValue)
            , EXTRACT_KM_DOC_THREAD_POOL);
            futureMap.put(url, future);
        });
        if (MapUtils.isEmpty(futureMap)) {
            return Maps.newHashMap();
        }
        CompletableFuture<Map<String, String>> resultFuture = CompletableFuture.allOf(futureMap.values().toArray(new CompletableFuture[0]))
                .thenApply(avoid -> {
                    Map<String, String> resultMap = Maps.newHashMap();
                    futureMap.forEach((k, v) -> resultMap.put(k, v.join()));
                    return resultMap;
                });
        Map<String, String> result = resultFuture.join();
        if (MapUtils.isEmpty(result)) {
            return result;
        }
        // 过滤空值结果
        return result.entrySet().stream().filter(entry -> StringUtils.isNotBlank(entry.getValue()))
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
    }

    private String mergeDocContent(String docContent, Map<String, String> imageContentMap) {
        if (MapUtils.isEmpty(imageContentMap)) {
            return docContent;
        }
        List<Message> messages = buildMessageForMergeDocContent(docContent, imageContentMap);
        return gpt4Dot1Client.prompt().messages(messages)
                .call().content();
    }

    private List<Message> buildMessagesForExtractSpecialContent(String docContent, String userInput) {
        List<Message> messages = Lists.newArrayList();
        SystemPromptTemplate systemPromptTemplate = new SystemPromptTemplate(specialSceneDocPrompt);
        Message systemMessage = systemPromptTemplate.createMessage(Map.of("docContent", docContent));
        messages.add(systemMessage);
        if (StringUtils.isNotEmpty(userInput)) {
            messages.add(new UserMessage("检索要求: " + userInput));
        }
        return messages;
    }

    private List<Message> buildMessagesForExtractImageUrl(String docContent) {
        List<Message> messages = Lists.newArrayList();
        SystemPromptTemplate systemPromptTemplate = new SystemPromptTemplate(extractImageUrlPrompt);
        Message systemMessage = systemPromptTemplate.createMessage(Map.of("docContent", docContent));
        messages.add(systemMessage);
        return messages;
    }

    private String getImageExplainContent(String imageUrl, String userInput, String cookieValue) {
        if (StringUtils.isEmpty(imageUrl)) {
            return StringUtils.EMPTY;
        }
        RemoteResponse<String> resp = readKmImageTool.parseImageWithInput(imageUrl, cookieValue, userInput);
        if (resp == null || !resp.isSuccess()) {
            return StringUtils.EMPTY;
        }
        return resp.getData();
    }

    private List<Message> buildMessageForMergeDocContent(String docContent, Map<String, String> imageContentMap) {
        List<Message> messages = Lists.newArrayList();
        SystemPromptTemplate systemPromptTemplate = new SystemPromptTemplate(mergeDocAndImagePrompt);
        Message systemMessage = systemPromptTemplate.createMessage(Map.of("docContent", docContent, "imageContentMap", SerializeUtils.toJsonStr(imageContentMap)));
        messages.add(systemMessage);
        return messages;
    }

}

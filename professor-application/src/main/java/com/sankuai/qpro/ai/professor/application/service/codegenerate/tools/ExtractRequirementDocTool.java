package com.sankuai.qpro.ai.professor.application.service.codegenerate.tools;

import com.dianping.rhino.Rhino;
import com.dianping.rhino.threadpool.DefaultThreadPoolProperties;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.meituan.mdp.boot.starter.config.annotation.MdpConfig;
import com.sankuai.qpro.ai.professor.application.constants.RequestContextConstant;
import com.sankuai.qpro.ai.professor.application.model.codegenerate.RequestContext;
import com.sankuai.qpro.ai.professor.application.model.codegenerate.RequirementReviewInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.chat.messages.Message;
import org.springframework.ai.chat.messages.UserMessage;
import org.springframework.ai.chat.prompt.SystemPromptTemplate;
import org.springframework.ai.tool.annotation.Tool;
import org.springframework.ai.tool.annotation.ToolParam;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;

/**
 * @author: wuwenqiang
 * @create: 2025-05-23
 * @description: 提取需求文档工具
 */
@Component
@Slf4j
public class ExtractRequirementDocTool {

    @Resource
    private QueryKmDocTool queryKmDocTool;

    @MdpConfig("CODE_GENERATE_SPECIAL_SCENE_DOC_PROMPT")
    private String specialSceneDocPrompt;

    @MdpConfig("CODE_GENERATE_REVIEW_DOC_PARSE_PROMPT")
    private String reviewDocPrompt;

    @MdpConfig("CODE_GENERATE_REVIEW_INFO_EXTRACT_PROMPT")
    private String reviewInfoExtractPrompt;

    @MdpConfig("CODE_GENERATE_MERGE_PRD_AND_REVIEW_PROMPT")
    private String mergePrdAndReviewPrompt;

    @MdpConfig("CODE_GENERATE_TEST_ALL_PROMPT:# 角色\n你是一个资深的Java工程师，擅长对需求文档PRD和需求评审记录进行整合")
    private String testAllPrompt;

    private static final ExecutorService REQUIRE_DOC_THREAD_POOL = Rhino.newThreadPool("extractRequirementDocTool", new DefaultThreadPoolProperties.Setter()
            .withCoreSize(10).withMaxQueueSize(100).withMaxSize(20)).getExecutor();

    @Resource
    @Lazy
    private ChatClient gpt4Dot1Client;

    public Pair<String, RequirementReviewInfo> generateRequirementPromptWithReviewInfo(String prdUrl, String reviewUrl, String scene, String userInput) {
        Map<Object, Object> requestContextMap = RequestContext.getAllAttribute();
        // 1. 提取PRD
        CompletableFuture<String> prdDocFuture = asyncExtractPRDSpecialSceneDoc(prdUrl, scene, userInput, requestContextMap);
        // 2. 提取需求评审记录
        CompletableFuture<Pair<String, RequirementReviewInfo>> reviewDocFuture = asyncExtractReviewDoc(reviewUrl, userInput, requestContextMap);
        // 3. 整合内容，输出
        if (prdDocFuture == null || reviewDocFuture == null) {
            return Pair.of("异常: 提取文档失败", null);
        }
        CompletableFuture<Pair<String, RequirementReviewInfo>> resultFuture = prdDocFuture.thenCombine(reviewDocFuture, (prd, review) -> {
                    RequestContext.setAllAttribute(requestContextMap);
                    return Pair.of(mergeDoc(prd, review.getLeft()), review.getRight());
                })
                .exceptionally(e -> {
                    log.error("[ExtractRequirementDocTool] mergeDoc error, prdDocFuture={}, reviewDocFuture={} ", prdDocFuture, reviewDocFuture, e);
                    return null;
                });
        return resultFuture == null ? Pair.of("异常: 提取文档失败", null) : resultFuture.join();
    }

    public String generateRequirementPrompt(String prdUrl, String reviewUrl, String scene, String userInput) {
        Map<Object, Object> requestContextMap = RequestContext.getAllAttribute();
        // 1. 提取PRD
        CompletableFuture<String> prdDocFuture = asyncExtractPRDSpecialSceneDoc(prdUrl, scene, userInput, requestContextMap);
        // 2. 提取需求评审记录
        CompletableFuture<Pair<String, RequirementReviewInfo>> reviewDocFuture = asyncExtractReviewDoc(reviewUrl, userInput, requestContextMap);
        // 3. 整合内容，输出
        if (prdDocFuture == null || reviewDocFuture == null) {
            return "异常: 提取文档失败";
        }
        CompletableFuture<String> resultFuture = prdDocFuture.thenCombine(reviewDocFuture, (prd, review) -> {
                    RequestContext.setAllAttribute(requestContextMap);
                    return mergeDoc(prd, review.getLeft());
                })
                .exceptionally(e -> {
                    log.error("[ExtractRequirementDocTool] mergeDoc error, prdDocFuture={}, reviewDocFuture={} ", prdDocFuture, reviewDocFuture, e);
                    return null;
                });
        return resultFuture == null ? "异常: 提取文档失败" : resultFuture.join();
    }

    private String mergeDoc(String prd, String review) {
        if (StringUtils.isBlank(prd)) {
            return "异常: PRD提取失败";
        }
        if (review == null || StringUtils.isBlank(review)) {
            return "异常: 评审记录提取失败";
        }
        return mergeRequirementDoc(prd, review);
    }

    // Todo: 待删除
    public String testAll(String prdUrl, String reviewUrl, String scene, String userInput) {
        List<Message> messages = Lists.newArrayList();
        SystemPromptTemplate systemPromptTemplate = new SystemPromptTemplate(testAllPrompt);
        Message systemMessage = systemPromptTemplate.createMessage(Map.of("prdUrl", prdUrl, "reviewUrl", reviewUrl));
        messages.add(systemMessage);
        messages.add(new UserMessage("检索内容: " + scene));
        if (StringUtils.isNotBlank(userInput)) {
            messages.add(new UserMessage("补充描述: " + userInput));
        }
        return gpt4Dot1Client.prompt().messages(messages)
                .tools(this, queryKmDocTool)
                .call().content();
    }

    @Tool(name = "ExtractPRDSpecialSceneDocTool", description = "提取PRD中特定场景内容")
    public String extractPRDSpecialSceneDoc(@ToolParam(description = "PRD文档链接") String url, @ToolParam(description = "需要检索的特定场景") String scene, @ToolParam(description = "用户输入消息") String userInput) {
        // 1. 获取文档内容
        String docContent = extractDocContent(url);
        if (StringUtils.isBlank(docContent)) {
            return "异常: 查询PRD文档为空";
        }
        // 2. 构造消息
        List<Message> messages = buildMessagesForPRD(docContent, scene, userInput);
        // 3. 提取文档记录
        return gpt4Dot1Client.prompt().messages(messages)
                .tools(queryKmDocTool)
                .call().content();
    }

    @Tool(name = "ExtractReviewDocTool", description = "提取需求评审记录")
    public String extractReviewDoc(@ToolParam(description = "需求评审文档链接") String url, @ToolParam(description = "用户输入消息") String userInput) {
        // 1. 获取文档内容
        String docContent = extractDocContent(url);
        if (StringUtils.isBlank(docContent)) {
            return "异常: 查询需求评审文档为空";
        }
        // 2. 解析文档
        return parseReviewDoc(docContent, userInput);
    }

    public Pair<String, RequirementReviewInfo> extractReviewDocAndInfo(String url, String userInput) {
        // 1. 获取文档内容
        String docContent = extractDocContent(url);
        if (StringUtils.isBlank(docContent)) {
            return Pair.of("异常: 查询需求评审文档为空", null);
        }
        // 2. 解析文档
        Map<Object, Object> requestContextMap = RequestContext.getAllAttribute();
        CompletableFuture<String> parseDocFuture = asyncParseReviewDoc(url, docContent, userInput, requestContextMap);
        // 3. 提取仓库、appkey和接口信息
        CompletableFuture<RequirementReviewInfo> extractReviewInfoFuture = asyncExtractReviewInfo(url, docContent, requestContextMap);
        // 4. 合并结果
        if (parseDocFuture == null || extractReviewInfoFuture == null) {
            return Pair.of("异常: 解析需求评审文档失败", null);
        }
        CompletableFuture<Pair<String, RequirementReviewInfo>> resultFuture = parseDocFuture.thenCombine(extractReviewInfoFuture, (parseDoc, extractReviewInfo) ->
           Pair.of(parseDoc, extractReviewInfo))
                .exceptionally(e -> {
                    log.error("[ExtractRequirementDocTool] extractReviewDoc -> mergeResult error, url={}, userInput={} ", url, userInput, e);
                    return null;
                });
        return resultFuture == null ? null : resultFuture.join();
    }

    private String extractDocContent(String url) {
        // 1. 尝试从线程上下文中获取文档
        Map<String, String> kmDocMap = RequestContext.getAttribute(RequestContextConstant.KM_DOC_MAP);
        if (kmDocMap == null) {
            kmDocMap = Maps.newHashMap();
        }
        String docContent = null;
        if (kmDocMap.containsKey(url)) {
            docContent = kmDocMap.get(url);
        }
        // 2. 获取文档为空则调用查询工具获取文档
        if (StringUtils.isBlank(docContent)) {
            docContent = queryKmDocTool.queryDocument(url);
        }
        return docContent;
    }

    private List<Message> buildMessagesForPRD(String docContent, String scene, String userInput) {
        List<Message> messages = Lists.newArrayList();
        SystemPromptTemplate systemPromptTemplate = new SystemPromptTemplate(specialSceneDocPrompt);
        Message systemMessage = systemPromptTemplate.createMessage(Map.of("docContent", docContent));
        messages.add(systemMessage);
        messages.add(new UserMessage("检索内容: " + scene));
        if (StringUtils.isNotBlank(userInput)) {
            messages.add(new UserMessage("补充描述: " + userInput));
        }
        return messages;
    }

    private List<Message> buildMessagesForReview(String docContent, String userInput) {
        List<Message> messages = Lists.newArrayList();
        SystemPromptTemplate systemPromptTemplate = new SystemPromptTemplate(reviewDocPrompt);
        Message systemMessage = systemPromptTemplate.createMessage(Map.of("docContent", docContent));
        messages.add(systemMessage);
        if (StringUtils.isNotBlank(userInput)) {
            messages.add(new UserMessage("补充描述: " + userInput));
        }
        return messages;
    }

    private List<Message> buildMessagesForStandardReviewInfo(String docContent) {
        List<Message> messages = Lists.newArrayList();
        SystemPromptTemplate systemPromptTemplate = new SystemPromptTemplate(reviewInfoExtractPrompt);
        Message systemMessage = systemPromptTemplate.createMessage(Map.of("docContent", docContent));
        messages.add(systemMessage);
        return messages;
    }

    private String parseReviewDoc(String docContent, String userInput) {
        List<Message> messages = buildMessagesForReview(docContent, userInput);
        return gpt4Dot1Client.prompt().messages(messages)
                .tools(queryKmDocTool)
                .call().content();
    }

    private RequirementReviewInfo extractReviewInfo(String docContent) {
        List<Message> messages = buildMessagesForStandardReviewInfo(docContent);
        return gpt4Dot1Client.prompt().messages(messages)
                .tools(queryKmDocTool)
                .call().entity(RequirementReviewInfo.class);
    }

    private String mergeRequirementDoc(String prdDoc, String reviewDoc) {
        List<Message> messages = buildMessagesForMergeRequirementDoc(prdDoc, reviewDoc);
        return gpt4Dot1Client.prompt().messages(messages)
                .call().content();
    }

    private List<Message> buildMessagesForMergeRequirementDoc(String prdDoc, String reviewDoc) {
        List<Message> messages = Lists.newArrayList();
        SystemPromptTemplate systemPromptTemplate = new SystemPromptTemplate(mergePrdAndReviewPrompt);
        Message systemMessage = systemPromptTemplate.createMessage(Map.of("prdDoc", prdDoc, "reviewDoc", reviewDoc));
        messages.add(systemMessage);
        return messages;
    }

    /**
     * 异步解析需求评审文档
     */
    private CompletableFuture<String> asyncParseReviewDoc(String url, String docContent, String userInput, Map<Object, Object> requestContextMap) {
        return CompletableFuture.supplyAsync(() -> {
            RequestContext.setAllAttribute(requestContextMap);
            return parseReviewDoc(docContent, userInput);
        }, REQUIRE_DOC_THREAD_POOL)
                .exceptionally(e -> {
                    log.error("[ExtractRequirementDocTool] extractReviewDoc -> parseDoc error, url={}, docContent, userInput={} ", url, docContent, userInput, e);
                    return null;
                });
    }

    /**
     * 异步提取需求评审信息（结构化信息）
     */
    private CompletableFuture<RequirementReviewInfo> asyncExtractReviewInfo(String url, String docContent, Map<Object, Object> requestContextMap) {
        return CompletableFuture.supplyAsync(() -> {
            RequestContext.setAllAttribute(requestContextMap);
            return extractReviewInfo(docContent);
        }, REQUIRE_DOC_THREAD_POOL)
                .exceptionally(e -> {
                    log.error("[ExtractRequirementDocTool] extractReviewDoc -> extractReviewInfo error, url={}, docContent={} ", url, docContent, e);
                    return null;
                });
    }

    /**
     * 异步提取PRD特殊场景文档
     */
    private CompletableFuture<String> asyncExtractPRDSpecialSceneDoc(String url, String scene, String userInput, Map<Object, Object> requestContextMap) {
        return CompletableFuture.supplyAsync(() -> {
            RequestContext.setAllAttribute(requestContextMap);
            return extractPRDSpecialSceneDoc(url, scene, userInput);
        }, REQUIRE_DOC_THREAD_POOL)
        .exceptionally(e -> {
            log.error("[ExtractRequirementDocTool] asyncExtractPRDSpecialSceneDoc error, url={}, scene={}, userInput={}", url, scene, userInput, e);
            return null;
        });
    }

    /**
     * 异步提取需求评审文档（返回Pair<String, RequirementReviewInfo>）
     */
    private CompletableFuture<Pair<String, RequirementReviewInfo>> asyncExtractReviewDoc(String url, String userInput, Map<Object, Object> requestContextMap) {
        return CompletableFuture.supplyAsync(() -> {
            RequestContext.setAllAttribute(requestContextMap);
            return extractReviewDocAndInfo(url, userInput);
                }, REQUIRE_DOC_THREAD_POOL)
            .exceptionally(e -> {
                log.error("[ExtractRequirementDocTool] asyncExtractReviewDoc error, url={}, userInput={}", url, userInput, e);
                return null;
            });
    }

}

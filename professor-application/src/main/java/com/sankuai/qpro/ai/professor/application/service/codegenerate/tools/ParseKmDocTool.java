package com.sankuai.qpro.ai.professor.application.service.codegenerate.tools;

import com.sankuai.qpro.ai.professor.api.response.RemoteResponse;
import com.sankuai.qpro.ai.professor.application.service.codegenerate.proxy.KmContentServiceProxy;
import com.sankuai.qpro.ai.professor.application.utils.HeaderUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.ai.tool.annotation.Tool;
import org.springframework.ai.tool.annotation.ToolParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @author: wuwenqiang
 * @create: 2025-07-21
 * @description:
 */
@Component
public class ParseKmDocTool {

    @Autowired
    private KmContentServiceProxy kmContentServiceProxy;

    @Tool(name = "ParseKmDocTool", description = "解析学城文档")
    public String queryDocument(@ToolParam(description = "学城文档链接，前缀为https://km.sankuai.com/collabpage") String url){
        String cookieValue = HeaderUtils.getCookie();
        RemoteResponse<String> rawContent = kmContentServiceProxy.readContentByCookie(url, cookieValue);
        if (rawContent == null || !rawContent.isSuccess() || StringUtils.isBlank(rawContent.getData())) {
            return rawContent.getMsg();
        }
        // 解析全量文档
        String result = kmContentServiceProxy.extractAllContent(rawContent.getData());
        return result;
    }

    public RemoteResponse<String> queryDocument(String url, String cookieValue) {
        RemoteResponse<String> rawContent = kmContentServiceProxy.readContentByCookie(url, cookieValue);
        if (rawContent == null || !rawContent.isSuccess() || StringUtils.isBlank(rawContent.getData())) {
            return rawContent;
        }
        // 解析全量文档
        String result = kmContentServiceProxy.extractAllContent(rawContent.getData());
        return RemoteResponse.success(result);
    }
}

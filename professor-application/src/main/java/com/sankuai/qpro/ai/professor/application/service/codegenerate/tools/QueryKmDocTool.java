package com.sankuai.qpro.ai.professor.application.service.codegenerate.tools;

import com.google.common.collect.Maps;
import com.sankuai.qpro.ai.professor.api.response.RemoteResponse;
import com.sankuai.qpro.ai.professor.application.constants.RequestContextConstant;
import com.sankuai.qpro.ai.professor.application.model.codegenerate.RequestContext;
import com.sankuai.qpro.ai.professor.application.service.codegenerate.proxy.KmContentServiceProxy;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.ai.tool.annotation.Tool;
import org.springframework.ai.tool.annotation.ToolParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * @author: wuwenqiang
 * @create: 2025-05-23
 * @description:
 */
@Component
public class QueryKmDocTool {

    @Autowired
    private KmContentServiceProxy kmContentServiceProxy;

    @Tool(name = "QueryDocumentTool", description = "查询学城文档")
    public String queryDocument(@ToolParam(description = "学城文档链接，前缀为https://km.sankuai.com/collabpage") String url){
        Map<String, String> documentCache = RequestContext.getAttribute(RequestContextConstant.KM_DOC_MAP);
        if (MapUtils.isNotEmpty(documentCache) && StringUtils.isNotBlank(documentCache.get(url))) {
            return documentCache.get(url);
        }
        String taskId = RequestContext.getAttribute(RequestContextConstant.TASK_ID);
        RemoteResponse<String> rawContent = kmContentServiceProxy.readContent(taskId, url);
        if (rawContent == null || !rawContent.isSuccess() || StringUtils.isBlank(rawContent.getData())) {
            return "异常: " + rawContent.getMsg();
        }
        // 解析全量文档
        String result = kmContentServiceProxy.extractAllContent(rawContent.getData());
        if (MapUtils.isEmpty(documentCache)) {
            documentCache = Maps.newHashMap();
        }
        documentCache.put(url, result);
        // 如果是异步线程调用了当前方法，RequestContext的set就无法生效
        RequestContext.setAttribute(RequestContextConstant.KM_DOC_MAP, documentCache);
        return result;
    }

//    @Tool(description = "查询特殊场景的学城文档，目前场景支持货架和团详")
//    public String queryAndFilterDocumentForScene(@ToolParam(description = "学城文档链接，前缀为https://km.sankuai.com/collabpage")String url, @ToolParam(description = "筛选场景: 当前支持输入货架/团详") String scene){
//        String userMis = RequestContext.getAttribute(RequestContextConstant.USER_MIS);
//        RemoteResponse<String> rawContent = kmContentServiceProxy.readContent(userMis, url);
//        if (rawContent == null || !rawContent.isSuccess() || StringUtils.isBlank(rawContent.getData())) {
//            return rawContent.getMsg();
//        }
//        // 包含特殊场景，解析特定段落
//        if (StringUtils.isNotBlank(scene)) {
//            return kmContentServiceProxy.extractContentForSpecialScene(rawContent.getData(), scene);
//        }
//        // 不包含特殊场景，解析全量文档
//        return kmContentServiceProxy.extractAllContent(rawContent.getData());
//    }
}

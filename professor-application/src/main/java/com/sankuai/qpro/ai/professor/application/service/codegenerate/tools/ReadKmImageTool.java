package com.sankuai.qpro.ai.professor.application.service.codegenerate.tools;

import com.google.common.collect.Lists;
import com.meituan.mdp.boot.starter.config.annotation.MdpConfig;
import com.sankuai.qpro.ai.professor.api.enums.ResponseCodeEnum;
import com.sankuai.qpro.ai.professor.api.response.RemoteResponse;
import com.sankuai.qpro.ai.professor.application.model.codegenerate.ImageContent;
import com.sankuai.qpro.ai.professor.application.service.codegenerate.proxy.KmImageServiceProxy;
import com.sankuai.qpro.ai.professor.application.utils.HeaderUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.chat.messages.Message;
import org.springframework.ai.chat.messages.SystemMessage;
import org.springframework.ai.chat.messages.UserMessage;
import org.springframework.ai.tool.annotation.Tool;
import org.springframework.ai.tool.annotation.ToolParam;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * @author: wuwenqiang
 * @create: 2025-07-01
 * @description:
 */
@Component
@Slf4j
public class ReadKmImageTool {

    @Resource
    private KmImageServiceProxy kmImageServiceProxy;

    @MdpConfig("CODE_GENERATE_READ_IMAGE_PROMPT")
    private String readImagePrompt;

    @Resource
    @Lazy
    private ChatClient gpt4Dot1Client;

//    @Tool(description = "从学城图片链接读取图片信息")
//    public ImageContent readImageFromUrl(@ToolParam(description = "学城文档链接，格式为https://km.sankuai.com/api/file/cdn/{file_id}") String url) {
//        String cookieValue = System.getenv("KM_COOKIE_VALUE");
//        RemoteResponse<ImageContent> resp = kmImageServiceProxy.readImageContentByCookie(url, cookieValue);
//        if (resp == null || !resp.isSuccess()) {
//            String msg = resp == null ? "返回为空" : resp.getMsg();
//            log.error("[ReadKmImageTool] readImageFromUrl error, url={}, msg={}", url, msg);
//            return null;
//        }
//        return resp.getData();
//    }

//    @Tool(description = "从学城图片链接读取压缩后的图片")
    public ImageContent readImageFromUrlCompress(
            @ToolParam(description = "学城文档链接，格式为https://km.sankuai.com/api/file/cdn/{file_id}") String url,
            @ToolParam(description = "压缩级别：1=轻度压缩(质量85%), 2=中度压缩(质量70%), 3=高度压缩(质量50%)") int compressionLevel) {
        String cookieValue = System.getenv("KM_COOKIE_VALUE");
        RemoteResponse<ImageContent> resp = kmImageServiceProxy.readImageContentByCookieWithCompression(url, cookieValue, compressionLevel);
        if (resp == null || !resp.isSuccess()) {
            String msg = resp == null ? "返回为空" : resp.getMsg();
            log.error("[ReadKmImageTool] readImageFromUrlWithCompression error, url={}, compressionLevel={}, msg={}", url, compressionLevel, msg);
            return null;
        }
        return resp.getData();
    }

    @Tool(name = "explain_km_image", description = "从学城图片链接解读图片信息")
    public RemoteResponse<String> explainKmImage(
            @ToolParam(description = "学城文档链接，格式为https://km.sankuai.com/api/file/cdn/{file_id}")String url) {
        String cookieValue = HeaderUtils.getKmCookieValue();
        RemoteResponse<ImageContent> resp = kmImageServiceProxy.readImageContentByCookie(url, cookieValue);
        if (resp == null || !resp.isSuccess()) {
            String msg = resp == null ? "获取学城图片内容失败" : resp.getMsg();
            return RemoteResponse.fail(ResponseCodeEnum.SERVER_ERROR, msg);
        }
        return parseImage(url, cookieValue);
    }


    public ImageContent readImageFromUrlCompress(
             String url, String cookieValue,
             int compressionLevel) {
        RemoteResponse<ImageContent> resp = kmImageServiceProxy.readImageContentByCookieWithCompression(url, cookieValue, compressionLevel);
        if (resp == null || !resp.isSuccess()) {
            String msg = resp == null ? "返回为空" : resp.getMsg();
            log.error("[ReadKmImageTool] readImageFromUrlWithCompression error, url={}, compressionLevel={}, msg={}", url, compressionLevel, msg);
            return null;
        }
        return resp.getData();
    }

    public RemoteResponse<String> parseImage(String url, String cookieValue) {
        return parseImageInternal(url, cookieValue, null);
    }
    
    public RemoteResponse<String> parseImageWithInput(String url, String cookieValue, String userInput) {
        return parseImageInternal(url, cookieValue, userInput);
    }

    /**
     * 解析图片的内部实现方法
     * @param url 图片链接
     * @param cookieValue cookie值
     * @param userInput 用户补充信息，可为null
     * @return 解析结果
     */
    private RemoteResponse<String> parseImageInternal(String url, String cookieValue, String userInput) {
        // 获取图片内容
        RemoteResponse<ImageContent> resp = kmImageServiceProxy.readImageContentByCookie(url, cookieValue);
        if (resp == null || !resp.isSuccess()) {
            String msg = resp == null ? "图片内容返回为空" : resp.getMsg();
            return RemoteResponse.fail(ResponseCodeEnum.SERVER_ERROR, msg);
        }
        
        ImageContent imageContent = resp.getData();
        
        // 构建消息列表
        List<Message> messages = Lists.newArrayList();
        Message systemMessage = new SystemMessage(readImagePrompt);
        messages.add(systemMessage);
        
        // 根据是否有用户输入构建不同的用户消息内容
        String userMessageContent = buildUserMessageContent(url, userInput);
        Message userMessage = UserMessage.builder()
                .text(userMessageContent)
                .media(ImageContent.buildMedia(imageContent))
                .build();
        messages.add(userMessage);
        
        // 调用AI解析图片
        String result = gpt4Dot1Client.prompt().messages(messages)
                .call().content();
        return RemoteResponse.success(result);
    }

    /**
     * 构建用户消息内容
     * @param url 图片链接
     * @param userInput 用户补充信息
     * @return 用户消息内容
     */
    private String buildUserMessageContent(String url, String userInput) {
        if (StringUtils.isNotBlank(userInput)) {
            return String.format("请结合以下信息解读图片, 当前图片链接为:%s，图片，补充信息: %s", url, userInput);
        }
        return "请解读以下图片";
    }

}

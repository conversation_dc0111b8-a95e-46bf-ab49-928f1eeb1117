package com.sankuai.qpro.ai.professor.application.service.developer.trading;

import com.google.common.collect.Lists;
import com.meituan.mdp.langmodel.api.annotation.MdpLangModelFunction;
import com.meituan.mdp.langmodel.api.message.AssistantMessage;
import com.meituan.mdp.langmodel.api.message.Message;
import com.meituan.mdp.langmodel.api.message.SystemMessage;
import com.meituan.mdp.langmodel.api.message.UserMessage;
import com.meituan.mdp.langmodel.component.function.MdpFunctionRegistry;
import com.meituan.mdp.langmodel.component.model.chat.OpenAIChatModel;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.util.List;
/**
 * 商品交易Agent
 * <AUTHOR>
 * @date 2024/12/4
 */
@Service
public class DeveloperAgent {

    @Qualifier("gpt4oMiniModel")
    @Autowired
    private OpenAIChatModel openAIChatModel;

    public String execute(String msg) {

        SystemMessage systemMessage = SystemMessage.from("你是个处理商品交易的专家。");

        UserMessage userMessage = UserMessage.from(msg);
        List<Message> msgList = Lists.newArrayList();
        msgList.add(systemMessage);
        msgList.add(userMessage);
        AssistantMessage assistantMessage = openAIChatModel.sendMessagesUseFuncs(msgList, MdpFunctionRegistry.getFunctions());
        return assistantMessage.getContent();
    }

    /**
     * RAG 从知识库中获取解决方案
     * @param msgList
     * @return
     */
    public String getSolution(List<Message> msgList) {
        return "报价问题排查方案：" +
                "第一步，参数校验，需要提供 traceId。" +
                "第二步，根据 traceId，获取各个场景的报价日志" +
                "第三步，获取各个场景查询报价的代码" +
                "第四步，ch"
                ;
    }

    @MdpLangModelFunction
    public String paramCheck(List<Message> msgList) {
        return "";
    }

    @MdpLangModelFunction
    public String getPriceCode(String str) {
        return "public void prepare(DealCtx ctx) {\n" +
                "        if (ctx.getEnvCtx().isExternalAndNoScene()) {\n" +
                "            return;\n" +
                "        }\n" +
                "        boolean requireLogging = isRequireLogging(ctx.isMt(), ctx.isMt() ? ctx.getMtId() : ctx.getDpId());\n" +
                "        // 分销优惠展示具有排他性，必须在最前面，并且其他优惠都不会展示\n" +
                "        if (DealCtxHelper.isOdpSource(ctx)) {\n" +
                "            if (!ctx.isExternal() || ctx.getEnvCtx().isExternalAndEnabled(MiniProgramSceneEnum.NORMAL_PROMO.getPromoScene())) {\n" +
                "                BatchPriceRequest request = buildOdpPriceRequest(ctx);\n" +
                "                addPageSource(request);\n" +
                "                addLiveSecKillParams(request, ctx.getPassParam(), ctx.getRequestSource(), ctx.isMt() ? ctx.getMtLongShopId() : ctx.getDpLongShopId());\n" +
                "                // 如果使用卡片样式\n" +
                "                if(ctx.isEnableCardStyle() || ctx.isEnableCardStyleV2()) {\n" +
                "                    addPromoDescType(request);\n" +
                "                }\n" +
                "                LogUtils.info(\"[PriceDisplayWrapper] prepare. batchQueryPriceByLongShopId: source=odp userId={} request={}\",\n" +
                "                        ctx.getUserId4P(), JsonCodec.encode(request));\n" +
                "                priceDisplayLocalService.batchQueryPriceByLongShopId(request, ctx);\n" +
                "                logBatchQueryPriceByLongShopId(requireLogging, request);\n" +
                "                ctx.getFutureCtx().setNormalPriceFuture(FutureFactory.getFuture());\n" +
                "                return;\n" +
                "            }\n" +
                "        }\n" +
                "\n" +
                "        calculateUserZuLiaoAbExpsResult(ctx);\n" +
                "\n" +
                "        // 展示市场价的团购分类\n" +
                "        if (GreyUtils.isShowMarketPriceCategory(ctx)\n" +
                "                || (ctx.getPriceContext().isZuLiaoButtonNewStyle())\n" +
                "                || ctx.isEnableCardStyle() || ctx.isEnableCardStyleV2()) {\n" +
                "            BatchPriceRequest batchPriceRequest = buildBatchPriceRequest(ctx);\n" +
                "            batchPriceRequest.setScene(RequestSceneEnum.PROMO_DETAIL_DESC_WithDealPromo.getScene());\n" +
                "            if (Objects.equals(ctx.getEnvCtx().getMpAppId(), MpAppIdEnum.MT_WEIXIN_MINIPROGRAM.getMpAppId())) {\n" +
                "                ClientEnv clientEnv = batchPriceRequest.getClientEnv();\n" +
                "                if (clientEnv != null) {\n" +
                "                    clientEnv.setClientType(ClientTypeEnum.mt_weApp.getType());\n" +
                "                }\n" +
                "            }\n" +
                "\n" +
                "            if (Objects.equals(ctx.getEnvCtx().getMpAppId(), MpAppIdEnum.DP_WEIXIN_MINIPROGRAM.getMpAppId())) {\n" +
                "                ClientEnv clientEnv = batchPriceRequest.getClientEnv();\n" +
                "                if (clientEnv != null) {\n" +
                "                    clientEnv.setClientType(ClientTypeEnum.dp_weApp.getType());\n" +
                "                }\n" +
                "            }\n" +
                "            addPageSource(batchPriceRequest);\n" +
                "            // 直播秒杀场景查价参数\n" +
                "            addLiveSecKillParams(batchPriceRequest, ctx.getPassParam(), ctx.getRequestSource(), ctx.isMt() ? ctx.getMtLongShopId() : ctx.getDpLongShopId());\n" +
                "            // 如果是预售\n" +
                "            putPreSaleTypeIfExist(batchPriceRequest, ctx);\n" +
                "\n" +
                "\n" +
                "            // 如果使用卡片样式\n" +
                "            if(ctx.isEnableCardStyle() || ctx.isEnableCardStyleV2()) {\n" +
                "                addPromoDescType(batchPriceRequest);\n" +
                "            }\n" +
                "\n" +
                "            addPriceCipher(batchPriceRequest,ctx);\n" +
                "            LogUtils.info(\"[PriceDisplayWrapper] prepare. batchQueryPriceByLongShopId: source=showMarketPrice userId={} request={}\",\n" +
                "                    ctx.getUserId4P(), JsonCodec.encode(batchPriceRequest));\n" +
                "            priceDisplayLocalService.batchQueryPriceByLongShopId(batchPriceRequest, ctx);\n" +
                "            logBatchQueryPriceByLongShopId(requireLogging, batchPriceRequest);\n" +
                "            ctx.getFutureCtx().setDealPromoPriceFuture(FutureFactory.getFuture());\n" +
                "\n" +
                "            //如果用户可以享受会员优惠，则查询会员优惠组合后的最终价格\n" +
                "            if (memberPriceWrapper.isMemberPriceProcessorEnable(ctx)) {\n" +
                "                batchPriceRequest.setScene(RequestSceneEnum.PROMO_DETAIL_DESC_WithMerchantMember.getScene());\n" +
                "                LogUtils.info(\"[PriceDisplayWrapper] prepare. batchQueryPriceByLongShopId: source=merchantMember userId={} request={}\",\n" +
                "                        ctx.getUserId4P(), JsonCodec.encode(batchPriceRequest));\n" +
                "                priceDisplayLocalService.batchQueryPriceByLongShopId(batchPriceRequest, ctx);\n" +
                "                logBatchQueryPriceByLongShopId(requireLogging, batchPriceRequest);\n" +
                "                ctx.getFutureCtx().setShopMemberPromoPriceFuture(FutureFactory.getFuture());\n" +
                "            }\n" +
                "        }\n" +
                "\n" +
                "        if (GreyUtils.needAtmosphereBarAndGeneralPromoDetail(ctx)) {\n" +
                "            BatchPriceRequest batchPriceRequest = buildBatchPriceRequest(ctx);\n" +
                "            batchPriceRequest.setScene(RequestSceneEnum.PROMO_DETAIL_DESC.getScene());\n" +
                "            addPageSource(batchPriceRequest);\n" +
                "            // 直播秒杀场景查价参数\n" +
                "            addLiveSecKillParams(batchPriceRequest, ctx.getPassParam(), ctx.getRequestSource(), ctx.isMt() ? ctx.getMtLongShopId() : ctx.getDpLongShopId());\n" +
                "            // 如果使用卡片样式\n" +
                "            if(ctx.isEnableCardStyle() || ctx.isEnableCardStyleV2()) {\n" +
                "                addPromoDescType(batchPriceRequest);\n" +
                "            }\n" +
                "            LogUtils.info(\"[PriceDisplayWrapper] prepare. batchQueryPriceByLongShopId: source=atmosphereBarAndPromoDetail userId={} request={}\",\n" +
                "                    ctx.getUserId4P(), JsonCodec.encode(batchPriceRequest));\n" +
                "            priceDisplayLocalService.batchQueryPriceByLongShopId(batchPriceRequest, ctx);\n" +
                "            logBatchQueryPriceByLongShopId(requireLogging, batchPriceRequest);\n" +
                "            ctx.getFutureCtx().setAtmosphereBarAndGeneralPromoDetailFuture(FutureFactory.getFuture());\n" +
                "        }\n" +
                "\n" +
                "        if(!ctx.isExternal() || ctx.getEnvCtx().isExternalAndEnabled(MiniProgramSceneEnum.MEMBER_CARD.getPromoScene())) {\n" +
                "            // 折扣卡价，这里为了提高并行度，对报价放大流量直接查询折扣卡价格\n" +
                "            PriceRequest request = buildPriceRequest(ctx);\n" +
                "            if (ctx.getPriceContext().isZuLiaoButtonNewStyle()) {\n" +
                "                request.setScene(RequestSceneEnum.MERGE_CARD_FUSION_WithDealPromo.getScene());\n" +
                "            }\n" +
                "            addPageSource(request);\n" +
                "            // 直播秒杀场景查价参数\n" +
                "            addLiveSecKillParams(request, ctx.getPassParam(), ctx.getRequestSource());\n" +
                "            // 如果使用卡片样式\n" +
                "            if (ctx.isEnableCardStyle() || ctx.isEnableCardStyleV2()) {\n" +
                "                addPromoDescType(request);\n" +
                "            }\n" +
                "            if (request.getExtension() == null) {\n" +
                "                request.setExtension(new HashMap<>());\n" +
                "            }\n" +
                "            //这个是RCF优化，后续上，通过报价返回权益台信息\n" +
                "            //request.getExtension().put(ExtensionKeyEnum.NeedCardInfo.getDesc(), \"true\");\n" +
                "            LogUtils.info(\"[PriceDisplayWrapper] prepare. queryPrice: source=memberCard userId={} request={}\",\n" +
                "                    ctx.getUserId4P(), JsonCodec.encode(request));\n" +
                "            priceDisplayLocalService.queryPrice(request, ctx);\n" +
                "            logQueryPrice(requireLogging, request);\n" +
                "            ctx.getFutureCtx().setJoyDiscountCardPriceFuture(FutureFactory.getFuture());\n" +
                "        }\n" +
                "\n" +
                "        if(!ctx.isExternal() || ctx.getEnvCtx().isExternalAndEnabled(MiniProgramSceneEnum.IDLE_PROMO.getPromoScene())) {\n" +
                "            // 闲时优惠价\n" +
                "            PriceRequest idlePriceRequest = buildPriceRequest(ctx);\n" +
                "            PromoIdentity idlePromo = new PromoIdentity(\n" +
                "                    0,\n" +
                "                    PromoTypeEnum.IDLE_PROMO.getType());\n" +
                "            idlePriceRequest.setSpecPromo(idlePromo);\n" +
                "            addPageSource(idlePriceRequest);\n" +
                "            // 直播秒杀场景查价参数\n" +
                "            addLiveSecKillParams(idlePriceRequest, ctx.getPassParam(), ctx.getRequestSource());\n" +
                "            // 如果使用卡片样式\n" +
                "            if(ctx.isEnableCardStyle() || ctx.isEnableCardStyleV2()) {\n" +
                "                addPromoDescType(idlePriceRequest);\n" +
                "            }\n" +
                "            LogUtils.info(\"[PriceDisplayWrapper] prepare. queryPrice: source=idlePromo userId={} request={}\",\n" +
                "                    ctx.getUserId4P(), JsonCodec.encode(idlePriceRequest));\n" +
                "            priceDisplayLocalService.queryPrice(idlePriceRequest, ctx);\n" +
                "            logQueryPrice(requireLogging, idlePriceRequest);\n" +
                "            ctx.getFutureCtx().setIdlePromoPriceFuture(FutureFactory.getFuture());\n" +
                "        }\n" +
                "\n" +
                "        if (!ctx.isExternal() || ctx.getEnvCtx().isExternalAndEnabled(MiniProgramSceneEnum.NORMAL_PROMO.getPromoScene())) {\n" +
                "            if (isUseBatchPriceQuery(ctx)) {\n" +
                "                BatchPriceRequest batchPriceRequest = buildBatchPriceRequest(ctx);\n" +
                "                batchPriceRequest.setScene(RequestSceneEnum.PROMO_DETAIL_NO_ITEM_DESC.getScene());\n" +
                "                addPageSource(batchPriceRequest);\n" +
                "                // 直播秒杀场景查价参数\n" +
                "                addLiveSecKillParams(batchPriceRequest, ctx.getPassParam(), ctx.getRequestSource(), ctx.isMt() ? ctx.getMtLongShopId() : ctx.getDpLongShopId());\n" +
                "                // 如果使用卡片样式\n" +
                "                if(ctx.isEnableCardStyle() || ctx.isEnableCardStyleV2()) {\n" +
                "                    addPromoDescType(batchPriceRequest);\n" +
                "                }\n" +
                "\n" +
                "                addPriceCipher(batchPriceRequest,ctx);\n" +
                "                LogUtils.info(\"[PriceDisplayWrapper] prepare. batchQueryPriceByLongShopId: source=normalPromo userId={} request={}\",\n" +
                "                        ctx.getUserId4P(), JsonCodec.encode(batchPriceRequest));\n" +
                "                priceDisplayLocalService.batchQueryPriceByLongShopId(batchPriceRequest, ctx);\n" +
                "                logBatchQueryPriceByLongShopId(requireLogging, batchPriceRequest);\n" +
                "                ctx.getFutureCtx().setNormalPriceFuture(FutureFactory.getFuture());\n" +
                "            } else {\n" +
                "                // 普通立减价\n" +
                "                PriceRequest request = buildPriceRequest(ctx);\n" +
                "                if (ctx.isMtLiveMinApp()) {\n" +
                "                    // 私域直播小程序无优惠\n" +
                "                    request.setScene(RequestSceneEnum.NO_PROMO.getScene());\n" +
                "                } else {\n" +
                "                    request.setScene(RequestSceneEnum.PROMO_DETAIL_NO_ITEM_DESC.getScene());\n" +
                "                }\n" +
                "                if (ctx.getPriceContext().isZuLiaoButtonNewStyle()){\n" +
                "                    request.setScene(RequestSceneEnum.PROMO_DETAIL_DESC_WithDealPromo.getScene());\n" +
                "                }\n" +
                "\n" +
                "                if (Objects.equals(ctx.getEnvCtx().getMpAppId(), MpAppIdEnum.MT_WEIXIN_MINIPROGRAM.getMpAppId())) {\n" +
                "                    ClientEnv clientEnv = request.getClientEnv();\n" +
                "                    if (clientEnv != null) {\n" +
                "                        clientEnv.setClientType(ClientTypeEnum.mt_weApp.getType());\n" +
                "                    }\n" +
                "                }\n" +
                "                if (Objects.equals(ctx.getEnvCtx().getMpAppId(), MpAppIdEnum.DP_WEIXIN_MINIPROGRAM.getMpAppId())) {\n" +
                "                    ClientEnv clientEnv = request.getClientEnv();\n" +
                "                    if (clientEnv != null) {\n" +
                "                        clientEnv.setClientType(ClientTypeEnum.dp_weApp.getType());\n" +
                "                    }\n" +
                "                }\n" +
                "\n" +
                "                addPageSource(request);\n" +
                "                // 直播秒杀场景查价参数\n" +
                "                addLiveSecKillParams(request, ctx.getPassParam(), ctx.getRequestSource());\n" +
                "                // 如果使用卡片样式\n" +
                "                if(ctx.isEnableCardStyle() || ctx.isEnableCardStyleV2()) {\n" +
                "                    addPromoDescType(request);\n" +
                "                }\n" +
                "\n" +
                "                addPriceCipher(request,ctx);\n" +
                "                LogUtils.info(\"[PriceDisplayWrapper] prepare. queryPrice: source=external userId={} request={}\",\n" +
                "                        ctx.getUserId4P(), JsonCodec.encode(request));\n" +
                "                priceDisplayLocalService.queryPrice(request, ctx);\n" +
                "                logQueryPrice(requireLogging, request);\n" +
                "                ctx.getFutureCtx().setNormalPriceFuture(FutureFactory.getFuture());\n" +
                "\n" +
                "                if (memberPriceWrapper.isMemberPriceProcessorEnable(ctx)) {\n" +
                "                    request.setScene(RequestSceneEnum.PROMO_DETAIL_NO_ITEM_DESC_WithMerchantMember.getScene());\n" +
                "                    LogUtils.info(\"[PriceDisplayWrapper] prepare. queryPrice: source=externalAndMerchantMember userId={} request={}\",\n" +
                "                            ctx.getUserId4P(), JsonCodec.encode(request));\n" +
                "                    priceDisplayLocalService.queryPrice(request, ctx);\n" +
                "                    logQueryPrice(requireLogging, request);\n" +
                "                    ctx.getFutureCtx().setMemberNormalPriceFuture(FutureFactory.getFuture());\n" +
                "                }\n" +
                "            }\n" +
                "        }\n" +
                "\n" +
                "        if (ctx.isHitCostEffectivePinTuan()) {\n" +
                "            BatchPriceRequest request = buildBatchPriceRequest(ctx);\n" +
                "            request.setScene(RequestSceneEnum.PROMO_DETAIL_DESC_WithDealPromo.getScene());\n" +
                "\n" +
                "            if (Objects.equals(ctx.getEnvCtx().getMpAppId(), MpAppIdEnum.MT_WEIXIN_MINIPROGRAM.getMpAppId())) {\n" +
                "                ClientEnv clientEnv = request.getClientEnv();\n" +
                "                if (clientEnv != null) {\n" +
                "                    clientEnv.setClientType(ClientTypeEnum.mt_weApp.getType());\n" +
                "                }\n" +
                "            }\n" +
                "\n" +
                "            fillPinTuanExtensionParams(request, ctx, ctx.isMt() ? ctx.getMtLongShopId() : ctx.getDpLongShopId());\n" +
                "            addPageSource(request);\n" +
                "            // 如果使用卡片样式\n" +
                "            if(ctx.isEnableCardStyle() || ctx.isEnableCardStyleV2()) {\n" +
                "                addPromoDescType(request);\n" +
                "            }\n" +
                "\n" +
                "            addPriceCipher(request,ctx);\n" +
                "            LogUtils.info(\"[PriceDisplayWrapper] cost effective pintuan prepare. queryPrice: source=external userId={} request={}\",\n" +
                "                    ctx.getUserId4P(), JsonCodec.encode(request));\n" +
                "            priceDisplayLocalService.batchQueryPriceByLongShopId(request, ctx);\n" +
                "            logBatchQueryPriceByLongShopId(requireLogging, request);\n" +
                "            ctx.getFutureCtx().setCostEffectivePriceFuture(FutureFactory.getFuture());\n" +
                "        }\n" +
                "\n" +
                "    }" + "public void process(DealCtx ctx) {\n" +
                "\n" +
                "            if (ctx.getEnvCtx().isExternalAndNoScene()) {\n" +
                "                PriceDisplayDTO normalPrice = getExternalNormalPrice(ctx);\n" +
                "                asyncPrintLog(\"[PriceDisplayWrapper] process. scene=externalAndNoScene userId={}, price={}, \"\n" +
                "                        + \"usedPromos={}, promoTag={}, identity={}, extPrices={}\", ctx.getUserId4P(), normalPrice);\n" +
                "                ctx.getPriceContext().setNormalPrice(normalPrice);\n" +
                "                return;\n" +
                "            }\n" +
                "\n" +
                "            if (!ctx.isExternal() || ctx.getEnvCtx().isExternalAndEnabled(MiniProgramSceneEnum.MEMBER_CARD.getPromoScene())) {\n" +
                "                if (ctx.getPriceContext().getDcCardMemberCard() != null) {\n" +
                "                    //如果有折扣卡再拿折扣卡价格\n" +
                "                    PriceDisplayDTO dcCardMemberPrice = extractResponse(getFutureResult(ctx.getFutureCtx().getJoyDiscountCardPriceFuture()));\n" +
                "                    asyncPrintLog(\"[PriceDisplayWrapper] process. scene=memberCard userId={}, price={}, \"\n" +
                "                            + \"usedPromos={}, promoTag={}, identity={}, extPrices={}\", ctx.getUserId4P(), dcCardMemberPrice);\n" +
                "                    ctx.getPriceContext().setDcCardMemberPrice(dcCardMemberPrice);\n" +
                "                }\n" +
                "            }\n" +
                "            if (!ctx.isExternal() || ctx.getEnvCtx().isExternalAndEnabled(MiniProgramSceneEnum.IDLE_PROMO.getPromoScene())) {\n" +
                "                PriceDisplayDTO idlePromoPrice = extractResponse(getFutureResult(ctx.getFutureCtx().getIdlePromoPriceFuture()));\n" +
                "                asyncPrintLog(\"[PriceDisplayWrapper] process. scene=idlePromo userId={}, price={}, usedPromos={}, \"\n" +
                "                        + \"promoTag={}, identity={}, extPrices={}\", ctx.getUserId4P(), idlePromoPrice);\n" +
                "                ctx.getPriceContext().setIdlePromoPrice(idlePromoPrice);\n" +
                "            }\n" +
                "            if (!ctx.isExternal() || ctx.getEnvCtx().isExternalAndEnabled(MiniProgramSceneEnum.NORMAL_PROMO.getPromoScene())) {\n" +
                "                if (isUseBatchPriceQuery(ctx) || DealCtxHelper.isOdpSource(ctx)) {\n" +
                "                    PriceResponse<Map<Long, List<PriceDisplayDTO>>> priceResponse = getFutureResult(ctx.getFutureCtx().getNormalPriceFuture());\n" +
                "                    PriceDisplayDTO normalPrice = extractAnyResponse(priceResponse);\n" +
                "                    asyncPrintLog(\"[PriceDisplayWrapper] process. scene=odp userId={}, price={}, usedPromos={}, \"\n" +
                "                            + \"promoTag={}, identity={}, extPrices={}\", ctx.getUserId4P(), normalPrice);\n" +
                "                    ctx.getPriceContext().setNormalPrice(normalPrice);\n" +
                "                    if (priceResponse != null && priceResponse.isSuccess() && StringUtils.isNotEmpty(priceResponse.getPriceSecretInfo())) {\n" +
                "                        ctx.getPriceContext().setNormalPriceCipher(priceResponse.getPriceSecretInfo());\n" +
                "                    }\n" +
                "                } else {\n" +
                "                    PriceResponse<PriceDisplayDTO> priceResponse = getFutureResult(ctx.getFutureCtx().getNormalPriceFuture());\n" +
                "                    PriceDisplayDTO normalPriceDTO = extractResponse(priceResponse);\n" +
                "                    asyncPrintLog(\"[PriceDisplayWrapper] process. scene=notOdp userId={}, price={}, usedPromos={}, \"\n" +
                "                            + \"promoTag={}, identity={}, extPrices={}\", ctx.getUserId4P(), normalPriceDTO);\n" +
                "                    ctx.getPriceContext().setNormalPrice(normalPriceDTO);\n" +
                "                    if (priceResponse != null && priceResponse.isSuccess() && StringUtils.isNotEmpty(priceResponse.getPriceSecretInfo())) {\n" +
                "                        ctx.getPriceContext().setNormalPriceCipher(priceResponse.getPriceSecretInfo());\n" +
                "                    }\n" +
                "\n" +
                "                    if (ctx.getShopMemberDiscountInfoDTO() != null && ctx.getFutureCtx().getMemberNormalPriceFuture() != null) {\n" +
                "                        PriceResponse<PriceDisplayDTO> memberNormalPriceResponse = getFutureResult(ctx.getFutureCtx().getMemberNormalPriceFuture());\n" +
                "                        PriceDisplayDTO memberNormalPriceDTO = extractResponse(memberNormalPriceResponse);\n" +
                "                        asyncPrintLog(\"[PriceDisplayWrapper] process. scene=memberNormalPrice userId={}, price={}, usedPromos={},\"\n" +
                "                                + \" promoTag={}, identity={}, extPrices={}\", ctx.getUserId4P(), memberNormalPriceDTO);\n" +
                "                        if (normalPriceDTO != null && memberNormalPriceDTO != null && memberNormalPriceDTO.getPrice().compareTo(normalPriceDTO.getPrice()) < 0) {\n" +
                "                            ctx.getPriceContext().setNormalPrice(memberNormalPriceDTO);\n" +
                "                            ctx.getPriceContext().setMemberNormalPrice(true);\n" +
                "                            ctx.getPriceContext().setNormalPriceCipher(memberNormalPriceResponse.getPriceSecretInfo());\n" +
                "                            ctx.getPriceContext().setDisplayInflateCoupon(displayInflateCoupon(normalPriceDTO));\n" +
                "                        }\n" +
                "                    }\n" +
                "                    ctx.getPriceContext().setHasExclusiveDeduction(hasExclusiveDeduction(ctx.getPriceContext().getNormalPrice()));\n" +
                "                }\n" +
                "            }\n" +
                "\n" +
                "            PriceResponse<Map<Long, List<PriceDisplayDTO>>> dealPromoPriceResponse = getFutureResult(ctx.getFutureCtx().getDealPromoPriceFuture());\n" +
                "            PriceDisplayDTO dealPromoPriceDTO = extractAnyResponse(dealPromoPriceResponse);\n" +
                "            asyncPrintLog(\"[PriceDisplayWrapper] process. scene=dealPromo userId={}, price={}, usedPromos={},\"\n" +
                "                    + \" promoTag={}, identity={}, extPrices={}, extendDisplayInfo={}, promoMap={}\", ctx.getUserId4P(), dealPromoPriceDTO);\n" +
                "            ctx.getPriceContext().setDealPromoPrice(dealPromoPriceDTO);\n" +
                "            if (dealPromoPriceResponse != null && dealPromoPriceResponse.isSuccess() && StringUtils.isNotEmpty(dealPromoPriceResponse.getPriceSecretInfo())){\n" +
                "                ctx.getPriceContext().setDealPromoPriceCipher(dealPromoPriceResponse.getPriceSecretInfo());\n" +
                "            }\n" +
                "            if (ctx.getShopMemberDiscountInfoDTO() != null && ctx.getFutureCtx().getShopMemberPromoPriceFuture() != null) {\n" +
                "                PriceResponse<Map<Long, List<PriceDisplayDTO>>> memberPriceResponse = getFutureResult(ctx.getFutureCtx().getShopMemberPromoPriceFuture());\n" +
                "                PriceDisplayDTO memberPriceDisplayDTO = extractAnyResponse(memberPriceResponse);\n" +
                "                asyncPrintLog(\"[PriceDisplayWrapper] process. scene=memberPromo userId={}, price={}, usedPromos={},\"\n" +
                "                        + \" promoTag={}, identity={}, extPrices={}\", ctx.getUserId4P(), memberPriceDisplayDTO);\n" +
                "                //如果会员价是最优组合\n" +
                "                if (memberPriceDisplayDTO !=null && dealPromoPriceDTO != null && memberPriceDisplayDTO.getPrice().compareTo(dealPromoPriceDTO.getPrice()) < 0) {\n" +
                "                    ctx.getPriceContext().setDealPromoPrice(memberPriceDisplayDTO);\n" +
                "                    ctx.getPriceContext().setOriginDealPromoPrice(dealPromoPriceDTO);\n" +
                "                    ctx.getPriceContext().setMemberPromoDealPrice(true);\n" +
                "                    ctx.getPriceContext().setDealPromoPriceCipher(memberPriceResponse.getPriceSecretInfo());\n" +
                "                    ctx.getPriceContext().setDisplayInflateCoupon(displayInflateCoupon(memberPriceDisplayDTO));\n" +
                "                }\n" +
                "            }\n" +
                "\n" +
                "            addJoyMarketPriceAB(ctx);\n" +
                "\n" +
                "            if (ctx.getFutureCtx().getAtmosphereBarAndGeneralPromoDetailFuture() != null) {\n" +
                "                PriceDisplayDTO atmosphereBarPromoPrice = extractAnyResponse(getFutureResult(ctx.getFutureCtx().getAtmosphereBarAndGeneralPromoDetailFuture()));\n" +
                "                asyncPrintLog(\"[PriceDisplayWrapper] process. scene=atmosphereBarPromo userId={}, price={}, usedPromos={},\"\n" +
                "                        + \" promoTag={}, identity={}, extPrices={}\", ctx.getUserId4P(), atmosphereBarPromoPrice);\n" +
                "                ctx.getPriceContext().setAtmosphereBarAndGeneralPromoDetailPrice(atmosphereBarPromoPrice);\n" +
                "            }\n" +
                "\n" +
                "            PromoDTO couponPromo = PromoHelper.getCouponPromo(ctx);\n" +
                "\n" +
                "            //有可领的券的时候做ab（该AB实验结束了）\n" +
                "            if (PromoHelper.canAssign(couponPromo)) {\n" +
                "                ctx.setModuleAbConfig(douHuBiz.resolveAb(ctx, \"buyBar\"));\n" +
                "            }\n" +
                "\n" +
                "            if (needJoyCardABTest(ctx)) {//JoyCard下线了\n" +
                "                List<ModuleAbConfig> moduleAbConfigs = ctx.getModuleAbConfigs();\n" +
                "\n" +
                "                if (moduleAbConfigs == null) {\n" +
                "                    moduleAbConfigs = Lists.newArrayList();\n" +
                "                }\n" +
                "\n" +
                "                moduleAbConfigs.add(douHuBiz.resolveAb(ctx, ctx.isMt() ? \"MTJoyCardPriceExp\" : \"DPJoyCardPriceExp\"));\n" +
                "                ctx.setModuleAbConfigs(moduleAbConfigs);\n" +
                "            }\n" +
                "\n" +
                "            if (ctx.isHitCostEffectivePinTuan()) {\n" +
                "                PriceDisplayDTO cePinTuanPrice = extractAnyResponse(getFutureResult(ctx.getFutureCtx().getCostEffectivePriceFuture()));\n" +
                "                fillCostEffectivePinTuanParams(ctx, cePinTuanPrice);\n" +
                "                asyncPrintLog(\"[PriceDisplayWrapper] process. scene=costEffectivePinTuan userId={}, price={}, usedPromos={}, \"\n" +
                "                        + \"promoTag={}, identity={}, extPrices={}\", ctx.getUserId4P(), cePinTuanPrice);\n" +
                "                ctx.getPriceContext().setCostEffectivePrice(cePinTuanPrice);\n" +
                "            }\n" +
                "        }";
    }

    @MdpLangModelFunction
    public String getLog(String str) {
        return "{\n" +
                "  \"mt_appkey\": \"com.sankuai.dzu.tpbase.dztgdetailweb\",\n" +
                "  \"mt_thread\": \"priceExecutor-thread-97\",\n" +
                "  \"mt_level\": \"INFO\",\n" +
                "  \"traceId__\": \"-*******************\",\n" +
                "  \"mt_datetime\": \"2024-12-03 12:55:10+0800\",\n" +
                "  \"mt_servername\": \"set-pj-dzs-tpbase-dztgdetailweb18.mt\",\n" +
                "  \"mt_logger_name\": \"com.dianping.mobile.mapi.dztgdetail.util.LogUtils\",\n" +
                "  \"es_timestamp\": \"2024/12/03 12:55:10 +0800\",\n" +
                "  \"message\": \" [PriceDisplayWrapper] process. scene=notOdp userId=**********, price=94.00, usedPromos=[{\"identity\":{\"promoId\":**********,\"promoType\":5,\"promoTypeDesc\":\"吃喝玩乐红包\",\"sourceType\":1,\"promoShowType\":\"MAGICAL_MEMBER_PLATFORM_COUPON\"},\"amount\":5,\"priceThrough\":true,\"canAssign\":false,\"extendDesc\":\"吃喝玩乐红包\",\"startTime\":\"Nov 16, 2024 12:39:11 PM\",\"endTime\":\"Dec 16, 2024 11:59:59 PM\",\"icon\":\"https://p0.meituan.net/travelcube/d5ba4048dc741674e6497f4c0c9ef55e1173.png\",\"couponTitle\":\"吃喝玩乐红包\",\"isNewUser\":false,\"priceLimitDesc\":\"无门槛\",\"minConsumptionAmount\":0,\"useTimeDesc\":\"2024.11.16-2024.12.16\",\"promoIdentity\":\"magicalMemberCoupon\",\"promoStatus\":0,\"couponGroupId\":\"**********\",\"couponId\":\"400896103327238748\",\"couponValueType\":0,\"couponValueText\":\"5\",\"promoTextDTO\":{\"title\":\"5元无门槛券\",\"subTitle\":\"吃喝玩乐红包\",\"icon\":\"https://p0.meituan.net/travelcube/d5ba4048dc741674e6497f4c0c9ef55e1173.png\",\"promoDivideType\":\"MAGICAL_MEMBER_PLATFORM_COUPON\",\"promoDivideTypeDesc\":\"神会员平台券\"},\"amountShareDetail\":{\"1\":5,\"2\":0},\"promotionExplanatoryTags\":[3],\"promotionOtherInfoMap\":{\"ASSIGNED_STATUS\":\"ALREADY_ASSIGNED\",\"COUPON_PACKAGE_ID\":\"DOQZGcd1IgRdGTg43J8TWEy+S19PGZ8ne0Jt+b6nm4E\\\\u003d\",\"ASSET_TYPE\":\"1\",\"TSP_COUPON_GROUP_ID\":\"22271997903881\",\"TSP_COUPON_ID\":\"1419356427684\"},\"promotionDisplayTextMap\":{\"topLeftIcon\":\"https://p0.meituan.net/ingee/6f4c76f970849530aadf296332059e0d11217.png\"}}], promoTag=已优惠5, identity=ProductIdentity(spuId=0, spuSceneType=0, productId=**********, shopId=null, productType=1, cityId=0, price=null, marketPrice=null, skuId=**********, categoryId=0, productIdL=0, skuIdL=0, priceTime=0, forceSpecifyInfo=null, extParams={}, tyingSaleDTOs=null, priceTrendType=0, relatedResourceDTOs=null), extPrices=[ExtPriceDisplayDTO(extPriceType=1, extPricePromoAmount=0, extPrice=99.00, extPriceTitle=全网低价, tyingSaleDTOS=null)]\\n\",\n" +
                "  \"mt_millisecond\": 773,\n" +
                "  \"es_datetime\": \"2024-12-03 12:55:10.773\"\n" +
                "}\n" +
                "{\n" +
                "  \"mt_appkey\": \"com.sankuai.dzu.tpbase.dztgdetailweb\",\n" +
                "  \"mt_thread\": \"priceExecutor-thread-97\",\n" +
                "  \"mt_level\": \"INFO\",\n" +
                "  \"traceId__\": \"-*******************\",\n" +
                "  \"mt_datetime\": \"2024-12-03 12:55:10+0800\",\n" +
                "  \"mt_servername\": \"set-pj-dzs-tpbase-dztgdetailweb18.mt\",\n" +
                "  \"mt_logger_name\": \"com.dianping.mobile.mapi.dztgdetail.util.LogUtils\",\n" +
                "  \"es_timestamp\": \"2024/12/03 12:55:10 +0800\",\n" +
                "  \"message\": \" [PriceDisplayWrapper] process. scene=dealPromo userId=**********, price=94.00, usedPromos=[{\"identity\":{\"promoId\":**********,\"promoType\":11,\"promoTypeDesc\":\"团购优惠201元\",\"sourceType\":1,\"promoShowType\":\"DEAL_PROMO\"},\"amount\":201.00,\"tag\":\"团购优惠201元\",\"description\":\"团购优惠201元\",\"priceThrough\":true,\"canAssign\":false,\"extendDesc\":\"团购优惠，下单立省201元\",\"icon\":\"https://p0.meituan.net/travelcube/1a7a0f263848c0490651d375fe869d114478.png\",\"isNewUser\":false,\"promoStatus\":0,\"couponValueType\":0,\"promoTextDTO\":{\"title\":\"团购优惠201元\",\"subTitle\":\"团购优惠，下单立省201元\",\"icon\":\"https://p0.meituan.net/travelcube/1a7a0f263848c0490651d375fe869d114478.png\",\"promoDivideType\":\"DEAL_PROMO\"}},{\"identity\":{\"promoId\":**********,\"promoType\":5,\"promoTypeDesc\":\"吃喝玩乐红包\",\"sourceType\":1,\"promoShowType\":\"MAGICAL_MEMBER_PLATFORM_COUPON\"},\"amount\":5,\"priceThrough\":true,\"canAssign\":false,\"extendDesc\":\"吃喝玩乐红包\",\"startTime\":\"Nov 16, 2024 12:39:11 PM\",\"endTime\":\"Dec 16, 2024 11:59:59 PM\",\"icon\":\"https://p0.meituan.net/travelcube/d5ba4048dc741674e6497f4c0c9ef55e1173.png\",\"couponTitle\":\"吃喝玩乐红包\",\"isNewUser\":false,\"priceLimitDesc\":\"无门槛\",\"minConsumptionAmount\":0,\"useTimeDesc\":\"2024.11.16-2024.12.16\",\"promoIdentity\":\"magicalMemberCoupon\",\"promoStatus\":0,\"couponGroupId\":\"**********\",\"couponId\":\"400896103327238748\",\"couponValueType\":0,\"couponValueText\":\"5\",\"promoTextDTO\":{\"title\":\"5元无门槛券\",\"subTitle\":\"吃喝玩乐红包\",\"icon\":\"https://p0.meituan.net/travelcube/d5ba4048dc741674e6497f4c0c9ef55e1173.png\",\"promoDivideType\":\"MAGICAL_MEMBER_PLATFORM_COUPON\",\"promoDivideTypeDesc\":\"神会员平台券\"},\"amountShareDetail\":{\"1\":5,\"2\":0},\"promotionExplanatoryTags\":[3],\"promotionOtherInfoMap\":{\"ASSIGNED_STATUS\":\"ALREADY_ASSIGNED\",\"COUPON_PACKAGE_ID\":\"DOQZGcd1IgRdGTg43J8TWEy+S19PGZ8ne0Jt+b6nm4E\\\\u003d\",\"ASSET_TYPE\":\"1\",\"TSP_COUPON_GROUP_ID\":\"22271997903881\",\"TSP_COUPON_ID\":\"1419356427684\"},\"promotionDisplayTextMap\":{\"topLeftIcon\":\"https://p0.meituan.net/ingee/6f4c76f970849530aadf296332059e0d11217.png\"}}], promoTag=特惠促销共省¥206, identity=ProductIdentity(spuId=0, spuSceneType=0, productId=**********, shopId=null, productType=1, cityId=0, price=null, marketPrice=null, skuId=**********, categoryId=0, productIdL=0, skuIdL=0, priceTime=0, forceSpecifyInfo=null, extParams={}, tyingSaleDTOs=null, priceTrendType=0, relatedResourceDTOs=null), extPrices=[ExtPriceDisplayDTO(extPriceType=1, extPricePromoAmount=0, extPrice=99.00, extPriceTitle=全网低价, tyingSaleDTOS=null), ExtPriceDisplayDTO(extPriceType=2, extPricePromoAmount=null, extPrice=0.32, extPriceTitle=单价, tyingSaleDTOS=null)], extendDisplayInfo={\"couponWalletInfo\":\"{\\\\\"optionals\\\\\":[\\\\\"COUPON_PACKAGE_LIST\\\\\"]}\",\"magicalMemberCouponLabel\":\"{\\\\\"priority\\\\\":0,\\\\\"magicalMemberCouponTag\\\\\":\\\\\"神券\\\\\",\\\\\"inflateShowText\\\\\":\\\\\"立减\\\\\",\\\\\"reduceMoney\\\\\":\\\\\"5\\\\\",\\\\\"status\\\\\":null,\\\\\"showType\\\\\":\\\\\"basic_poi_has_pre_inflated_mmc\\\\\"}\"}, promoMap={\"1\":[{\"identity\":{\"promoId\":**********,\"promoType\":5,\"promoTypeDesc\":\"吃喝玩乐红包\",\"sourceType\":1},\"amount\":5,\"priceThrough\":true,\"canAssign\":false,\"extendDesc\":\"吃喝玩乐红包, 减5元\",\"startTime\":\"Nov 16, 2024 12:39:11 PM\",\"endTime\":\"Dec 16, 2024 11:59:59 PM\",\"couponTitle\":\"吃喝玩乐红包\",\"isNewUser\":false,\"priceLimitDesc\":\"无门槛\",\"minConsumptionAmount\":0,\"useTimeDesc\":\"2024.11.16-2024.12.16\",\"promoIdentity\":\"magicalMemberCoupon\",\"promoStatus\":0,\"couponGroupId\":\"**********\",\"couponId\":\"400896103327439011\",\"couponValueType\":0,\"couponValueText\":\"5\",\"amountShareDetail\":{\"1\":5,\"2\":0},\"promotionExplanatoryTags\":[3],\"promotionOtherInfoMap\":{\"ASSIGNED_STATUS\":\"ALREADY_ASSIGNED\",\"COUPON_PACKAGE_ID\":\"DOQZGcd1IgRdGTg43J8TWEy+S19PGZ8ne0Jt+b6nm4E\\\\u003d\",\"ASSET_TYPE\":\"1\",\"TSP_COUPON_GROUP_ID\":\"22271997903881\",\"TSP_COUPON_ID\":\"1419356427687\"},\"promotionDisplayTextMap\":{\"topLeftIcon\":\"https://p0.meituan.net/ingee/6f4c76f970849530aadf296332059e0d11217.png\"}},{\"identity\":{\"promoId\":**********,\"promoType\":5,\"promoTypeDesc\":\"吃喝玩乐红包\",\"sourceType\":1},\"amount\":5,\"priceThrough\":true,\"canAssign\":false,\"extendDesc\":\"吃喝玩乐红包, 减5元\",\"startTime\":\"Nov 16, 2024 12:39:11 PM\",\"endTime\":\"Dec 16, 2024 11:59:59 PM\",\"couponTitle\":\"吃喝玩乐红包\",\"isNewUser\":false,\"priceLimitDesc\":\"无门槛\",\"minConsumptionAmount\":0,\"useTimeDesc\":\"2024.11.16-2024.12.16\",\"promoIdentity\":\"magicalMemberCoupon\",\"promoStatus\":0,\"couponGroupId\":\"**********\",\"couponId\":\"400896103327435112\",\"couponValueType\":0,\"couponValueText\":\"5\",\"amountShareDetail\":{\"1\":5,\"2\":0},\"promotionExplanatoryTags\":[3],\"promotionOtherInfoMap\":{\"ASSIGNED_STATUS\":\"ALREADY_ASSIGNED\",\"COUPON_PACKAGE_ID\":\"DOQZGcd1IgRdGTg43J8TWEy+S19PGZ8ne0Jt+b6nm4E\\\\u003d\",\"ASSET_TYPE\":\"1\",\"TSP_COUPON_GROUP_ID\":\"22271997903881\",\"TSP_COUPON_ID\":\"1419356427682\"},\"promotionDisplayTextMap\":{\"topLeftIcon\":\"https://p0.meituan.net/ingee/6f4c76f970849530aadf296332059e0d11217.png\"}},{\"identity\":{\"promoId\":**********,\"promoType\":5,\"promoTypeDesc\":\"吃喝玩乐红包\",\"sourceType\":1},\"amount\":5,\"priceThrough\":true,\"canAssign\":false,\"extendDesc\":\"吃喝玩乐红包, 减5元\",\"startTime\":\"Nov 16, 2024 12:39:11 PM\",\"endTime\":\"Dec 16, 2024 11:59:59 PM\",\"couponTitle\":\"吃喝玩乐红包\",\"isNewUser\":false,\"priceLimitDesc\":\"无门槛\",\"minConsumptionAmount\":0,\"useTimeDesc\":\"2024.11.16-2024.12.16\",\"promoIdentity\":\"magicalMemberCoupon\",\"promoStatus\":0,\"couponGroupId\":\"**********\",\"couponId\":\"400896103327368023\",\"couponValueType\":0,\"couponValueText\":\"5\",\"amountShareDetail\":{\"1\":5,\"2\":0},\"promotionExplanatoryTags\":[3],\"promotionOtherInfoMap\":{\"ASSIGNED_STATUS\":\"ALREADY_ASSIGNED\",\"COUPON_PACKAGE_ID\":\"DOQZGcd1IgRdGTg43J8TWEy+S19PGZ8ne0Jt+b6nm4E\\\\u003d\",\"ASSET_TYPE\":\"1\",\"TSP_COUPON_GROUP_ID\":\"22271997903881\",\"TSP_COUPON_ID\":\"1419356427686\"},\"promotionDisplayTextMap\":{\"topLeftIcon\":\"https://p0.meituan.net/ingee/6f4c76f970849530aadf296332059e0d11217.png\"}},{\"identity\":{\"promoId\":**********,\"promoType\":5,\"promoTypeDesc\":\"吃喝玩乐红包\",\"sourceType\":1},\"amount\":5,\"priceThrough\":true,\"canAssign\":false,\"extendDesc\":\"吃喝玩乐红包, 减5元\",\"startTime\":\"Nov 16, 2024 12:39:11 PM\",\"endTime\":\"Dec 16, 2024 11:59:59 PM\",\"couponTitle\":\"吃喝玩乐红包\",\"isNewUser\":false,\"priceLimitDesc\":\"无门槛\",\"minConsumptionAmount\":0,\"useTimeDesc\":\"2024.11.16-2024.12.16\",\"promoIdentity\":\"magicalMemberCoupon\",\"promoStatus\":0,\"couponGroupId\":\"**********\",\"couponId\":\"400896103327347296\",\"couponValueType\":0,\"couponValueText\":\"5\",\"amountShareDetail\":{\"1\":5,\"2\":0},\"promotionExplanatoryTags\":[3],\"promotionOtherInfoMap\":{\"ASSIGNED_STATUS\":\"ALREADY_ASSIGNED\",\"COUPON_PACKAGE_ID\":\"DOQZGcd1IgRdGTg43J8TWEy+S19PGZ8ne0Jt+b6nm4E\\\\u003d\",\"ASSET_TYPE\":\"1\",\"TSP_COUPON_GROUP_ID\":\"22271997903881\",\"TSP_COUPON_ID\":\"1419356427690\"},\"promotionDisplayTextMap\":{\"topLeftIcon\":\"https://p0.meituan.net/ingee/6f4c76f970849530aadf296332059e0d11217.png\"}},{\"identity\":{\"promoId\":**********,\"promoType\":5,\"promoTypeDesc\":\"吃喝玩乐红包\",\"sourceType\":1},\"amount\":5,\"priceThrough\":true,\"canAssign\":false,\"extendDesc\":\"吃喝玩乐红包, 减5元\",\"startTime\":\"Nov 16, 2024 12:39:11 PM\",\"endTime\":\"Dec 16, 2024 11:59:59 PM\",\"couponTitle\":\"吃喝玩乐红包\",\"isNewUser\":false,\"priceLimitDesc\":\"无门槛\",\"minConsumptionAmount\":0,\"useTimeDesc\":\"2024.11.16-2024.12.16\",\"promoIdentity\":\"magicalMemberCoupon\",\"promoStatus\":0,\"couponGroupId\":\"**********\",\"couponId\":\"400896103327339476\",\"couponValueType\":0,\"couponValueText\":\"5\",\"amountShareDetail\":{\"1\":5,\"2\":0},\"promotionExplanatoryTags\":[3],\"promotionOtherInfoMap\":{\"ASSIGNED_STATUS\":\"ALREADY_ASSIGNED\",\"COUPON_PACKAGE_ID\":\"DOQZGcd1IgRdGTg43J8TWEy+S19PGZ8ne0Jt+b6nm4E\\\\u003d\",\"ASSET_TYPE\":\"1\",\"TSP_COUPON_GROUP_ID\":\"22271997903881\",\"TSP_COUPON_ID\":\"1419356427689\"},\"promotionDisplayTextMap\":{\"topLeftIcon\":\"https://p0.meituan.net/ingee/6f4c76f970849530aadf296332059e0d11217.png\"}},{\"identity\":{\"promoId\":**********,\"promoType\":5,\"promoTypeDesc\":\"吃喝玩乐红包\",\"sourceType\":1},\"amount\":5,\"priceThrough\":true,\"canAssign\":false,\"extendDesc\":\"吃喝玩乐红包, 减5元\",\"startTime\":\"Nov 16, 2024 12:39:11 PM\",\"endTime\":\"Dec 16, 2024 11:59:59 PM\",\"couponTitle\":\"吃喝玩乐红包\",\"isNewUser\":false,\"priceLimitDesc\":\"无门槛\",\"minConsumptionAmount\":0,\"useTimeDesc\":\"2024.11.16-2024.12.16\",\"promoIdentity\":\"magicalMemberCoupon\",\"promoStatus\":0,\"couponGroupId\":\"**********\",\"couponId\":\"400896103327271301\",\"couponValueType\":0,\"couponValueText\":\"5\",\"amountShareDetail\":{\"1\":5,\"2\":0},\"promotionExplanatoryTags\":[3],\"promotionOtherInfoMap\":{\"ASSIGNED_STATUS\":\"ALREADY_ASSIGNED\",\"COUPON_PACKAGE_ID\":\"DOQZGcd1IgRdGTg43J8TWEy+S19PGZ8ne0Jt+b6nm4E\\\\u003d\",\"ASSET_TYPE\":\"1\",\"TSP_COUPON_GROUP_ID\":\"22271997903881\",\"TSP_COUPON_ID\":\"1419356427688\"},\"promotionDisplayTextMap\":{\"topLeftIcon\":\"https://p0.meituan.net/ingee/6f4c76f970849530aadf296332059e0d11217.png\"}},{\"identity\":{\"promoId\":**********,\"promoType\":5,\"promoTypeDesc\":\"吃喝玩乐红包\",\"sourceType\":1},\"amount\":5,\"priceThrough\":true,\"canAssign\":false,\"extendDesc\":\"吃喝玩乐红包, 减5元\",\"startTime\":\"Nov 16, 2024 12:39:11 PM\",\"endTime\":\"Dec 16, 2024 11:59:59 PM\",\"couponTitle\":\"吃喝玩乐红包\",\"isNewUser\":false,\"priceLimitDesc\":\"无门槛\",\"minConsumptionAmount\":0,\"useTimeDesc\":\"2024.11.16-2024.12.16\",\"promoIdentity\":\"magicalMemberCoupon\",\"promoStatus\":0,\"couponGroupId\":\"**********\",\"couponId\":\"400896103327271300\",\"couponValueType\":0,\"couponValueText\":\"5\",\"amountShareDetail\":{\"1\":5,\"2\":0},\"promotionExplanatoryTags\":[3],\"promotionOtherInfoMap\":{\"ASSIGNED_STATUS\":\"ALREADY_ASSIGNED\",\"COUPON_PACKAGE_ID\":\"DOQZGcd1IgRdGTg43J8TWEy+S19PGZ8ne0Jt+b6nm4E\\\\u003d\",\"ASSET_TYPE\":\"1\",\"TSP_COUPON_GROUP_ID\":\"22271997903881\",\"TSP_COUPON_ID\":\"1419356427691\"},\"promotionDisplayTextMap\":{\"topLeftIcon\":\"https://p0.meituan.net/ingee/6f4c76f970849530aadf296332059e0d11217.png\"}},{\"identity\":{\"promoId\":**********,\"promoType\":5,\"promoTypeDesc\":\"吃喝玩乐红包\",\"sourceType\":1},\"amount\":5,\"priceThrough\":true,\"canAssign\":false,\"extendDesc\":\"吃喝玩乐红包, 减5元\",\"startTime\":\"Nov 16, 2024 12:39:11 PM\",\"endTime\":\"Dec 16, 2024 11:59:59 PM\",\"couponTitle\":\"吃喝玩乐红包\",\"isNewUser\":false,\"priceLimitDesc\":\"无门槛\",\"minConsumptionAmount\":0,\"useTimeDesc\":\"2024.11.16-2024.12.16\",\"promoIdentity\":\"magicalMemberCoupon\",\"promoStatus\":0,\"couponGroupId\":\"**********\",\"couponId\":\"400896103327267471\",\"couponValueType\":0,\"couponValueText\":\"5\",\"amountShareDetail\":{\"1\":5,\"2\":0},\"promotionExplanatoryTags\":[3],\"promotionOtherInfoMap\":{\"ASSIGNED_STATUS\":\"ALREADY_ASSIGNED\",\"COUPON_PACKAGE_ID\":\"DOQZGcd1IgRdGTg43J8TWEy+S19PGZ8ne0Jt+b6nm4E\\\\u003d\",\"ASSET_TYPE\":\"1\",\"TSP_COUPON_GROUP_ID\":\"22271997903881\",\"TSP_COUPON_ID\":\"1419*********\"},\"promotionDisplayTextMap\":{\"topLeftIcon\":\"https://p0.meituan.net/ingee/6f4c76f970849530aadf296332059e0d11217.png\"}},{\"identity\":{\"promoId\":**********,\"promoType\":5,\"promoTypeDesc\":\"吃喝玩乐红包\",\"sourceType\":1,\"promoShowType\":\"MAGICAL_MEMBER_PLATFORM_COUPON\"},\"amount\":5,\"priceThrough\":true,\"canAssign\":false,\"extendDesc\":\"吃喝玩乐红包\",\"startTime\":\"Nov 16, 2024 12:39:11 PM\",\"endTime\":\"Dec 16, 2024 11:59:59 PM\",\"icon\":\"https://p0.meituan.net/travelcube/d5ba4048dc741674e6497f4c0c9ef55e1173.png\",\"couponTitle\":\"吃喝玩乐红包\",\"isNewUser\":false,\"priceLimitDesc\":\"无门槛\",\"minConsumptionAmount\":0,\"useTimeDesc\":\"2024.11.16-2024.12.16\",\"promoIdentity\":\"magicalMemberCoupon\",\"promoStatus\":0,\"couponGroupId\":\"**********\",\"couponId\":\"400896103327238748\",\"couponValueType\":0,\"couponValueText\":\"5\",\"promoTextDTO\":{\"title\":\"5元无门槛券\",\"subTitle\":\"吃喝玩乐红包\",\"icon\":\"https://p0.meituan.net/travelcube/d5ba4048dc741674e6497f4c0c9ef55e1173.png\",\"promoDivideType\":\"MAGICAL_MEMBER_PLATFORM_COUPON\",\"promoDivideTypeDesc\":\"神会员平台券\"},\"amountShareDetail\":{\"1\":5,\"2\":0},\"promotionExplanatoryTags\":[3],\"promotionOtherInfoMap\":{\"ASSIGNED_STATUS\":\"ALREADY_ASSIGNED\",\"COUPON_PACKAGE_ID\":\"DOQZGcd1IgRdGTg43J8TWEy+S19PGZ8ne0Jt+b6nm4E\\\\u003d\",\"ASSET_TYPE\":\"1\",\"TSP_COUPON_GROUP_ID\":\"22271997903881\",\"TSP_COUPON_ID\":\"1419356427684\"},\"promotionDisplayTextMap\":{\"topLeftIcon\":\"https://p0.meituan.net/ingee/6f4c76f970849530aadf296332059e0d11217.png\"}}]}\\n\",\n" +
                "  \"mt_millisecond\": 773,\n" +
                "  \"es_datetime\": \"2024-12-03 12:55:10.773\"\n" +
                "}\n" +
                "{\n" +
                "  \"mt_appkey\": \"com.sankuai.dzu.tpbase.dztgdetailweb\",\n" +
                "  \"mt_thread\": \"priceExecutor-thread-97\",\n" +
                "  \"mt_level\": \"INFO\",\n" +
                "  \"traceId__\": \"-*******************\",\n" +
                "  \"mt_datetime\": \"2024-12-03 12:55:10+0800\",\n" +
                "  \"mt_servername\": \"set-pj-dzs-tpbase-dztgdetailweb18.mt\",\n" +
                "  \"mt_logger_name\": \"com.dianping.mobile.mapi.dztgdetail.util.LogUtils\",\n" +
                "  \"es_timestamp\": \"2024/12/03 12:55:10 +0800\",\n" +
                "  \"message\": \" [PriceDisplayWrapper] process. scene=idlePromo userId=**********, price=99.00, usedPromos=[], promoTag=null, identity=ProductIdentity(spuId=0, spuSceneType=0, productId=**********, shopId=null, productType=1, cityId=0, price=null, marketPrice=null, skuId=**********, categoryId=0, productIdL=0, skuIdL=0, priceTime=0, forceSpecifyInfo=null, extParams={}, tyingSaleDTOs=null, priceTrendType=0, relatedResourceDTOs=null), extPrices=null\\n\",\n" +
                "  \"mt_millisecond\": 769,\n" +
                "  \"es_datetime\": \"2024-12-03 12:55:10.769\"\n" +
                "}\n{\n" +
                "  \"mt_appkey\": \"com.sankuai.dzu.tpbase.dztgdetailweb\",\n" +
                "  \"mt_thread\": \"priceExecutor-thread-97\",\n" +
                "  \"mt_level\": \"INFO\",\n" +
                "  \"traceId__\": \"-*******************\",\n" +
                "  \"mt_datetime\": \"2024-12-03 12:55:10+0800\",\n" +
                "  \"mt_servername\": \"set-pj-dzs-tpbase-dztgdetailweb18.mt\",\n" +
                "  \"mt_logger_name\": \"com.dianping.mobile.mapi.dztgdetail.util.LogUtils\",\n" +
                "  \"es_timestamp\": \"2024/12/03 12:55:10 +0800\",\n" +
                "  \"message\": \" [PriceDisplayWrapper] prepare. batchQueryPriceByLongShopId: source=showMarketPrice userId=********** request={\"shopProductIds\":null,\"longShopId2ProductIds\":{\"190418053\":[{\"spuId\":0,\"spuSceneType\":0,\"productId\":**********,\"shopId\":null,\"productType\":1,\"cityId\":0,\"price\":null,\"marketPrice\":null,\"skuId\":0,\"categoryId\":0,\"productIdL\":0,\"skuIdL\":0,\"priceTime\":0,\"forceSpecifyInfo\":null,\"extParams\":{},\"tyingSaleDTOs\":null,\"priceTrendType\":0,\"relatedResourceDTOs\":null,\"lskuId\":0,\"lproductId\":**********}]},\"longShopId2PoiIdDTO\":null,\"userId\":**********,\"scene\":400200,\"clientEnv\":{\"userIdDTO\":{\"dpRealId\":**********,\"mtRealId\":**********,\"dpVirtualId\":**********,\"mtVirtualId\":**********},\"unionId\":\"9a19b400003e4924bd8ef89cd46d8056a171027297307149752\",\"uuid\":\"00000000000000A85A0E97E6143ABB8CCFE8EDB42034EA171027297306125139\",\"version\":\"12.26.407\",\"cityId\":122,\"gpsCityId\":122,\"gpsCityIdDTO\":null,\"appointCityId\":0,\"clientType\":200501,\"mobile\":null,\"ip\":null,\"mtgsig\":null,\"fingerPrint\":null,\"longitude\":119.6144181,\"latitude\":39.9408039,\"gpsCoordinateType\":\"GCJ02\",\"riskPlatform\":null,\"wxOpenId\":null,\"wxAppId\":null,\"wxVersion\":null},\"startTime\":null,\"days\":0,\"extension\":{\"页面来源\":\"8\",\"position\":\"2104\",\"promoDescType\":\"newTuanDetail\",\"pageName\":\"DealGroupNormalDetail\"},\"specPromo\":null}\\n\",\n" +
                "  \"mt_millisecond\": 691,\n" +
                "  \"es_datetime\": \"2024-12-03 12:55:10.691\"\n" +
                "}\n" +
                "\n" +
                "{\n" +
                "  \"mt_appkey\": \"com.sankuai.dzu.tpbase.dztgdetailweb\",\n" +
                "  \"mt_thread\": \"priceExecutor-thread-97\",\n" +
                "  \"mt_level\": \"INFO\",\n" +
                "  \"traceId__\": \"-*******************\",\n" +
                "  \"mt_datetime\": \"2024-12-03 12:55:10+0800\",\n" +
                "  \"mt_servername\": \"set-pj-dzs-tpbase-dztgdetailweb18.mt\",\n" +
                "  \"mt_logger_name\": \"com.dianping.mobile.mapi.dztgdetail.util.LogUtils\",\n" +
                "  \"es_timestamp\": \"2024/12/03 12:55:10 +0800\",\n" +
                "  \"message\": \" [PriceDisplayWrapper] prepare. queryPrice: source=external userId=********** request={\"identity\":{\"spuId\":0,\"spuSceneType\":0,\"productId\":**********,\"shopId\":null,\"productType\":1,\"cityId\":0,\"price\":null,\"marketPrice\":null,\"skuId\":0,\"categoryId\":0,\"productIdL\":0,\"skuIdL\":0,\"priceTime\":0,\"forceSpecifyInfo\":null,\"extParams\":{},\"tyingSaleDTOs\":null,\"priceTrendType\":0,\"relatedResourceDTOs\":null,\"lskuId\":0,\"lproductId\":**********},\"shopId\":0,\"longShopId\":190418053,\"longShopId2PoiIdDTO\":null,\"userId\":**********,\"specPromo\":null,\"clientEnv\":{\"userIdDTO\":{\"dpRealId\":**********,\"mtRealId\":**********,\"dpVirtualId\":**********,\"mtVirtualId\":**********},\"unionId\":\"9a19b400003e4924bd8ef89cd46d8056a171027297307149752\",\"uuid\":\"00000000000000A85A0E97E6143ABB8CCFE8EDB42034EA171027297306125139\",\"version\":\"12.26.407\",\"cityId\":122,\"gpsCityId\":122,\"gpsCityIdDTO\":null,\"appointCityId\":0,\"clientType\":200501,\"mobile\":null,\"ip\":null,\"mtgsig\":null,\"fingerPrint\":null,\"longitude\":119.6144181,\"latitude\":39.9408039,\"gpsCoordinateType\":null,\"riskPlatform\":null,\"wxOpenId\":null,\"wxAppId\":null,\"wxVersion\":null},\"scene\":400002,\"extension\":{\"页面来源\":\"8\",\"position\":\"2104\",\"promoDescType\":\"newTuanDetail\",\"pageName\":\"DealGroupNormalDetail\"},\"startTime\":null,\"templateId\":271}\\n\",\n" +
                "  \"mt_millisecond\": 694,\n" +
                "  \"es_datetime\": \"2024-12-03 12:55:10.694\"\n" +
                "}\n" +
                "\n" +
                "\n" +
                "{\n" +
                "  \"mt_appkey\": \"com.sankuai.dzu.tpbase.dztgdetailweb\",\n" +
                "  \"mt_thread\": \"priceExecutor-thread-97\",\n" +
                "  \"mt_level\": \"INFO\",\n" +
                "  \"traceId__\": \"-*******************\",\n" +
                "  \"mt_datetime\": \"2024-12-03 12:55:10+0800\",\n" +
                "  \"mt_servername\": \"set-pj-dzs-tpbase-dztgdetailweb18.mt\",\n" +
                "  \"mt_logger_name\": \"com.dianping.mobile.mapi.dztgdetail.util.LogUtils\",\n" +
                "  \"es_timestamp\": \"2024/12/03 12:55:10 +0800\",\n" +
                "  \"message\": \" [PriceDisplayWrapper] prepare. queryPrice: source=externalAndMerchantMember userId=********** request={\"identity\":{\"spuId\":0,\"spuSceneType\":0,\"productId\":**********,\"shopId\":null,\"productType\":1,\"cityId\":0,\"price\":null,\"marketPrice\":null,\"skuId\":0,\"categoryId\":0,\"productIdL\":0,\"skuIdL\":0,\"priceTime\":0,\"forceSpecifyInfo\":null,\"extParams\":{},\"tyingSaleDTOs\":null,\"priceTrendType\":0,\"relatedResourceDTOs\":null,\"lskuId\":0,\"lproductId\":**********},\"shopId\":0,\"longShopId\":190418053,\"longShopId2PoiIdDTO\":null,\"userId\":**********,\"specPromo\":null,\"clientEnv\":{\"userIdDTO\":{\"dpRealId\":**********,\"mtRealId\":**********,\"dpVirtualId\":**********,\"mtVirtualId\":**********},\"unionId\":\"9a19b400003e4924bd8ef89cd46d8056a171027297307149752\",\"uuid\":\"00000000000000A85A0E97E6143ABB8CCFE8EDB42034EA171027297306125139\",\"version\":\"12.26.407\",\"cityId\":122,\"gpsCityId\":122,\"gpsCityIdDTO\":null,\"appointCityId\":0,\"clientType\":200501,\"mobile\":null,\"ip\":null,\"mtgsig\":null,\"fingerPrint\":null,\"longitude\":119.6144181,\"latitude\":39.9408039,\"gpsCoordinateType\":null,\"riskPlatform\":null,\"wxOpenId\":null,\"wxAppId\":null,\"wxVersion\":null},\"scene\":400006,\"extension\":{\"页面来源\":\"8\",\"position\":\"2104\",\"promoDescType\":\"newTuanDetail\",\"pageName\":\"DealGroupNormalDetail\"},\"startTime\":null,\"templateId\":271}\\n\",\n" +
                "  \"mt_millisecond\": 694,\n" +
                "  \"es_datetime\": \"2024-12-03 12:55:10.694\"\n" +
                "}\n" +
                "\n" +
                "{\n" +
                "  \"mt_appkey\": \"com.sankuai.dzu.tpbase.dztgdetailweb\",\n" +
                "  \"mt_thread\": \"priceExecutor-thread-97\",\n" +
                "  \"mt_level\": \"INFO\",\n" +
                "  \"traceId__\": \"-*******************\",\n" +
                "  \"mt_datetime\": \"2024-12-03 12:55:10+0800\",\n" +
                "  \"mt_servername\": \"set-pj-dzs-tpbase-dztgdetailweb18.mt\",\n" +
                "  \"mt_logger_name\": \"com.dianping.mobile.mapi.dztgdetail.util.LogUtils\",\n" +
                "  \"es_timestamp\": \"2024/12/03 12:55:10 +0800\",\n" +
                "  \"message\": \" [PriceDisplayWrapper] prepare. queryPrice: source=memberCard userId=********** request={\"identity\":{\"spuId\":0,\"spuSceneType\":0,\"productId\":**********,\"shopId\":null,\"productType\":1,\"cityId\":0,\"price\":null,\"marketPrice\":null,\"skuId\":0,\"categoryId\":0,\"productIdL\":0,\"skuIdL\":0,\"priceTime\":0,\"forceSpecifyInfo\":null,\"extParams\":{},\"tyingSaleDTOs\":null,\"priceTrendType\":0,\"relatedResourceDTOs\":null,\"lskuId\":0,\"lproductId\":**********},\"shopId\":0,\"longShopId\":190418053,\"longShopId2PoiIdDTO\":null,\"userId\":**********,\"specPromo\":null,\"clientEnv\":{\"userIdDTO\":{\"dpRealId\":**********,\"mtRealId\":**********,\"dpVirtualId\":**********,\"mtVirtualId\":**********},\"unionId\":\"9a19b400003e4924bd8ef89cd46d8056a171027297307149752\",\"uuid\":\"00000000000000A85A0E97E6143ABB8CCFE8EDB42034EA171027297306125139\",\"version\":\"12.26.407\",\"cityId\":122,\"gpsCityId\":122,\"gpsCityIdDTO\":null,\"appointCityId\":0,\"clientType\":200501,\"mobile\":null,\"ip\":null,\"mtgsig\":null,\"fingerPrint\":null,\"longitude\":119.6144181,\"latitude\":39.9408039,\"gpsCoordinateType\":null,\"riskPlatform\":null,\"wxOpenId\":null,\"wxAppId\":null,\"wxVersion\":null},\"scene\":300100,\"extension\":{\"页面来源\":\"8\",\"position\":\"2104\",\"promoDescType\":\"newTuanDetail\"},\"startTime\":null,\"templateId\":271}\\n\",\n" +
                "  \"mt_millisecond\": 693,\n" +
                "  \"es_datetime\": \"2024-12-03 12:55:10.693\"\n" +
                "}\n" +
                "\n" +
                "{\n" +
                "  \"mt_appkey\": \"com.sankuai.dzu.tpbase.dztgdetailweb\",\n" +
                "  \"mt_thread\": \"priceExecutor-thread-97\",\n" +
                "  \"mt_level\": \"INFO\",\n" +
                "  \"traceId__\": \"-*******************\",\n" +
                "  \"mt_datetime\": \"2024-12-03 12:55:10+0800\",\n" +
                "  \"mt_servername\": \"set-pj-dzs-tpbase-dztgdetailweb18.mt\",\n" +
                "  \"mt_logger_name\": \"com.dianping.mobile.mapi.dztgdetail.util.LogUtils\",\n" +
                "  \"es_timestamp\": \"2024/12/03 12:55:10 +0800\",\n" +
                "  \"message\": \" [PriceDisplayWrapper] prepare. queryPrice: source=idlePromo userId=********** request={\"identity\":{\"spuId\":0,\"spuSceneType\":0,\"productId\":**********,\"shopId\":null,\"productType\":1,\"cityId\":0,\"price\":null,\"marketPrice\":null,\"skuId\":0,\"categoryId\":0,\"productIdL\":0,\"skuIdL\":0,\"priceTime\":0,\"forceSpecifyInfo\":null,\"extParams\":{},\"tyingSaleDTOs\":null,\"priceTrendType\":0,\"relatedResourceDTOs\":null,\"lskuId\":0,\"lproductId\":**********},\"shopId\":0,\"longShopId\":190418053,\"longShopId2PoiIdDTO\":null,\"userId\":**********,\"specPromo\":{\"promoId\":0,\"promoType\":2,\"promoTypeDesc\":null,\"sourceType\":0,\"promoShowType\":null},\"clientEnv\":{\"userIdDTO\":{\"dpRealId\":**********,\"mtRealId\":**********,\"dpVirtualId\":**********,\"mtVirtualId\":**********},\"unionId\":\"9a19b400003e4924bd8ef89cd46d8056a171027297307149752\",\"uuid\":\"00000000000000A85A0E97E6143ABB8CCFE8EDB42034EA171027297306125139\",\"version\":\"12.26.407\",\"cityId\":122,\"gpsCityId\":122,\"gpsCityIdDTO\":null,\"appointCityId\":0,\"clientType\":200501,\"mobile\":null,\"ip\":null,\"mtgsig\":null,\"fingerPrint\":null,\"longitude\":119.6144181,\"latitude\":39.9408039,\"gpsCoordinateType\":null,\"riskPlatform\":null,\"wxOpenId\":null,\"wxAppId\":null,\"wxVersion\":null},\"scene\":300100,\"extension\":{\"页面来源\":\"8\",\"position\":\"2104\",\"promoDescType\":\"newTuanDetail\"},\"startTime\":null,\"templateId\":271}\\n\",\n" +
                "  \"mt_millisecond\": 693,\n" +
                "  \"es_datetime\": \"2024-12-03 12:55:10.693\"\n" +
                "}\n" +
                "\n" +
                "{\n" +
                "  \"mt_appkey\": \"com.sankuai.dzu.tpbase.dztgdetailweb\",\n" +
                "  \"mt_thread\": \"priceExecutor-thread-97\",\n" +
                "  \"mt_level\": \"INFO\",\n" +
                "  \"traceId__\": \"-*******************\",\n" +
                "  \"mt_datetime\": \"2024-12-03 12:55:10+0800\",\n" +
                "  \"mt_servername\": \"set-pj-dzs-tpbase-dztgdetailweb18.mt\",\n" +
                "  \"mt_logger_name\": \"com.dianping.mobile.mapi.dztgdetail.util.LogUtils\",\n" +
                "  \"es_timestamp\": \"2024/12/03 12:55:10 +0800\",\n" +
                "  \"message\": \" [PriceDisplayWrapper] prepare. batchQueryPriceByLongShopId: source=merchantMember userId=********** request={\"shopProductIds\":null,\"longShopId2ProductIds\":{\"190418053\":[{\"spuId\":0,\"spuSceneType\":0,\"productId\":**********,\"shopId\":null,\"productType\":1,\"cityId\":0,\"price\":null,\"marketPrice\":null,\"skuId\":0,\"categoryId\":0,\"productIdL\":0,\"skuIdL\":0,\"priceTime\":0,\"forceSpecifyInfo\":null,\"extParams\":{},\"tyingSaleDTOs\":null,\"priceTrendType\":0,\"relatedResourceDTOs\":null,\"lskuId\":0,\"lproductId\":**********}]},\"longShopId2PoiIdDTO\":null,\"userId\":**********,\"scene\":400203,\"clientEnv\":{\"userIdDTO\":{\"dpRealId\":**********,\"mtRealId\":**********,\"dpVirtualId\":**********,\"mtVirtualId\":**********},\"unionId\":\"9a19b400003e4924bd8ef89cd46d8056a171027297307149752\",\"uuid\":\"00000000000000A85A0E97E6143ABB8CCFE8EDB42034EA171027297306125139\",\"version\":\"12.26.407\",\"cityId\":122,\"gpsCityId\":122,\"gpsCityIdDTO\":null,\"appointCityId\":0,\"clientType\":200501,\"mobile\":null,\"ip\":null,\"mtgsig\":null,\"fingerPrint\":null,\"longitude\":119.6144181,\"latitude\":39.9408039,\"gpsCoordinateType\":\"GCJ02\",\"riskPlatform\":null,\"wxOpenId\":null,\"wxAppId\":null,\"wxVersion\":null},\"startTime\":null,\"days\":0,\"extension\":{\"promoDetailTemplate\":\"oldVersion\",\"页面来源\":\"8\",\"position\":\"2104\",\"promoDescType\":\"newTuanDetail\",\"pageName\":\"DealGroupNormalDetail\"},\"specPromo\":null}\\n\",\n" +
                "  \"mt_millisecond\": 692,\n" +
                "  \"es_datetime\": \"2024-12-03 12:55:10.692\"\n" +
                "}";
    }

}

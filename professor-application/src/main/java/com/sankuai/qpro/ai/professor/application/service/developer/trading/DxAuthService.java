package com.sankuai.qpro.ai.professor.application.service.developer.trading;

import com.sankuai.xm.openplatform.auth.entity.AccessTokenResp;
import com.sankuai.xm.openplatform.auth.entity.AppAuthInfo;
import com.sankuai.xm.openplatform.auth.service.XmAuthServiceI;
import lombok.extern.slf4j.Slf4j;
import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2024/12/4
 */
@Service
@Slf4j
public class DxAuthService {
    private long expireTime = 0;
    private String accessToken = "";
    private final static long FIVE_MINUTES = 1000 * 60 * 5;

    @Value("${dx.appId}")
    private String appId;

    @Value("${dx.appSecret}")
    private String appSecret;


    @Resource
    private XmAuthServiceI.Iface xmAuthService;

    public String getToken() {
        long nowTime = System.currentTimeMillis();
        if (expireTime - FIVE_MINUTES > nowTime) {
            return accessToken;
        }
        synchronized (this) {
            nowTime = System.currentTimeMillis();
            if (expireTime - FIVE_MINUTES > nowTime) {
                return accessToken;
            }
            AppAuthInfo appAuthInfo = new AppAuthInfo();
            appAuthInfo.setAppkey(appId);
            appAuthInfo.setAppSecret(appSecret);
            try {
                AccessTokenResp resp = xmAuthService.accessToken(appAuthInfo);
                log.info("生成开放平台Token:{}", resp);
                if(resp.status.getCode()==0) {
                    accessToken = resp.getAccessToken().getToken();
                    expireTime = resp.getAccessToken().getExpireTime();
                }else{
                    throw new RuntimeException("生成开放平台Token失败");
                }
                return accessToken;
            } catch (TException e) {
                log.error("生成开放平台Token失败", e);
                throw new RuntimeException("生成开放平台Token失败", e);
            }
        }
    }
}

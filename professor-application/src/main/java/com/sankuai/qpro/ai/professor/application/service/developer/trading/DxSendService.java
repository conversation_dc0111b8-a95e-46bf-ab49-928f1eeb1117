package com.sankuai.qpro.ai.professor.application.service.developer.trading;

import com.alibaba.fastjson.JSON;
import com.sankuai.dxenterprise.open.gateway.service.dx.api.DxService;
import com.sankuai.dxenterprise.open.gateway.service.dx.api.req.GetUidByEmpIdReq;
import com.sankuai.dxenterprise.open.gateway.service.dx.api.resp.GetUidByEmpIdResp;
import com.sankuai.qpro.ai.professor.application.model.developer.TextBodyModel;
import com.sankuai.qpro.ai.professor.application.utils.SerializeUtils;
import com.sankuai.xm.openplatform.api.entity.SendChatMsgByRobotReq;
import com.sankuai.xm.openplatform.api.entity.SendChatMsgByRobotRes;
import com.sankuai.xm.openplatform.api.entity.SendMsgInfo;
import com.sankuai.xm.openplatform.api.service.open.XmOpenMessageServiceI;
import com.sankuai.xm.openplatform.common.enums.ResCodeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/12/4
 */
@Slf4j
@Service
public class DxSendService {

    @Resource
    private XmOpenMessageServiceI.Iface xmOpenMsgService;

    @Autowired
    private DxAuthService dxAuthService;

    @Resource
    private DxService dxService;

    public void sendChatMsg(String msg, List<Long> receiverIds) {
        String accessToken = dxAuthService.getToken();
        SendChatMsgByRobotReq req = new SendChatMsgByRobotReq();
        req.setReceiverIds(receiverIds);
        SendMsgInfo info = new SendMsgInfo();
        info.setType("text");
        TextBodyModel textBody = new TextBodyModel();
        textBody.setText(msg);
        info.setBody(SerializeUtils.toJsonStr(textBody));
        req.setSendMsgInfo(info);
        try {
            SendChatMsgByRobotRes res = xmOpenMsgService.sendChatMsgByRobot(accessToken, req);
            log.info("发送单聊消息,req={},resp={}", JSON.toJSONString(req), JSON.toJSONString(res));
        } catch (TException e) {
            log.error("发送单聊消息异常,e={},req={}", e, JSON.toJSONString(req));
        }
    }

    public Long getUidByEmpId(Long empId) {
        Long uid = null;
        GetUidByEmpIdReq req = new GetUidByEmpIdReq();
        req.setEmpId(empId);
        String accessToken = dxAuthService.getToken();
        try {
            GetUidByEmpIdResp resp = dxService.getUidByEmpId(accessToken, req);
            if (ResCodeEnum.SUCCESS.getCode() == resp.getStatus().getCode()) {
                uid = resp.getData().getUid();
            }
            log.info("根据empId获取uid,req={},resp={}", JSON.toJSONString(req), JSON.toJSONString(resp));
        } catch (Exception e) {
            log.error("根据empId获取uid失败,e={},req={}", e, empId);
        }
        return uid;
    }
}

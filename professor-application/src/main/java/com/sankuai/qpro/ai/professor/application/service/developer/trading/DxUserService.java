package com.sankuai.qpro.ai.professor.application.service.developer.trading;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.sankuai.qpro.ai.professor.application.utils.SerializeUtils;
import com.sankuai.xm.openplatform.api.entity.UserDetail;
import com.sankuai.xm.openplatform.api.entity.UserInfoResp;
import com.sankuai.xm.openplatform.api.entity.UserInfosReq;
import com.sankuai.xm.openplatform.api.entity.UserInfosResp;
import com.sankuai.xm.openplatform.api.service.open.XmOpenUserServiceI;
import com.sankuai.xm.openplatform.common.enums.ResCodeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @author: wuwenqiang
 * @create: 2025-06-05
 * @description:
 */
@Service
@Slf4j
public class DxUserService {
    @Resource
    private XmOpenUserServiceI.Iface xmOpenUserService;

    @Resource
    private DxAuthService dxAuthService;

    @Resource
    private DxSendService dxSendService;

    public UserDetail getUserDetailByEmpId(Long empId) {
        if (Objects.isNull(empId)) {
            return null;
        }
        String token = dxAuthService.getToken();
        try {
            Long uid = dxSendService.getUidByEmpId(empId);
            if (Objects.isNull(uid)) {
                return null;
            }
            UserInfosResp resp = xmOpenUserService.getBatchUserInfos(token, buildUserInfosReq(Lists.newArrayList(uid)));
            if (ResCodeEnum.SUCCESS.getCode() != resp.getStatus().getCode()) {
                log.error("查询大象用户信息失败, empId={}, resp:{}", empId, resp);
                return null;
            }
            return buildUserDetail(resp, uid);
        } catch (Exception e) {
            log.error("查询大象用户信息失败", e);
        }
        return null;
    }


    private UserInfosReq buildUserInfosReq(List<Long> uids) {
        UserInfosReq userInfosReq = new UserInfosReq();
        userInfosReq.setUids(Sets.newHashSet(uids));
        return userInfosReq;
    }

    private UserDetail buildUserDetail(UserInfosResp resp, Long uid) {
        if (resp.getUserList() == null) {
            return null;
        }
        return resp.getUserList().stream().filter(userDetail -> Objects.equals(userDetail.getUid(), uid)).findFirst().orElse(null);
    }
}

package com.sankuai.qpro.ai.professor.application.service.developer.trading;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.dianping.rhino.Rhino;
import com.dianping.rhino.threadpool.DefaultThreadPoolProperties;
import com.dianping.rhino.threadpool.ThreadPool;
import com.google.common.collect.Lists;
import com.meituan.mdp.boot.starter.thrift.annotation.MdpThriftServer;
import com.sankuai.xm.openplatform.callback.api.XmOpenCallbackServiceI;
import com.sankuai.xm.openplatform.callback.constant.CallBackEventTypeEnum;
import com.sankuai.xm.openplatform.callback.event.OpenEvent;
import com.sankuai.xm.openplatform.callback.event.data.GroupMsgEvent;
import com.sankuai.xm.openplatform.callback.event.data.PubChatEvent;
import com.sankuai.xm.openplatform.callback.event.data.PubMsgEvent;
import com.sankuai.xm.openplatform.common.entity.EmptyResp;
import com.sankuai.xm.openplatform.common.entity.RespStatus;
import com.sankuai.xm.openplatform.common.enums.ResCodeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 * @date 2024/12/4
 */
@Slf4j
@MdpThriftServer
public class XmOpenCallbackServiceImpl implements XmOpenCallbackServiceI.Iface {

    @Autowired
    private DxSendService dxSendService;

    @Autowired
    private DeveloperAgent agentService;

    private static final ThreadPool threadPool = Rhino.newThreadPool("threadPool-event", DefaultThreadPoolProperties.Setter().withCoreSize(10).withMaxSize(20));

    @Override
    public EmptyResp eventCallback(int eventType, String jsonEvent) throws TException {
        log.info("event = {}", jsonEvent);
        // 实现你的逻辑
        try {
            threadPool.execute(() -> handOpenEvent(jsonEvent));
        } catch (Exception e) {
            log.error("hand event fail", e);
        }

        EmptyResp response = new EmptyResp();
        RespStatus status = new RespStatus();
        status.setCode(ResCodeEnum.SUCCESS.getCode());
        status.setMsg(ResCodeEnum.SUCCESS.getMsg());
        response.setStatus(status);
        return response;
    }

    private void handOpenEvent(String jsonEvent) {
        OpenEvent<JSONObject> openEvent = JSON.parseObject(jsonEvent, OpenEvent.class);
        CallBackEventTypeEnum eventTypeEnum = openEvent.getEventTypeEnum();
        JSONObject data = openEvent.getData();
        switch (eventTypeEnum){
            case GROUP_MESSAGE:
                GroupMsgEvent GroupMsgEvent = JSON.parseObject(data.toJSONString(), GroupMsgEvent.class);
//                handOpenEventImpl.handGroupMsg(GroupMsgEvent);
                break;
            case ROBOT_SINGLE_CHAT_MESSAGE:
                PubMsgEvent pubMsgEvent = JSON.parseObject(data.toJSONString(), PubMsgEvent.class);
                handPubMsg(pubMsgEvent);
                break;
            case PUB_CHAT:
                PubChatEvent pubChatEvent = JSON.parseObject(data.toJSONString(), PubChatEvent.class);
//                handOpenEventImpl.handPubChat(pubChatEvent);
                break;
        }
    }

    private void handPubMsg(PubMsgEvent pubMsgEvent) {
        String response = agentService.execute(pubMsgEvent.getMessage());
        dxSendService.sendChatMsg(response, Lists.newArrayList(pubMsgEvent.getFromId()));
    }
}

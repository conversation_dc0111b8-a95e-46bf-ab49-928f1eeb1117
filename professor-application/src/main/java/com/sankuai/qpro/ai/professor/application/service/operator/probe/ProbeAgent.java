package com.sankuai.qpro.ai.professor.application.service.operator.probe;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.TextNode;
import com.google.common.collect.Lists;
import com.meituan.mdp.langmodel.api.function.MdpChatFunction;
import com.meituan.mdp.langmodel.api.message.AssistantMessage;
import com.meituan.mdp.langmodel.api.message.Message;
import com.meituan.mdp.langmodel.api.message.SystemMessage;
import com.meituan.mdp.langmodel.api.message.UserMessage;
import com.meituan.mdp.langmodel.component.exceptions.FunctionExecutionException;
import com.meituan.mdp.langmodel.component.function.MdpFunctionRegistry;
import com.meituan.mdp.langmodel.component.model.chat.FridayChatModelUseTool;
import com.meituan.mdp.langmodel.component.model.chat.OpenAIChatModel;
import com.sankuai.qpro.ai.professor.api.enums.MessageStatusEnum;
import com.sankuai.qpro.ai.professor.application.constants.ToolTypeConstant;
import com.sankuai.qpro.ai.professor.api.enums.SegmentEnum;
import com.sankuai.qpro.ai.professor.application.lion.ProbeCacheInLion;
import com.sankuai.qpro.ai.professor.application.model.operator.exception.model.ModelException;
import com.sankuai.qpro.ai.professor.application.model.operator.exception.model.generate.NaturalLanguageGenerateModelException;
import com.sankuai.qpro.ai.professor.application.model.operator.exception.model.timeout.TimeoutModelException;
import com.sankuai.qpro.ai.professor.application.model.operator.probe.agent.plan.AgentPlanModel;
import com.sankuai.qpro.ai.professor.application.model.operator.probe.chat.AgentOutputModel;
import com.sankuai.qpro.ai.professor.application.model.operator.probe.agent.category.param.ProbeCategoryParam;
import com.sankuai.qpro.ai.professor.application.model.operator.probe.agent.plan.ValidateModel;
import com.sankuai.qpro.ai.professor.application.prompt.ProbeAgentPrompt;
import com.sankuai.qpro.ai.professor.application.service.operator.probe.chat.ConversationParamService;
import com.sankuai.qpro.ai.professor.application.service.operator.probe.agent.ProbeCategoryAgent;
import com.sankuai.qpro.ai.professor.application.service.operator.probe.chat.ConversationStoreService;
import com.sankuai.qpro.ai.professor.application.utils.LLMExceptionUtil;
import com.sankuai.qpro.ai.professor.application.utils.SerializeUtils;
import com.sankuai.qpro.ai.professor.entity.ConversationMsgEntity;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.lang.reflect.Method;
import java.net.SocketTimeoutException;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 探针Agent入口
 * <AUTHOR>
 * @date 2025/1/2
 */
@Slf4j
@Service
public class ProbeAgent {

    @Autowired
    @Qualifier("gpt4oMiniModel")
    private OpenAIChatModel openAIChatModel;

    @Autowired
    @Qualifier("gpt4oModel")
    private OpenAIChatModel gpt4oModel;

    @Autowired
    @Qualifier("claudeModel")
    private FridayChatModelUseTool claudeModel;

    @Autowired
    @Qualifier("deepSeekR1Model")
    private FridayChatModelUseTool deepSeekR1Model;

    @Autowired
    private ProbeAgentPrompt agentPrompt;

    @Autowired
    private ProbeCategoryAgent probeCategoryAgent;

    @Autowired
    private ConversationParamService conversationParamService;

    @Autowired
    private ProbeCacheInLion cache;

    @Autowired
    private ConversationStoreService conversationStoreService;

    public AgentOutputModel execute(List<Message> msgList, Long conversationId, String configUnit) {
        // 1 先取 cache
        AgentOutputModel cachedAgentOutputModel = getCachedModel(msgList, conversationId, configUnit);
        if (cachedAgentOutputModel != null) {
            return cachedAgentOutputModel;
        }

        // 2 根据当前会话信息推理出对应的TaskAgent进行处理
        AssistantMessage assistantMessage = null;
        try {
            assistantMessage = getTaskAgentResp(filterMsgList(msgList, 1), conversationId, configUnit);
            if (!LLMExceptionUtil.validateMsg(assistantMessage)) {
                Exception modelException = LLMExceptionUtil.extractModelException(assistantMessage);
                Throwable cause = LLMExceptionUtil.parseModelFunctionException(modelException);
                if (cause instanceof SocketTimeoutException) {
                    cause = new TimeoutModelException(cause);
                }
                log.error("[ProbeAgent] 模型异常, conversationId={}, msgList={}", conversationId, SerializeUtils.toJsonStr(msgList), cause);
                if (cause instanceof TimeoutModelException) {
                    return AgentOutputModel.from("小舟思考超时啦，请重试", SegmentEnum.ERROR, MessageStatusEnum.ERROR.getCode());
                } else if (cause instanceof ModelException) {
                    // 模型异常，重新问用户上一个问题
                    return repeatPreviousReplay(conversationId, configUnit);
                }
                // 程序执行异常或其他异常，直接将问题暴露给用户
                return AgentOutputModel.from("程序执行异常，请联系管理员查看这个报错:" + cause.getMessage(), SegmentEnum.ERROR, MessageStatusEnum.ERROR.getCode());
            }
        } catch (Throwable e) {
            log.error("[ProbeAgent] 执行异常, conversationId={}, msgList={}", conversationId, SerializeUtils.toJsonStr(msgList), e);
            return AgentOutputModel.from("程序执行异常，请联系管理员查看这个报错:" + e.getMessage(), SegmentEnum.ERROR, MessageStatusEnum.ERROR.getCode());
        }

        AgentOutputModel agentOutputModel = SerializeUtils.toObj(assistantMessage.getContent(), AgentOutputModel.class);

        if (agentOutputModel == null) {
            return AgentOutputModel.from(StringUtils.isEmpty(assistantMessage.getContent()) ? "当前提问无效，请按照引导进行规范输入" : assistantMessage.getContent(), SegmentEnum.ERROR, MessageStatusEnum.ERROR.getCode());
        }

        return agentOutputModel;
    }

    private AgentOutputModel getCachedModel(List<Message> msgList, Long conversationId, String configUnit) {
        String lastUserMsg = getLastUserMsg(msgList);
        if (StringUtils.isBlank(lastUserMsg)) {
            return null;
        }

        if (lastUserMsg.contains(cache.CONF_UNIT_DESC)) {
            return AgentOutputModel.from(probeCategoryAgent.getCategoryParam(configUnit), SegmentEnum.CATEGORY);
        }

        if (lastUserMsg.contains(cache.CHOOSE_CATEGORY_DESC)) {
            ProbeCategoryParam param = new ProbeCategoryParam();
            param.setConversationId(conversationId);
            param.setConfigUnit(configUnit);
            param.setPoiCategoryIdList(Lists.newArrayList(cache.POI_CATEGORY_ID));
            param.setPoiCategoryNameList(Lists.newArrayList(cache.POI_CATEGORY_NAME));
            param.setProductCategoryId(cache.PRODUCT_CATEGORY_ID);
            param.setProductCategoryName(cache.PRODUCT_CATEGORY_NAME);
            conversationParamService.updateCategory(conversationId, param.getPoiCategoryIdList(), param.getPoiCategoryNameList(), param.getProductCategoryId(), param.getProductCategoryName());
            try {
                return AgentOutputModel.from(probeCategoryAgent.getOnlineConfig(param), SegmentEnum.CATEGORY);
            } catch (Exception e) {
                // 直接将问题暴露给用户
                log.error("[ProbeAgent] 模型异常, conversationId={}, msgList={}", conversationId, SerializeUtils.toJsonStr(msgList), e);
                return AgentOutputModel.from("程序执行异常，请联系管理员查看这个报错:" + e.getMessage(), SegmentEnum.ERROR, MessageStatusEnum.ERROR.getCode());
            }
        }

        return null;
    }

    private String getLastUserMsg(List<Message> msgList) {
        if (CollectionUtils.isEmpty(msgList)) {
            return null;
        }

        int size = msgList.size();
        for (int i = size - 1; i >= 0; i--) {
            Message message = msgList.get(i);
            if (message instanceof UserMessage) {
                return (String)message.getContent();
            }
        }
        return null;
    }

    private AgentOutputModel repeatPreviousReplay(Long conversationId, String configUnit) {
        ConversationMsgEntity assistantMsgEntity = conversationStoreService.queryLatestValidAIMessage(conversationId);
        if (Objects.isNull(assistantMsgEntity) || StringUtils.isBlank(assistantMsgEntity.getMessage())) {
            return AgentOutputModel.from(probeCategoryAgent.getCategoryParam(configUnit), SegmentEnum.CATEGORY);
        }
        return AgentOutputModel.from(assistantMsgEntity.getMessage(), SegmentEnum.getByCode(assistantMsgEntity.getDialogSegment()));
    }

    /**
     * 返回最近 3 条消息
     */
    private List<Message> filterMsgList(List<Message> msgList, int msgSize) {
        List<Message> result = Lists.newArrayList(msgList);
        if (result.size() <= msgSize) {
            return result;
        }
        // 如果过滤后结果的第一条消息为AI的消息，则剔除（当返回出现异常，则可能会出现第一条消息为AI消息）
        List<Message> filterResult = result.subList(result.size() - msgSize, result.size());
        int startUserIndex = 0;
        for (int i = 0; i < filterResult.size(); i++) {
            Message message = filterResult.get(i);
            if (message instanceof UserMessage) {
                startUserIndex = i;
                break;
            }
        }
        return filterResult.subList(startUserIndex, filterResult.size());
    }

    private AssistantMessage getTaskAgentResp(List<Message> msgList, Long conversationId, String configUnit) throws Exception {
        // 1.推理 & 复检
        log.info("[ProbeAgent] 开始执行推理使用的TaskAgent, conversationId={}, msgList={}", conversationId, SerializeUtils.toJsonStr(msgList));
        AssistantMessage taskAgentReason = chooseTaskAgentReasoning(msgList, conversationId, configUnit);

        // 2. 判断推理子Agent是否在提供范围内
        AgentPlanModel taskAgentPlan = SerializeUtils.toObj(taskAgentReason.getContent(), AgentPlanModel.class);
        if (!judgeSubAgentInRange(taskAgentPlan)) {
            log.warn("[ProbeAgent] 推理出的子Agent不在范围内或者用户意图不在处理范围内, conversationId={}, msgList={}", conversationId, SerializeUtils.toJsonStr(msgList));
            return AssistantMessage.from("抱歉，小舟无法处理您的问题，请换个问题再看看呢？");
        }
        MdpChatFunction function = MdpFunctionRegistry.getFunction(taskAgentPlan.getAgent());
        log.info("[ProbeAgent] 已找到待执行子Agent={}, conversationId={}, msgList={}", function.getName(), conversationId, SerializeUtils.toJsonStr(msgList));

        // 3. 提取子Agent参数并执行
        try {
            String subAgentParam = extractSubAgentParam(msgList, function, conversationId, configUnit);
            Object execResult = executeSubAgent(function, subAgentParam);
            log.info("[ProbeAgent] 子Agent执行结果={}, conversationId={}, msgList={}", SerializeUtils.toJsonStr(execResult), conversationId, SerializeUtils.toJsonStr(msgList));
            return AssistantMessage.from(SerializeUtils.toJsonStr(execResult));
        } catch (Exception e) {
            log.error("[ProbeAgent] 执行子Agent失败, conversationId={}, msgList={}", conversationId, SerializeUtils.toJsonStr(msgList), e);
            return AssistantMessage.from(new FunctionExecutionException(e));
        }
    }

    /**
     * 推理选择子Agent
     */
    public AssistantMessage chooseTaskAgentReasoning(List<Message> msgList, Long conversationId, String configUnit) {
        AssistantMessage taskAgentReason = null;
        int times = 0;
        while (times++ <= 3) {
            // 1.1 规划推理
            taskAgentReason = chooseTaskAgent(msgList, conversationId, configUnit, times);
            log.info("[ProbeAgent] taskAgentResp={}, times={}, conversationId={}", SerializeUtils.toJsonStr(taskAgentReason.getContent()), times, conversationId);

            // 1.2 复检
            log.info("[ProbeAgent] 开始复检，times={}", times);
            ValidateModel validateModel = getReasoningValidateResult(msgList, taskAgentReason, conversationId);

            if (validateModel == null) {
                log.warn("[ProbeAgent] 复检结束，复检结果为空, time={}, conversationId={}", times, conversationId);
                continue;
            }
            log.info("[ProbeAgent] 复检结束，validateModel={}, times={}, conversationId={}", SerializeUtils.toJsonStr(validateModel), times, conversationId);

            // 复检通过
            if (validateModel.isPassValidate()) {
                return taskAgentReason;
            }

            // 复检未通过，追加未通过的原因
            msgList = getReChooseMsgList(msgList, validateModel);
        }
        return taskAgentReason;
    }

    private AssistantMessage chooseTaskAgent(List<Message> list, Long conversationId, String configUnit, int times) {
        List<Message> msgList = Lists.newArrayList(buildReasoningSystemMessage(conversationId, configUnit));
        msgList.addAll(list);

        if (times == 1) {
            return openAIChatModel.sendMessages(msgList);
        }

        return gpt4oModel.sendMessages(msgList);
//        return claudeModel.sendMessages(msgList);
    }

    private ValidateModel getReasoningValidateResult(List<Message> msgList, AssistantMessage routeMsg, Long conversationId) {
        List<Message> list = Lists.newArrayList(buildReasoningRevisorSystemMessage());
        list.addAll(msgList);

        // 构建路由现状并添加到消息列表中
        if (routeMsg == null || StringUtils.isBlank(routeMsg.getContent())) {
            list.add(UserMessage.from("路由专家未给出任何结果，请一步步分析用户的消息并给出合适的回复。"));
        } else {
            list.add(UserMessage.from(String.format("路由专家当前的路由结果是「%s」, 请校验路由专家是否推理正确", routeMsg.getContent())));
        }
        AssistantMessage assistantMessage = claudeModel.sendMessages(list);
//        AssistantMessage assistantMessage = deepSeekR1Model.sendMessages(list);
        if (!LLMExceptionUtil.validateMsg(assistantMessage)) {
            Exception exception = LLMExceptionUtil.extractModelException(assistantMessage);
            log.error("[ProbeAgent] getReasoningValidateResult 路由复检异常, msgList={}, conversationId={} ", SerializeUtils.toJsonStr(list), conversationId, exception);
            return null;
        }
        log.info("[ProbeAgent] getValidateResult 复检结果：{}", SerializeUtils.toJsonStr(assistantMessage.getContent()));

        return SerializeUtils.toObj(assistantMessage.getContent(), ValidateModel.class);
    }

    // Todo：仅测试，待删除
    /**
     * 推理选择子Agent
     */
    public AssistantMessage chooseTaskAgentReasoning(List<Message> msgList, Long conversationId, String configUnit, String reasonModelName, String validateModelName) {
        AssistantMessage taskAgentReason = null;
        int times = 0;
        while (times++ <= 3) {
            // 1.1 规划推理
            taskAgentReason = chooseTaskAgent(msgList, conversationId, configUnit, times, reasonModelName);
            log.info("[ProbeAgent] taskAgentResp={}, times={}, conversationId={}", SerializeUtils.toJsonStr(taskAgentReason.getContent()), times, conversationId);

            // 1.2 复检
            log.info("[ProbeAgent] 开始复检，times={}", times);
            ValidateModel validateModel = getReasoningValidateResult(msgList, taskAgentReason, conversationId, validateModelName);

            if (validateModel == null) {
                log.warn("[ProbeAgent] 复检结束，复检结果为空, time={}, conversationId={}", times, conversationId);
                continue;
            }
            log.info("[ProbeAgent] 复检结束，validateModel={}, times={}, conversationId={}", SerializeUtils.toJsonStr(validateModel), times, conversationId);

            // 复检通过
            if (validateModel.isPassValidate()) {
                return taskAgentReason;
            }

            // 复检未通过，追加未通过的原因
            msgList = getReChooseMsgList(msgList, validateModel);
        }
        return taskAgentReason;
    }

    private AssistantMessage chooseTaskAgent(List<Message> list, Long conversationId, String configUnit, int times, String reasonModelName) {
        List<Message> msgList = Lists.newArrayList(buildReasoningSystemMessage(conversationId, configUnit));
        msgList.addAll(list);

        if ("gpt".equals(reasonModelName)) {
            if (times == 1) {
                return openAIChatModel.sendMessages(msgList);
            }

            return gpt4oModel.sendMessages(msgList);
        } else if ("claude".equals(reasonModelName)) {
            return claudeModel.sendMessages(msgList);
        } else if ("deepSeekR1".equals(reasonModelName)) {
            return deepSeekR1Model.sendMessages(msgList);
        }

        return claudeModel.sendMessages(msgList);
    }

    private ValidateModel getReasoningValidateResult(List<Message> msgList, AssistantMessage routeMsg, Long conversationId, String validateModelName) {
        List<Message> list = Lists.newArrayList(buildReasoningRevisorSystemMessage());
        list.addAll(msgList);

        // 构建路由现状并添加到消息列表中
        if (routeMsg == null || StringUtils.isBlank(routeMsg.getContent())) {
            list.add(UserMessage.from("路由专家未给出任何结果，请一步步分析用户的消息并给出合适的回复。"));
        } else {
            list.add(UserMessage.from(String.format("路由专家当前的路由结果是「%s」, 请校验路由专家是否推理正确", routeMsg.getContent())));
        }

        AssistantMessage assistantMessage = "deepSeekR1".equals(validateModelName) ? deepSeekR1Model.sendMessages(list) : claudeModel.sendMessages(list);
        if (!LLMExceptionUtil.validateMsg(assistantMessage)) {
            Exception exception = LLMExceptionUtil.extractModelException(assistantMessage);
            log.error("[ProbeAgent] getReasoningValidateResult 路由复检异常, msgList={}, conversationId={} ", SerializeUtils.toJsonStr(list), conversationId, exception);
            return null;
        }
        log.info("[ProbeAgent] getValidateResult 复检结果：{}", SerializeUtils.toJsonStr(assistantMessage.getContent()));

        return SerializeUtils.toObj(assistantMessage.getContent(), ValidateModel.class);
    }

    private List<Message> getReChooseMsgList(List<Message> msgList, ValidateModel validateModel) {
        List<Message> reChooseMsgList = Lists.newArrayList(msgList);

        StringBuilder sb = new StringBuilder("当前路由不正确，请按照复检理由重新路由。");
        if (StringUtils.isNotBlank(validateModel.getReason())) {
            sb.append("复检理由为：").append(validateModel.getReason());
        }

        reChooseMsgList.add(UserMessage.from(sb.toString()));
        return reChooseMsgList;
    }

    private boolean judgeSubAgentInRange(AgentPlanModel taskAgentPlan) {
        if (taskAgentPlan == null || taskAgentPlan.getHitAgent() == null || !taskAgentPlan.getHitAgent()) {
            return false;
        }
        List<MdpChatFunction> functions = MdpFunctionRegistry.getFunctionsByType(ToolTypeConstant.PROBE);
        if (CollectionUtils.isEmpty(functions)) {
            return false;
        }
        return functions.stream().anyMatch(function -> function.getName().equals(taskAgentPlan.getAgent()));
    }

    private String extractSubAgentParam(List<Message> msgList, MdpChatFunction function, Long conversationId, String configUnit) throws Exception {
        if (CollectionUtils.isEmpty(msgList) || function == null) {
            return null;
        }
        SystemMessage systemMsg = buildExtractParamSystemMessage(function, conversationId, configUnit);
        List<Message> list = Lists.newArrayList(systemMsg);
        list.addAll(msgList);
        AssistantMessage assistantMessage = openAIChatModel.sendMessages(list);
        if (!LLMExceptionUtil.validateMsg(assistantMessage)) {
            Exception exception = LLMExceptionUtil.extractModelException(assistantMessage);
            log.error("[ProbeAgent] extractSubAgentParam 提取子Agent入参失败, msgList={}, conversationId={}, subAgent={}", SerializeUtils.toJsonStr(list), conversationId, function.getName(), exception);
            throw new NaturalLanguageGenerateModelException(exception);
        }
        log.info("[ProbeAgent] extractSubAgentParam 提取子Agent入参成功, msgList={}, conversationId={}, subAgent={}", SerializeUtils.toJsonStr(list), conversationId, function.getName());
        return assistantMessage.getContent();
    }

    private <T> T executeSubAgent(MdpChatFunction function, String param) throws Exception {
        if (function == null || StringUtils.isBlank(param)) {
            return null;
        }
        Method method = function.getFunctionClass().getDeclaredMethod(function.getName(), function.getParamClass());
        Object paramObj = SerializeUtils.toObj(param, function.getParamClass());
        return (T) method.invoke(function.getBean(), paramObj);
    }

    private String chooseSubAgentList() {
        List<MdpChatFunction> functions = MdpFunctionRegistry.getFunctionsByType(ToolTypeConstant.PROBE);
        if (CollectionUtils.isEmpty(functions)) {
            return "";
        }
        String subAgentsDesc = functions.stream()
                .map(function -> String.format("名称:%s, 描述:%s", function.getName(), function.getDescription()))
                .collect(Collectors.joining("\n"));
        return String.format("%s\n\n%s", "【可选的子Agent详细描述】", subAgentsDesc);
    }

    private SystemMessage buildReasoningSystemMessage(Long conversationId, String configUnit) {
        String msg = String.format("%s\n\n%s" ,agentPrompt.PROBE_ROUTE_REASONING_PROMPT, chooseSubAgentList());
        return SystemMessage.from(msg);
    }

    private SystemMessage buildReasoningRevisorSystemMessage() {
        String msg = String.format("%s\n\n%s", agentPrompt.PROBE_ROUTE_REASONING_REVISE_PROMPT, chooseSubAgentList());
        return SystemMessage.from(msg);
    }

    private SystemMessage buildExtractParamSystemMessage(MdpChatFunction function, Long conversationId, String configUnit) throws Exception {
        String functionJson = SerializeUtils.toJsonStr(function);
        JsonNode parametersNode = SerializeUtils.readTree(functionJson).get("parameters");
        String parameter = parametersNode instanceof TextNode ? parametersNode.asText() : parametersNode.toPrettyString();
        String subAgentParamDesc = String.format("【参数结构】\n\n%s", parameter);
        String baseMsg = String.format("%s\n\n%s", agentPrompt.PROBE_ROUTE_PARAM_EXTRACT_PROMPT, subAgentParamDesc);
        String msg = conversationId == null ? baseMsg :
                String.format("%s 补充信息：当前会话Id=%s，当前配置单元:%s", baseMsg, conversationId, configUnit);
        return SystemMessage.from(msg);
    }
}

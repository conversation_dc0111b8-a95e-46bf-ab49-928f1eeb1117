package com.sankuai.qpro.ai.professor.application.service.operator.probe.agent;

import com.dianping.rhino.Rhino;
import com.dianping.rhino.threadpool.DefaultThreadPoolProperties;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.meituan.mdp.langmodel.api.annotation.MdpLangModelFunction;
import com.meituan.mdp.langmodel.api.message.AssistantMessage;
import com.meituan.mdp.langmodel.api.message.Message;
import com.meituan.mdp.langmodel.api.message.SystemMessage;
import com.meituan.mdp.langmodel.api.message.UserMessage;
import com.meituan.mdp.langmodel.component.function.MdpFunctionRegistry;
import com.meituan.mdp.langmodel.component.model.chat.OpenAIChatModel;
import com.sankuai.dzviewscene.unifiedshelf.operator.api.dto.OperatorShelfCategoryDTO;
import com.sankuai.dzviewscene.unifiedshelf.operator.api.dto.OperatorShelfConfigDTO;
import com.sankuai.dzviewscene.unifiedshelf.operator.api.enums.OperatorShelfConfigStrategyUnit;
import com.sankuai.qpro.ai.professor.api.enums.ConfigUnitEnum;
import com.sankuai.qpro.ai.professor.api.enums.SegmentEnum;
import com.sankuai.qpro.ai.professor.application.constants.LinkConstant;
import com.sankuai.qpro.ai.professor.application.constants.ToolTypeConstant;
import com.sankuai.qpro.ai.professor.application.lion.ProbeLinkInLion;
import com.sankuai.qpro.ai.professor.application.model.operator.exception.execute.InfrastructureExecuteException;
import com.sankuai.qpro.ai.professor.application.model.operator.exception.execute.RpcExecuteException;
import com.sankuai.qpro.ai.professor.application.model.operator.exception.model.extract.ToolParamExractModelException;
import com.sankuai.qpro.ai.professor.application.model.operator.exception.model.generate.NaturalLanguageGenerateModelException;
import com.sankuai.qpro.ai.professor.application.model.operator.exception.model.timeout.TimeoutModelException;
import com.sankuai.qpro.ai.professor.application.model.operator.probe.agent.category.UnifiedCategoryModel;
import com.sankuai.qpro.ai.professor.application.model.operator.probe.agent.category.param.ProbeCategoryParam;
import com.sankuai.qpro.ai.professor.application.model.operator.probe.chat.AgentOutputModel;
import com.sankuai.qpro.ai.professor.application.prompt.ProbeAgentPrompt;
import com.sankuai.qpro.ai.professor.application.prompt.ProbeOutputPrompt;
import com.sankuai.qpro.ai.professor.application.service.operator.probe.chat.ConversationParamService;
import com.sankuai.qpro.ai.professor.application.service.operator.probe.chat.ConversationStoreService;
import com.sankuai.qpro.ai.professor.application.service.operator.probe.chat.ResultMergeUtils;
import com.sankuai.qpro.ai.professor.application.service.operator.probe.proxy.CategoryServiceProxy;
import com.sankuai.qpro.ai.professor.application.service.operator.probe.proxy.ShelfOperatorMServiceProxy;
import com.sankuai.qpro.ai.professor.application.utils.LLMExceptionUtil;
import com.sankuai.qpro.ai.professor.application.utils.RetryExecuteUtils;
import com.sankuai.qpro.ai.professor.application.utils.SerializeUtils;
import com.sankuai.qpro.ai.professor.entity.ConfigUnitEntity;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.net.SocketTimeoutException;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/12/30
 */
@Slf4j
@Component
public class ProbeCategoryAgent {

    private static final ExecutorService PROBE_CATEGORY_THREAD_POOL = Rhino.newThreadPool("probeCategoryAgent", new DefaultThreadPoolProperties.Setter()
            .withCoreSize(10).withMaxQueueSize(100).withMaxSize(20)).getExecutor();

    @Qualifier("gpt4oMiniModel")
    @Resource
    private OpenAIChatModel openAIChatModel;

    @Autowired
    private ProbeAgentPrompt agentPrompt;

    @Autowired
    private ProbeOutputPrompt outputPrompt;

    @Autowired
    private ProbeLinkInLion probeLinkInLion;

    @Autowired
    private ConversationStoreService conversationStoreService;

    @Autowired
    private CategoryServiceProxy categoryServiceProxy;

    @Autowired
    private ShelfOperatorMServiceProxy shelfOperatorMServiceProxy;

    @Autowired
    private ConversationParamService conversationParamService;

    @Autowired
    private ProbeOutputPrompt probeOutputPrompt;

    @MdpLangModelFunction(description = "处理分类问题，如果用户最新消息是「选择或者调整场景/具体做什么任务/选择或提供具体分类信息/重新调整分类」都可以由我来处理（注：如果用户提供具体分类信息但不了解当前配置，需要选择该工具）", type = ToolTypeConstant.PROBE, returnDirect = true)
    public AgentOutputModel generateCategoryTips(ProbeCategoryParam param) throws Throwable {
        log.info("[ProbeCategoryAgent] generateCategoryTips param={}", SerializeUtils.toJsonStr(param));

        List<Message> msgList = Lists.newArrayList(SystemMessage.from(String.format("%s\n会话当前会话Id=%s\n当前配置单元:%s", agentPrompt.PROBE_PARAM_PROMPT, param.getConversationId(), param.getConfigUnit())));
        msgList.addAll(filterMsgList(param.getConversationId()));
        AssistantMessage assistantMessage = RetryExecuteUtils.retryModelTimeoutExecute(() -> openAIChatModel.sendMessagesUseFuncs(msgList, MdpFunctionRegistry.getFunctionsByType(ToolTypeConstant.PROBE_PARAM)), 3);
        if (!LLMExceptionUtil.validateMsg(assistantMessage)) {
            Exception modelException = LLMExceptionUtil.extractModelException(assistantMessage);
            log.error("[ProbeCategoryAgent] generateCategoryTips error, msgList={}, assistantMessage={}", SerializeUtils.toJsonStr(msgList), SerializeUtils.toJsonStr(assistantMessage), modelException);
            throw LLMExceptionUtil.parseModelFunctionException(modelException);
        }


        return AgentOutputModel.from(assistantMessage.getContent(), SegmentEnum.CATEGORY);
    }

    @MdpLangModelFunction(description = "查询分类，如果用户输入我要调整XXX展示，可以由我来处理，返回查询分类组件", type = ToolTypeConstant.PROBE_PARAM, returnDirect = true)
    public String getParams(ProbeCategoryParam param) {
        log.info("[ProbeCategoryAgent] getParams param={}", SerializeUtils.toJsonStr(param));

        if (param.getConversationId() == null || StringUtils.isBlank(param.getConfigUnit())) {
            throw new ToolParamExractModelException(SegmentEnum.CATEGORY.getCode() + "阶段，getParams 参数提取失败");
        }

        return getCategoryParam(param.getConfigUnit());
    }

    public String getCategoryParam(String configUnit) {
        if (ConfigUnitEnum.SHELF_SUBTITLE.getCode().equals(configUnit)) {
            return getShelfParam();
        }
        if (ConfigUnitEnum.TITLE_PREFIX.getCode().equals(configUnit)) {
            return getShelfParam();
        }
        return "";
    }

    /**
     * 给前端返回组件，交互协议参考 https://km.sankuai.com/collabpage/2674643496
     */
    private String getShelfParam() {
        String poiLink = "[查看POI分类树](https://pdc.sankuai.com/category/categorytree/back)";
        String productLink = "[查看团购分类树](https://qing.sankuai.com/url:JTIwJTJGJTJGcWluZy5zYW5rdWFpLmNvbSUyRnNraWZmLXNjcC1tYW5hZ2Utc3lzdGVtJTJGY2F0ZWdvcnktcGFnZSUyMyUyRm1haW4lMkZjYXRlZ29yeS1saXN0/pageId:categoryPage#/main/category-list)";
        return String.format("%s\n\n%s\n\n%s",outputPrompt.SHELF_PARAM_COMPONENT, poiLink, productLink);
    }

    @MdpLangModelFunction(description = "查询在线配置，如果用户输入POI分类是xxx，团购分类是xxx，由我来处理，根据具体分类查询在线配置信息", type = ToolTypeConstant.PROBE_PARAM, returnDirect = true)
    public String getOnlineConfig(ProbeCategoryParam param) {
        log.info("[ProbeCategoryAgent] getOnlineConfig param={}", SerializeUtils.toJsonStr(param));

        // 参数校验
        if (param.getConversationId() == null) {
            throw new ToolParamExractModelException(SegmentEnum.CATEGORY.getCode() + "阶段，getOnlineConfig conversionId 提取为空");
        }
        if (CollectionUtils.isEmpty(param.getPoiCategoryIdList()) || CollectionUtils.isEmpty(param.getPoiCategoryNameList())
                || param.getProductCategoryId() == null || StringUtils.isBlank(param.getProductCategoryName())) {
            return "分类信息不能为空，请选择分类" + generateCategoryErrorTips();
        }

        // 存储分类参数
        conversationParamService.updateCategory(param.getConversationId(), param.getPoiCategoryIdList(), param.getPoiCategoryNameList(), param.getProductCategoryId(), param.getProductCategoryName());

        String onlineConfig = "";
        if (ConfigUnitEnum.SHELF_SUBTITLE.getCode().equals(param.getConfigUnit())) {
            onlineConfig = shelfSubTitleConfig(param);
        }

        if (ConfigUnitEnum.TITLE_PREFIX.getCode().equals(param.getConfigUnit())) {
            onlineConfig = titlePrefixConfig(param);
        }

        return onlineConfig;
    }

    /**
     * 货架当前配置查询接口 https://km.sankuai.com/collabpage/2674351209
     */
    public String shelfSubTitleConfig(ProbeCategoryParam param) {
        Map<OperatorShelfCategoryDTO, List<OperatorShelfConfigDTO>> configMap = shelfOperatorMServiceProxy.getUsedShelfOperatorConfig(param.getPoiCategoryIdList(), param.getProductCategoryId(), OperatorShelfConfigStrategyUnit.subTitle);
        if (configMap == null || configMap.isEmpty()) {
            log.info("[ProbeCategoryAgent] 查询货架副标题线上配置为空 param={}", SerializeUtils.toJsonStr(param));
            return String.format("%s\n\n%s\n\n%s", "线上没有进行中的策略，可以继续创建", LinkConstant.SUBTITLE_LINK, generateExpTips());
        }

        log.info("[ProbeCategoryAgent] 查询货架副标题线上配置成功，configMap={}", SerializeUtils.toJsonStr(configMap));

        CompletableFuture<ConfigUnitEntity> configUnitEntityFuture = asyncQueryConfigUnit(param.getConversationId());
        Pair<Map<Long, UnifiedCategoryModel>, Map<Long, UnifiedCategoryModel>> categoryPair = asyncExtractCategoryInfos(configMap.keySet()).join();
        Map<Long, UnifiedCategoryModel> shopCategoryMap = getCategoryMap(categoryPair.getLeft());
        Map<Long, UnifiedCategoryModel> productCategoryMap = getCategoryMap(categoryPair.getRight());
        List<Long> nonQueryShopCategory = Lists.newArrayList();
        List<Long> nonQueryProductCategory = Lists.newArrayList();
        List<String> existExpCategoryMsg = Lists.newArrayList();

        StringBuilder sb = new StringBuilder();
        ConfigUnitEntity configUnitInfo = configUnitEntityFuture.join();
        for (Map.Entry<OperatorShelfCategoryDTO, List<OperatorShelfConfigDTO>> entry : configMap.entrySet()) {
            OperatorShelfCategoryDTO categoryDTO = entry.getKey();
            List<OperatorShelfConfigDTO> configDTOList = entry.getValue();
            if (CollectionUtils.isEmpty(configDTOList)) {
                continue;
            }

            // 获取分类
            UnifiedCategoryModel shopCategory = getShopCategoryAndAddNonQuery(categoryDTO, shopCategoryMap, nonQueryShopCategory);
            UnifiedCategoryModel productCategory = getProductCategoryAndAddNonQuery(categoryDTO, productCategoryMap, nonQueryProductCategory);
            if (shopCategory == null || productCategory == null) {
                continue;
            }

            // 分析当前分类是否存在实验，存在实验则认为异常
            if (ShelfOperatorMServiceProxy.hasExp(configDTOList)) {
                handleExistExpCategoryMsg(existExpCategoryMsg, shopCategory, productCategory, configUnitInfo, configDTOList);
                continue;
            }
            // 获取当前分类的非实验配置信息
            OperatorShelfConfigDTO nonExpConfig = ShelfOperatorMServiceProxy.extractNonExpConfig(configDTOList);
            // 当前分类没有配置
            if (nonExpConfig == null) {
                ResultMergeUtils.mergeCategoryResult(sb, shopCategory, productCategory);
                sb.append("没有进行中的策略，可以继续创建。\n");
                continue;
            }
            // 把线上代码总结成策略
            handleStrategyResult(sb, param, shopCategory, productCategory, nonExpConfig);
        }

        String errorMsg = buildErrorMsg(nonQueryShopCategory, nonQueryProductCategory, existExpCategoryMsg);
        if (StringUtils.isNotEmpty(errorMsg)) {
            return String.format("%s\n\n%s", errorMsg, generateCategoryErrorTips());
        }
        return String.format("%s\n\n%s\n\n%s", sb, LinkConstant.SUBTITLE_LINK, generateExpTips());
    }

    private Map<Long, UnifiedCategoryModel> getCategoryMap(Map<Long, UnifiedCategoryModel> categoryMap) {
        return MapUtils.isEmpty(categoryMap) ? Maps.newHashMap() : categoryMap;
    }

    private UnifiedCategoryModel getShopCategoryAndAddNonQuery(OperatorShelfCategoryDTO categoryDTO, Map<Long, UnifiedCategoryModel> shopCategoryMap, List<Long> nonQueryShopCategory) {
        Long shopCategoryId = (long) Optional.ofNullable(categoryDTO.getShopBackCategory()).orElse(0);
        UnifiedCategoryModel shopCategory = shopCategoryMap.get(shopCategoryId);
        if (shopCategory == null) {
            nonQueryShopCategory.add(shopCategoryId);
        }
        return shopCategory;
    }

    private UnifiedCategoryModel getProductCategoryAndAddNonQuery(OperatorShelfCategoryDTO categoryDTO, Map<Long, UnifiedCategoryModel> productCategoryMap, List<Long> nonQueryProductCategory) {
        Long productCategoryId = categoryDTO.getProductCategory();
        UnifiedCategoryModel productCategory = productCategoryMap.get(productCategoryId);
        if (productCategory == null) {
            nonQueryProductCategory.add(productCategoryId);
        }
        return productCategory;
    }


    private void handleExistExpCategoryMsg(List<String> existExpCategoryMsg, UnifiedCategoryModel shopCategory, UnifiedCategoryModel productCategory, ConfigUnitEntity configUnitInfo, List<OperatorShelfConfigDTO> configDTOList) {
        StringBuilder existExpCateSb = new StringBuilder();
        ResultMergeUtils.mergeCategoryResult(existExpCateSb, shopCategory, productCategory);
        ResultMergeUtils.mergeConfigUnitResult(existExpCateSb, configUnitInfo);
        ResultMergeUtils.mergeExpConflictResult(existExpCateSb, ShelfOperatorMServiceProxy.extractExpConfig(configDTOList), probeLinkInLion.PROBE_STRATEGY_LINK);
        existExpCategoryMsg.add(existExpCateSb.toString());
    }

    private void handleStrategyResult(StringBuilder result, ProbeCategoryParam param, UnifiedCategoryModel shopCategory, UnifiedCategoryModel productCategory, OperatorShelfConfigDTO nonExpConfig) {
        List<Message> msgList = Lists.newArrayList(SystemMessage.from(agentPrompt.PROBE_PARAM_ONLINE_STRATEGY_CONCLUSION_PROMPT));
        msgList.add(UserMessage.from(nonExpConfig.getGroovyContent()));
        AssistantMessage assistantMessage = RetryExecuteUtils.retryModelTimeoutExecute(() -> openAIChatModel.sendMessages(msgList), 3);
        if (!LLMExceptionUtil.validateMsg(assistantMessage)) {
            Exception modelException = LLMExceptionUtil.extractModelException(assistantMessage);
            log.error("[ProbeCategoryAgent] handleStrategyResult 获取线上逻辑异常, param={}, error={}", SerializeUtils.toJsonStr(param), modelException);
            if (modelException instanceof SocketTimeoutException) {
                throw new TimeoutModelException("获取线上逻辑超时，请重试");
            }
            throw new NaturalLanguageGenerateModelException("获取线上逻辑异常");
        }
        log.info("[ProbeCategoryAgent] handleStrategyResult 获取线上逻辑={}, param={}", SerializeUtils.toJsonStr(assistantMessage), SerializeUtils.toJsonStr(param));
        ResultMergeUtils.mergeCategoryResult(result, shopCategory, productCategory);
        result.append("线上展示规则：")
                .append(assistantMessage.getContent())
                .append("\n");
    }


    /**
     * 货架当前配置查询接口 https://km.sankuai.com/collabpage/2674351209
     */
    private String titlePrefixConfig(ProbeCategoryParam param) {
        return "当前分类没有进行中的策略，可以继续创建。";
    }

    private String generateExpTips() {
        return String.format("\n\n---\n\n%s%s\n\n%s\n\n%s", "当前只支持实验配置的方式进行修改，请问斗斛实验号是多少？",
                probeOutputPrompt.PROBE_EXP_EXAMPLE,
                LinkConstant.DOUHU_EXPERIMENT_LINK,
                LinkConstant.DOUHU_PLATFORM_LINK);
    }

    public String generateCategoryErrorTips() {
        return String.format("\n\n%s", outputPrompt.SHELF_PARAM_COMPONENT);
    }

    private List<Message> getMsgList(Long conversationId) {
        return conversationStoreService.queryHistoryMessagesWithoutSystem(conversationId);
    }

    /**
     * 只返回一条消息
     */
    private List<Message> filterMsgList(Long conversationId) {
        List<Message> msgList = getMsgList(conversationId);
        if (msgList.size() <= 1) {
            return msgList;
        }
        return msgList.subList(msgList.size() - 1, msgList.size());
    }

    /**
     * 获取poi分类和商品分类详情
     * @param categoryDTOSet
     * @return
     */
    private CompletableFuture<Pair<Map<Long, UnifiedCategoryModel>, Map<Long, UnifiedCategoryModel>>> asyncExtractCategoryInfos(Set<OperatorShelfCategoryDTO> categoryDTOSet) {
        List<Long> productCategoryIds = Lists.newArrayList();
        List<Long> shopCategoryIds = Lists.newArrayList();
        for (OperatorShelfCategoryDTO categoryDTO : categoryDTOSet) {
            if (categoryDTO == null) {
                continue;
            }
            productCategoryIds.add(categoryDTO.getProductCategory());
            shopCategoryIds.add((long) Optional.ofNullable(categoryDTO.getShopBackCategory()).orElse(0));
        }
        CompletableFuture<List<UnifiedCategoryModel>> productCategoryModelsFuture = asyncGetProductCategoryListByPlatformIds(productCategoryIds);
        CompletableFuture<List<UnifiedCategoryModel>> shopCategoryModelsFuture = asyncGetPoiCategoryListByIds(shopCategoryIds);
        return productCategoryModelsFuture.thenCombine(shopCategoryModelsFuture, (productCategoryModels, shopCategoryModels) -> {
            Map<Long, UnifiedCategoryModel> productCategoryMap = productCategoryModels.stream().collect(Collectors.toMap(UnifiedCategoryModel::getPlatformCategoryId, Function.identity(), (a, b) -> a));
            Map<Long, UnifiedCategoryModel> shopCategoryMap = shopCategoryModels.stream().collect(Collectors.toMap(UnifiedCategoryModel::getCategoryId, Function.identity(), (a, b) -> a));
            return Pair.of(shopCategoryMap, productCategoryMap);
        });
    }

    private CompletableFuture<List<UnifiedCategoryModel>> asyncGetProductCategoryListByPlatformIds(List<Long> productCategoryIds) {
        return CompletableFuture.supplyAsync(() -> categoryServiceProxy.getProductCategoryListByPlatformIds(productCategoryIds), PROBE_CATEGORY_THREAD_POOL)
                .exceptionally(throwable -> {
                    log.error("[ProbeCategoryAgent] getProductCategoryListByPlatformIds error productCateIds={}", SerializeUtils.toJsonStr(productCategoryIds), throwable);
                    throw new RpcExecuteException(throwable);
                });
    }

    private CompletableFuture<List<UnifiedCategoryModel>> asyncGetPoiCategoryListByIds(List<Long> shopCategoryIds) {
        return CompletableFuture.supplyAsync(() -> categoryServiceProxy.getPoiCategoryListByIds(shopCategoryIds), PROBE_CATEGORY_THREAD_POOL)
                .exceptionally(throwable -> {
                    log.error("[ProbeCategoryAgent] getPoiCategoryListByIds error, shopCategoryIds={}", SerializeUtils.toJsonStr(shopCategoryIds), throwable);
                    throw new RpcExecuteException(throwable);
                });
    }

    private CompletableFuture<ConfigUnitEntity> asyncQueryConfigUnit(Long conversationId) {
        return CompletableFuture.supplyAsync(() -> conversationStoreService.queryConfigUnitOfConversation(conversationId), PROBE_CATEGORY_THREAD_POOL)
                .exceptionally(throwable -> {
                    log.error("[ProbeCategoryAgent] queryConfigUnit error conversationId={}", conversationId, throwable);
                    throw new InfrastructureExecuteException(throwable);
                });
    }

    private String buildErrorMsg(List<Long> nonQueryShopCategory, List<Long> nonQueryProductCategory, List<String> existExpCategoryMsg) {
        if (CollectionUtils.isEmpty(nonQueryShopCategory) && CollectionUtils.isEmpty(nonQueryProductCategory) && CollectionUtils.isEmpty(existExpCategoryMsg)) {
            return null;
        }
        StringBuilder sb = new StringBuilder();
        if (CollectionUtils.isNotEmpty(nonQueryProductCategory) || CollectionUtils.isNotEmpty(nonQueryShopCategory)) {
            sb.append("查询失败，错误原因：分类不存在。\n");
        }
        if (CollectionUtils.isNotEmpty(nonQueryShopCategory)) {
            sb.append("POI分类不存在, ID=");
            sb.append(String.join(",", nonQueryShopCategory.stream().map(String::valueOf).collect(Collectors.toList())));
        }
        if (CollectionUtils.isNotEmpty(nonQueryProductCategory)) {
            sb.append("团购分类不存在, ID=");
            sb.append(String.join(",", nonQueryProductCategory.stream().map(String::valueOf).collect(Collectors.toList())));
        }
        if (CollectionUtils.isNotEmpty(existExpCategoryMsg)) {
            sb.append(String.join("\n", existExpCategoryMsg));
        }
        return sb.toString();
    }
}

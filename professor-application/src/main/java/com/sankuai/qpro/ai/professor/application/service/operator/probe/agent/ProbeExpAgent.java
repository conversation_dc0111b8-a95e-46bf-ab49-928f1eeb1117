package com.sankuai.qpro.ai.professor.application.service.operator.probe.agent;

import com.meituan.mdp.langmodel.api.annotation.MdpLangModelFunction;
import com.sankuai.qpro.ai.professor.api.enums.ConfigUnitEnum;
import com.sankuai.qpro.ai.professor.api.enums.ConversationStageEnum;
import com.sankuai.qpro.ai.professor.api.enums.SegmentEnum;
import com.sankuai.qpro.ai.professor.application.constants.LinkConstant;
import com.sankuai.qpro.ai.professor.application.constants.ToolTypeConstant;
import com.sankuai.qpro.ai.professor.application.model.operator.exception.model.extract.ToolParamExractModelException;
import com.sankuai.qpro.ai.professor.application.model.operator.probe.agent.exp.ExpCheckModel;
import com.sankuai.qpro.ai.professor.application.model.operator.probe.agent.exp.ExpStrategyModel;
import com.sankuai.qpro.ai.professor.application.model.operator.probe.agent.exp.param.ProbeExpParam;
import com.sankuai.qpro.ai.professor.application.model.operator.probe.chat.AgentOutputModel;
import com.sankuai.qpro.ai.professor.application.prompt.ProbeOutputPrompt;
import com.sankuai.qpro.ai.professor.application.service.operator.probe.chat.ConversationParamService;
import com.sankuai.qpro.ai.professor.application.service.operator.probe.proxy.ExpManageProxy;
import com.sankuai.qpro.ai.professor.application.utils.SerializeUtils;
import com.sankuai.qpro.ai.professor.entity.ConversationParamEntity;
import com.sankuai.spt.ark.common.enums.exp.ArkExpStrategyTypeEnum;
import deps.redis.clients.util.CollectionUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/12/30
 */
@Slf4j
@Component
public class ProbeExpAgent {

    @Autowired
    private ExpManageProxy expManageProxy;

    @Autowired
    private ConversationParamService conversationParamService;

    @Autowired
    private ProbeOutputPrompt probeOutputPrompt;

    @MdpLangModelFunction(description = "处理斗斛实验问题，最新消息是关于斗斛实验的由我来处理", type = ToolTypeConstant.PROBE, returnDirect = true)
    public AgentOutputModel generateExperimentTips(ProbeExpParam param) {
        log.info("[ProbeExpAgent] generateExperimentTips param={}", SerializeUtils.toJsonStr(param));

        // 参数校验
        if (param.getConversationId() == null) {
            throw new ToolParamExractModelException(SegmentEnum.EXP.getCode() + "阶段，generateExperimentTips conversionId 提取为空");
        }
        if (CollectionUtils.isEmpty(param.getExpIdList())) {
            throw new ToolParamExractModelException(SegmentEnum.EXP.getCode() + "阶段，generateExperimentTips expIdList 提取为空");
        }
        ConversationParamEntity paramEntity = conversationParamService.getConversationParamEntity(param.getConversationId(), SegmentEnum.EXP);
        log.info("[ProbeExpAgent] generateExperimentTips，conversationId={}, paramEntity={}", param.getConversationId(), SerializeUtils.toJsonStr(paramEntity));

        AgentOutputModel validateAgentOutputModel = AgentOutputModel.from(paramEntity.getStage(), SegmentEnum.EXP, probeOutputPrompt);
        if (validateAgentOutputModel != null) {
            return validateAgentOutputModel;
        }

        ConversationParamEntity conversationParamEntity = conversationParamService.updateExpIds(param.getConversationId(), param.getExpIdList(), ConversationStageEnum.EXP_ENTERED);
        log.info("[ProbeExpAgent] generateExperimentTips 斗斛实验问题处理开始，conversationParamEntity={}", SerializeUtils.toJsonStr(conversationParamEntity));

        ExpCheckModel model = null;
        try {
            param.setProductCategoryId(conversationParamEntity.getProductCategoryId());
            param.setProductCategoryName(conversationParamEntity.getProductCategoryName());
            model = expManageProxy.checkExp(param);
        } catch (Exception e) {
            log.error("[ProbeConfAgent] generateExperimentTips 斗斛实验远程 RPC 校验服务调用异常", e);
            return AgentOutputModel.from("实验号校验服务异常，请联系系统管理员", SegmentEnum.EXP);
        }
        log.info("[ProbeExpAgent] generateExperimentTips 斗斛实验校验结果model={}", SerializeUtils.toJsonStr(model));

        // 1 实验号校验失败
        String msg;
        if (!model.isCheckResult() && !model.isCanSkip()) {
            // 校验失败
            conversationParamService.updateExpIds(param.getConversationId(), param.getExpIdList(), ConversationStageEnum.EXP_CHECKED_FAILED);

            msg = model.getCheckErrorMsg() + "\n\n请重新输入有效的实验号，你可以这样回答：\n\n>eg.我想变更斗斛实验号为 EXP2025010700001";
            return AgentOutputModel.from(msg, SegmentEnum.EXP);
        }

        // 2 生成实验号提示信息
        // 校验通过
        conversationParamService.updateExpIds(param.getConversationId(), param.getExpIdList(), ConversationStageEnum.EXP_CHECKED_SUCCEED);

        String targetGroupName = generateTargetGroupName(model);
        int targetGroupNum = computeNum(model);
        String firstGroupName = generateFirstGroupName(model);
        String referenceGroupName = mergeShortNameAndId(model.getExpInfoModelList().stream().flatMap(expInfoModel -> expInfoModel.getExpStrategyModels().stream()).filter(strategyModel -> strategyModel.getStrategyType().equals(String.valueOf(ArkExpStrategyTypeEnum.CONTROL.getCode()))).collect(Collectors.toList()));
        String blankReferenceGroupName = mergeShortNameAndId(model.getExpInfoModelList().stream().flatMap(expInfoModel -> expInfoModel.getExpStrategyModels().stream()).filter(strategyModel -> strategyModel.getStrategyType().equals(String.valueOf(ArkExpStrategyTypeEnum.BLANK_CONTROL.getCode()))).collect(Collectors.toList()));

        String twoExpIdMsg = "";
        if (CollectionUtils.isNotEmpty(param.getExpIdList()) && param.getExpIdList().size() == 2) {
            twoExpIdMsg = "本次实验在两个平台同时开展并复用同一套实验方案，咱们只需要沟通清楚一套方案，小舟会保证它在双平台生效~  \n\n";
        }

        String expIdListStr = param.getExpIdList().stream().collect(Collectors.joining("、"));

        String expMsg = String.format("%s实验号**%s**，生效时间为**%s~%s**。\n\n", twoExpIdMsg, expIdListStr, model.getExpInfoModel().getStartTime(), model.getExpInfoModel().getEndTime());
        String expGroupMsg = String.format("该实验包含%s个分组，其中 %s 是空白对照组、 %s 是对照组，只能对 %s %s 个实验分组进行配置。\n\n", model.getExpInfoModel().getExpStrategyModels().size(), blankReferenceGroupName, referenceGroupName, targetGroupName, targetGroupNum);
        String expTips = String.format("%s  \n\n你可以这样回答：\n\n>eg.我想基于线上逻辑进行修改，把排烟设备点位修改成预约点位，其他点位保持不变  \n\n>eg.我想覆盖线上逻辑，重新配置服务部位范围、艾灸手法两个点位", getFirstConfTips(param.getConfigUnit(), firstGroupName));
        String linkTips = generateLinkTips(param);
        String skipOrNotMsg = !model.isCheckResult() && model.isCanSkip() ? String.format("【紧急提示】%s  \n\n如果**继续**，%s%s  \n\n如果**不继续**，你可以这样回答：\n\n>eg. 我想变更斗斛实验号为 EXP2025010700002\n\n\n\n >eg. 我已经修改斗斛实验号 EXP2025010700001，请重新帮我校验  \n\n  %s", model.getCheckErrorMsg(), expGroupMsg, expTips, linkTips)
                : String.format("%s%s%s", expGroupMsg, expTips, linkTips);

        msg = String.format("%s%s", expMsg, skipOrNotMsg);
        return AgentOutputModel.from(msg, SegmentEnum.EXP);
    }

    /**
     * 示例：c (EXP2025010700001_c、EXP2025010700002_c)、d (EXP2025010700001_d、EXP2025010700002_d)
     */
    private String generateTargetGroupName(ExpCheckModel model) {
        return mergeShortNameAndId(model.getExpInfoModelList().stream().flatMap(expInfoModel -> expInfoModel.getExpStrategyModels().stream())
                .filter(strategyModel -> strategyModel.getStrategyType().equals(String.valueOf(ArkExpStrategyTypeEnum.EXPERIMENTAL.getCode())))
                .collect(Collectors.toList()));
    }

    /**
     * 计算实验组数量
     */
    private int computeNum(ExpCheckModel model) {
        return (int) model.getExpInfoModelList().stream()
                .findFirst().get().getExpStrategyModels().stream()
                .filter(expStrategyModel ->
                        String.valueOf(ArkExpStrategyTypeEnum.EXPERIMENTAL.getCode())
                        .equals(expStrategyModel.getStrategyType()))
                .count();
    }

    /**
     * 示例：c (EXP2025010700001_c、EXP2025010700002_c)
     */
    private String generateFirstGroupName(ExpCheckModel model) {
        String shortName = model.getExpInfoModelList().get(0).getExpStrategyModels().stream().filter(expStrategyModel->
                String.valueOf(ArkExpStrategyTypeEnum.EXPERIMENTAL.getCode()).equals(expStrategyModel.getStrategyType())).findFirst().get().getExpStrategyShortName();
        List<ExpStrategyModel> expStrategyModels = model.getExpInfoModelList().stream()
                .flatMap(expInfoModel -> expInfoModel.getExpStrategyModels().stream())
                .filter(expStrategyModel -> shortName.equals(expStrategyModel.getExpStrategyShortName())
                        && String.valueOf(ArkExpStrategyTypeEnum.EXPERIMENTAL.getCode()).equals(expStrategyModel.getStrategyType())).collect(Collectors.toList());
        return mergeShortNameAndId(expStrategyModels);
    }

    /**
     * 示例：c (EXP2025010700001_c、EXP2025010700002_c)、d (EXP2025010700001_d、EXP2025010700002_d)
     */
    private String mergeShortNameAndId(List<ExpStrategyModel> strategyModels) {
        if (CollectionUtils.isEmpty(strategyModels)) {
            return null;
        }

        return strategyModels.stream()
                .collect(Collectors.groupingBy(ExpStrategyModel::getExpStrategyShortName))
                .entrySet().stream()
                .map(entry -> {
                    String shortName = entry.getKey();
                    String ids = entry.getValue().stream()
                            .map(ExpStrategyModel::getExpStrategyId)
                            .collect(Collectors.joining("、"));
                    return String.format("**%s** (%s)", shortName, ids);
                })
                .collect(Collectors.joining("、"));
    }

    private String getFirstConfTips(String configUnit, String expGroupName) {
        if (ConfigUnitEnum.SHELF_SUBTITLE.getCode().equals(configUnit)) {
            return String.format("请根据我的指引一步步来，**首先，请问你想如何配置实验分组 %s 的副标题点位**?  ", expGroupName);
        }

        if (ConfigUnitEnum.TITLE_PREFIX.getCode().equals(configUnit)) {
            return String.format("请根据我的指引一步步来，**首先，请问你想如何配置实验分组 %s 的标题前缀**?", expGroupName);
        }
        return "";
    }

    private String generateLinkTips(ProbeExpParam param) {
        String cpvLink = String.format(LinkConstant.CPV_LINK, param.getProductCategoryName(), param.getProductCategoryName(), param.getConversationId());
        return new StringBuilder().append("\n\n")
                .append(cpvLink).append("\n\n")
                .append(LinkConstant.TANZHEN_LINK).append("\n\n")
                .toString();
    }
}

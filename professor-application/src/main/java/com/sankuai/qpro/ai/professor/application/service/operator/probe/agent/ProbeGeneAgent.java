package com.sankuai.qpro.ai.professor.application.service.operator.probe.agent;

import com.dianping.cat.Cat;
import com.dianping.rhino.Rhino;
import com.dianping.rhino.threadpool.DefaultThreadPoolProperties;
import com.google.common.collect.Lists;
import com.meituan.mdp.langmodel.api.annotation.MdpLangModelFunction;
import com.meituan.mdp.langmodel.api.message.AssistantMessage;
import com.meituan.mdp.langmodel.api.message.Message;
import com.meituan.mdp.langmodel.api.message.SystemMessage;
import com.meituan.mdp.langmodel.api.message.UserMessage;
import com.meituan.mdp.langmodel.component.model.chat.FridayChatModelUseTool;
import com.meituan.mdp.langmodel.component.model.chat.OpenAIChatModel;
import com.sankuai.dzviewscene.unifiedshelf.operator.api.dto.OperatorShelfCategoryDTO;
import com.sankuai.dzviewscene.unifiedshelf.operator.api.dto.OperatorShelfConfigDTO;
import com.sankuai.dzviewscene.unifiedshelf.operator.api.dto.OperatorShelfConfigValidateResult;
import com.sankuai.dzviewscene.unifiedshelf.operator.api.dto.OperatorShelfExpSkDTO;
import com.sankuai.dzviewscene.unifiedshelf.operator.api.enums.OperatorShelfConfigExpSkType;
import com.sankuai.dzviewscene.unifiedshelf.operator.api.enums.OperatorShelfConfigSource;
import com.sankuai.dzviewscene.unifiedshelf.operator.api.enums.OperatorShelfConfigStrategyUnit;
import com.sankuai.dzviewscene.unifiedshelf.operator.api.utils.OperatorShelfConfigValidateUtil;
import com.sankuai.qpro.ai.professor.api.enums.*;
import com.sankuai.qpro.ai.professor.application.constants.RedisConstant;
import com.sankuai.qpro.ai.professor.application.constants.ToolTypeConstant;
import com.sankuai.qpro.ai.professor.application.converter.ProbeGeneConverter;
import com.sankuai.qpro.ai.professor.application.lion.ProbeLinkInLion;
import com.sankuai.qpro.ai.professor.application.model.operator.exception.execute.ExecuteException;
import com.sankuai.qpro.ai.professor.application.model.operator.exception.execute.InfrastructureExecuteException;
import com.sankuai.qpro.ai.professor.application.model.operator.exception.model.ModelException;
import com.sankuai.qpro.ai.professor.application.model.operator.exception.model.extract.JsonExtractModelException;
import com.sankuai.qpro.ai.professor.application.model.operator.exception.model.extract.ToolParamExractModelException;
import com.sankuai.qpro.ai.professor.application.model.operator.exception.model.generate.CodeGenerateModelException;
import com.sankuai.qpro.ai.professor.application.model.operator.exception.model.generate.NaturalLanguageGenerateModelException;
import com.sankuai.qpro.ai.professor.application.model.operator.exception.model.timeout.TimeoutModelException;
import com.sankuai.qpro.ai.professor.application.model.operator.probe.agent.gene.GeneStatisticModel;
import com.sankuai.qpro.ai.professor.application.model.operator.probe.agent.plan.ValidateModel;
import com.sankuai.qpro.ai.professor.application.model.operator.probe.agent.exp.ExpInfoModel;
import com.sankuai.qpro.ai.professor.application.model.operator.probe.agent.exp.ExpModel;
import com.sankuai.qpro.ai.professor.application.model.operator.probe.agent.exp.ExpStrategyModel;
import com.sankuai.qpro.ai.professor.application.model.operator.probe.agent.gene.GenerateOutModel;
import com.sankuai.qpro.ai.professor.application.model.operator.probe.agent.gene.SimpleStructInfoModel;
import com.sankuai.qpro.ai.professor.application.model.operator.probe.agent.gene.StructureInfoModel;
import com.sankuai.qpro.ai.professor.application.model.operator.probe.agent.gene.param.ProbeGeneParam;
import com.sankuai.qpro.ai.professor.application.model.operator.probe.chat.AgentOutputModel;
import com.sankuai.qpro.ai.professor.application.prompt.ProbeAgentPrompt;
import com.sankuai.qpro.ai.professor.application.prompt.ProbeOutputPrompt;
import com.sankuai.qpro.ai.professor.application.service.operator.probe.chat.AgentChatManager;
import com.sankuai.qpro.ai.professor.application.service.operator.probe.chat.ConversationParamService;
import com.sankuai.qpro.ai.professor.application.service.operator.probe.chat.ConversationStoreService;
import com.sankuai.qpro.ai.professor.application.service.operator.probe.chat.ResultMergeUtils;
import com.sankuai.qpro.ai.professor.application.service.operator.probe.proxy.ExpManageProxy;
import com.sankuai.qpro.ai.professor.application.service.operator.probe.proxy.ShelfOperatorMServiceProxy;
import com.sankuai.qpro.ai.professor.application.service.operator.probe.proxy.SquirrelProxy;
import com.sankuai.qpro.ai.professor.application.utils.LLMExceptionUtil;
import com.sankuai.qpro.ai.professor.application.utils.RetryExecuteUtils;
import com.sankuai.qpro.ai.professor.application.utils.SerializeUtils;
import com.sankuai.qpro.ai.professor.application.utils.TimeUtils;
import com.sankuai.qpro.ai.professor.entity.ConfigUnitEntity;
import com.sankuai.qpro.ai.professor.entity.ConversationParamEntity;
import com.sankuai.spt.ark.common.enums.exp.ArkExpStrategyTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.net.SocketTimeoutException;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/12/30
 */
@Slf4j
@Component
public class ProbeGeneAgent {

    private static final String PROBE_GENE_AGENT_CAT = "ProbeGeneAgent";

    private static final ExecutorService PROBE_GENE_THREAD_POOL = Rhino.newThreadPool("probeGeneAgent", new DefaultThreadPoolProperties.Setter()
            .withCoreSize(10).withMaxQueueSize(100).withMaxSize(20)).getExecutor();

    @Autowired
    private ProbeAgentPrompt agentPrompt;

    @Autowired
    private ProbeOutputPrompt outputPrompt;

    @Autowired
    private ProbeLinkInLion probeLinkInLion;

    @Autowired
    private ConversationStoreService conversationStoreService;

    @Qualifier("gpt4oMiniModel")
    @Autowired
    private OpenAIChatModel gpt4oMiniModel;

    @Qualifier("gpt4oCodeModel")
    @Autowired
    private OpenAIChatModel gpt4oCodeModel;

    @Qualifier("gpt4oSummaryModel")
    @Autowired
    private OpenAIChatModel gpt4oSummaryModel;

    @Qualifier("gpt4oStructModel")
    @Autowired
    private OpenAIChatModel gpt4oStructModel;

    @Autowired
    @Qualifier("deepSeekV3Model")
    private FridayChatModelUseTool deepSeekV3Model;

    @Autowired
    private ExpManageProxy expManageProxy;

    @Autowired
    private SquirrelProxy squirrelProxy;

    @Resource
    private ShelfOperatorMServiceProxy shelfOperatorMServiceProxy;

    @Autowired
    private ConversationParamService conversationParamService;

    public final static ThreadLocal<GeneStatisticModel> CODE_STATISTIC_MODEL = new ThreadLocal<>();

    @MdpLangModelFunction(description = "最终代码生成，实验配置完毕后可以由我来生成策略小结或者代码，调整策略小结或重新生成代码也可以由我处理", type = ToolTypeConstant.PROBE, returnDirect = true)
    public AgentOutputModel generateSummaryOrCode(ProbeGeneParam param) {
        log.info("[ProbeGeneAgent] generateCode param = {}", SerializeUtils.toJsonStr(param));

        try {
            // 参数校验
            if (param == null || param.getConversationId() == null) {
                throw new ToolParamExractModelException(SegmentEnum.GENERATE.getCode() + "阶段，generateCode conversionId 提取为空");
            }
            ConversationParamEntity paramEntity = conversationParamService.getConversationParamEntity(param.getConversationId(), SegmentEnum.GENERATE);
            AgentOutputModel validateAgentOutputModel = AgentOutputModel.from(paramEntity.getStage(), SegmentEnum.GENERATE, outputPrompt);
            if (validateAgentOutputModel != null) {
                return validateAgentOutputModel;
            }

            param = conversationParamService.getProbeGeneParam(param);
            log.info("[ProbeGeneAgent] generateCode 代码生成处理开始，param = {}", SerializeUtils.toJsonStr(param));

            // 1. 查询生成小结，小结不存在或者匹配到重新生成小结，则生成小结返回
            String summary = param.getStrategy();
            Message latestMsg = queryLatestMessage(param.getConversationId());
            if (StringUtils.isBlank(summary) || validateSummaryAttention(latestMsg)) {

                // 1.1 查询历史消息
                List<Message> historyMsg = queryHistoryMsg(param.getConversationId());

                // 1.2 将总结写入数据库
                AgentChatManager.sendCompleteMsg("小舟正在快马加鞭生成小结中", SseEventTypeEnum.MESSAGE.getEventType(), MessageTypeEnum.LOADING.getType());
                summary = generateSummary(param, historyMsg);
                conversationParamService.updateStrategy(param.getConversationId(), summary);

                // 1.3 返回生成总结
                String result = String.format("%s%s", summary, outputPrompt.PROBE_GENERATE_SUMMARY_INTRODUCE);
                return AgentOutputModel.from(result, SegmentEnum.GENERATE);
            }

            // 2. 生成代码
            // 识别是否重新生成代码，需要带上用户在上一阶段生成的代码
            CODE_STATISTIC_MODEL.set(new GeneStatisticModel(4));
            if (validateCodeRegenerateAttention(latestMsg)) {
                List<Message> latestMsgWithCode = queryLatestMsgWithCode(param, Lists.newArrayList(latestMsg));
                return generateCode(param, latestMsgWithCode, summary);
            }

            return generateCode(param, Lists.newArrayList(latestMsg), summary);

        } catch (ModelException e) {
            // 异常打点
            Cat.logError(e);
            return AgentOutputModel.from(e.getMessage(), SegmentEnum.GENERATE);
        } catch (ExecuteException e) {
            // 异常打点
            Cat.logError(e);
            return AgentOutputModel.from(e.getMessage(), SegmentEnum.GENERATE);
        } catch (Exception e) {
            log.error("[ProbeGeneAgent] generateCode error, param:{}", param, e);
            throw new RuntimeException(e);
        } finally {
            CODE_STATISTIC_MODEL.remove();
        }
    }

    public List<Message> queryHistoryMsg(Long conversationId) {

        // 1.查询配置历史消息
        List<Message> historyMsg = conversationStoreService.queryHistoryMessagesForConfigSegment(conversationId);
        if (CollectionUtils.isEmpty(historyMsg)) {
            log.error("[ProbeGeneAgent] queryHistoryMsg 查询会话历史消息为空 conversationId = {}", conversationId);
            throw new InfrastructureExecuteException("查询会话历史消息为空");
        }

        // 2.查询当前分类线上策略
        Message onlineConfig = conversationStoreService.queryLatestCategoryMessage(conversationId);
        if (onlineConfig == null) {
            log.error("[ProbeGeneAgent] queryHistoryMsg 查询分类线上策略为空 conversationId = {}", conversationId);
            throw new InfrastructureExecuteException("查询当前线上生效策略异常");
        }
        String onlineConfigStr = (String) onlineConfig.getContent();

        // 3.将线上分类策略插入消息第一条
        historyMsg.add(0, AssistantMessage.from(onlineConfigStr));
        log.info("[ProbeGeneAgent] queryHistoryMsg 当前历史消息详细信息(线上配置 + 配置信息), historyMsg = {}, conversationId = {}", SerializeUtils.toJsonStr(historyMsg), conversationId);
        return historyMsg;
    }

    private boolean validateSummaryAttention(Message latestMessage) {
        String regex = ".*(调整|改造|生成).*(小结|总结).*|.*(小结|总结).*(调整|改造|生成).*";
        Pattern pattern = Pattern.compile(regex, Pattern.CASE_INSENSITIVE);
        Matcher matcher = pattern.matcher((String) latestMessage.getContent());
        return matcher.find();
    }

    private boolean validateCodeRegenerateAttention(Message latestMessage) {
        String regex = ".*(重新生成|调整|修改).*(代码).*|.*(代码).*(重新生成|调整|修改).*";
        Pattern pattern = Pattern.compile(regex, Pattern.CASE_INSENSITIVE);
        Matcher matcher = pattern.matcher((String) latestMessage.getContent());
        return matcher.find();
    }

    public String generateSummary(ProbeGeneParam param, List<Message> historyMsg) {
        List<Message> msgList = Lists.newArrayList(SystemMessage.from(agentPrompt.PROBE_GENERATE_SUMMARY_PROMPT));
        msgList.addAll(historyMsg);
        AssistantMessage assistantMessage = RetryExecuteUtils.retryModelTimeoutExecute(() -> gpt4oSummaryModel.sendMessages(msgList), 3);
        if (!LLMExceptionUtil.validateMsg(assistantMessage)) {
            Exception modelException = LLMExceptionUtil.extractModelException(assistantMessage);
            log.error("[ProbeGeneAgent] 策略小结生成失败 param = {}, error = {}", SerializeUtils.toJsonStr(param), modelException);
            if (modelException instanceof SocketTimeoutException) {
                throw new TimeoutModelException("策略小结生成超时，请重试");
            }
            throw new NaturalLanguageGenerateModelException("策略小结生成失败，请重试");
        }
        log.info("[ProbeGeneAgent] 策略小结生成详细信息={}, param = {}", assistantMessage.getContent(), SerializeUtils.toJsonStr(param));
        return assistantMessage.getContent();
    }

    private Message queryLatestMessage(Long conversationId) {
        Message message = conversationStoreService.queryLatestMessage(conversationId);
        if (message == null || message.getContent() == null) {
            log.error("[ProbeGeneAgent] queryLatestMessage 查询会话最新消息为空 conversationId = {}", conversationId);
            throw new InfrastructureExecuteException("获取会话消息为空");
        }
        return message;
    }

    private List<Message> queryLatestMsgWithCode(ProbeGeneParam param, List<Message> latestMsg) {
        List<Message> historyMsg = Lists.newArrayList();
        String[] redisKey = buildRedisKey(param);
        List<OperatorShelfConfigDTO> shelfConfigs = squirrelProxy.syncGet(RedisConstant.SHELF_OPERATOR_CATEGORY, redisKey);
        if (CollectionUtils.isEmpty(shelfConfigs) || shelfConfigs.get(0) == null || StringUtils.isBlank(shelfConfigs.get(0).getGroovyContent())) {
            log.error("[ProbeGeneAgent] queryLatestMsgWithCode 查询货架缓存为空 result={}, param={}", SerializeUtils.toJsonStr(shelfConfigs), SerializeUtils.toJsonStr(param));
            throw new InfrastructureExecuteException("生成代码阶段获取线上代码异常");
        }
        OperatorShelfConfigDTO shelfConfig = shelfConfigs.get(0);
        log.info("[ProbeGeneAgent] queryLatestMsgWithCode 查询当前货架缓存={} param={}", SerializeUtils.toJsonStr(shelfConfig), SerializeUtils.toJsonStr(param));
        historyMsg.add(UserMessage.from(String.format("当前生成的groovy脚本为 : %s", shelfConfig.getGroovyContent())));
        historyMsg.addAll(latestMsg);
        return historyMsg;
    }

    public AgentOutputModel generateCode(ProbeGeneParam param, List<Message> historyMsg, String summary) throws Exception {
        AgentChatManager.sendCompleteMsg("小舟正在快马加鞭生成代码中", SseEventTypeEnum.MESSAGE.getEventType(), MessageTypeEnum.LOADING.getType());

        // 1.提取结构化信息
        CompletableFuture<StructureInfoModel> structureInfoModelFuture = asyncExtractStructure(param, summary);
        CompletableFuture<ConfigUnitEntity> configUnitInfoFuture = asyncQueryConfigUnit(param.getConversationId());

        // 2. 拉取线上配置，目前只取第一个配置
        ConfigUnitEntity configUnitInfo = configUnitInfoFuture.join();
        String onlineConfig = getOnlineConfig(param, configUnitInfo);

        // 3. 生成DSL
        String dsl = generateInitDSL(param, historyMsg, summary, onlineConfig);
        StructureInfoModel structureInfoModel = structureInfoModelFuture.join();
        AgentChatManager.sendCompleteMsg("初始代码生成完毕，小舟正在仔细检查中", SseEventTypeEnum.MESSAGE.getEventType(), MessageTypeEnum.LOADING.getType());

        // 4. 复检
        dsl = validateAndRetryDSL(param, structureInfoModel, historyMsg, summary, onlineConfig, dsl);

        // 5. 存到 Redis
        storage2Redis(param, structureInfoModel, dsl);

        // 6. 转换输出模型
        GenerateOutModel generateOutModel = ProbeGeneConverter.buildGenerateOutModel(param, structureInfoModel, configUnitInfo, dsl);

        // 7. 包装特殊组件
        String outputMsg = generateComponent(generateOutModel);
        AgentChatManager.sendCompleteMsg("小舟已完成代码生成和策略总结", SseEventTypeEnum.MESSAGE.getEventType(), MessageTypeEnum.LOADING.getType());

        // 8. 更新代码生成状态
        conversationParamService.updateStage(param.getConversationId(), ConversationStageEnum.CODE_GENERATED, SegmentEnum.GENERATE);

        return AgentOutputModel.from(outputMsg, SegmentEnum.GENERATE);
    }

    /**
     * 通过 Model 提取出结构化信息
     */
    public StructureInfoModel extractStructure(ProbeGeneParam param, String summary) {

        // 不依赖历史聊天记录，直接从当前策略配置总结中提取
        List<Message> msgList = Lists.newArrayList(SystemMessage.from(agentPrompt.PROBE_STRUCTURE_PROMPT));
        msgList.add(UserMessage.from(String.format("请基于当前策略配置总结提取信息，当前策略配置总结为: %s", summary)));
        AssistantMessage assistantMessage = RetryExecuteUtils.retryModelTimeoutExecute(() -> gpt4oStructModel.sendMessages(msgList), 3);
        if (!LLMExceptionUtil.validateMsg(assistantMessage)) {
            Exception modelException = LLMExceptionUtil.extractModelException(assistantMessage);
            log.error("[ProbeGeneAgent] 提取结构化信息失败 param={}, error={}", SerializeUtils.toJsonStr(param), modelException);
            if (modelException instanceof SocketTimeoutException) {
                throw new TimeoutModelException("提取结构化信息超时，请重试");
            }
            throw new JsonExtractModelException("提取结构化信息失败，请重试");
        }

        log.info("[ProbeGeneAgent] extractStructure 提取结构化详细信息模型结果={}, param = {}", assistantMessage.getContent(), SerializeUtils.toJsonStr(param));

        SimpleStructInfoModel simpleStructInfoModel = SerializeUtils.toObj(assistantMessage.getContent(), SimpleStructInfoModel.class);
        StructureInfoModel structureInfoModel = ProbeGeneConverter.convert2StructureInfoModel(simpleStructInfoModel);
        if (structureInfoModel == null || CollectionUtils.isEmpty(structureInfoModel.getNeedAttrs())) {
            log.error("[ProbeGeneAgent] 模型提取结构化信息异常, param={}, summary={}, structMsg={}", SerializeUtils.toJsonStr(param), summary, assistantMessage.getContent());
            throw new JsonExtractModelException("提取结构化信息异常，请重试");
        }
        structureInfoModel.setConversionId(param.getConversationId());

        // 查询斗斛信息填充
        fillDouHuInfo(structureInfoModel);
        log.info("[ProbeGeneAgent] extractStructure 提取结构化详细信息={}, param = {}", structureInfoModel, SerializeUtils.toJsonStr(param));

        return structureInfoModel;
    }

    public String generateInitDSL(ProbeGeneParam param, List<Message> historyMsg, String summary, String onlineConfig) {
        if (CODE_STATISTIC_MODEL.get() != null) {
            CODE_STATISTIC_MODEL.get().getTimesCount()[0]++;
        }
        List<Message> msgList = Lists.newArrayList(getDSLSystemPrompt(param, onlineConfig,false));
        msgList.addAll(historyMsg);
        msgList.add(UserMessage.from(String.format("请基于聊天记录和当前策略配置总结生成groovy脚本，当前策略配置总结为: %s", summary)));
        AssistantMessage assistantMessage = RetryExecuteUtils.retryModelTimeoutExecute(() -> gpt4oCodeModel.sendMessages(msgList), 3);
        if (!LLMExceptionUtil.validateMsg(assistantMessage)) {
            Exception modelException = LLMExceptionUtil.extractModelException(assistantMessage);
            log.error("[ProbeGeneAgent] 获取初始化DSL信息失败 param={}, error={}", SerializeUtils.toJsonStr(param), modelException);
            if (modelException instanceof SocketTimeoutException) {
                throw new TimeoutModelException("获取初始化DSL信息超时，请重试");
            }
            throw new NaturalLanguageGenerateModelException("获取初始化DSL信息失败，请重试");
        }
        log.info("[ProbeGeneAgent] generateInitDSL 获取初始DSL信息={}, param = {}", assistantMessage.getContent(), SerializeUtils.toJsonStr(param));
        return assistantMessage.getContent();
    }

    public CompletableFuture<StructureInfoModel> asyncExtractStructure(ProbeGeneParam param, String summary){
        return CompletableFuture.supplyAsync(() -> extractStructure(param, summary), PROBE_GENE_THREAD_POOL)
                .exceptionally(throwable -> {
                    if (throwable.getCause() instanceof JsonExtractModelException) {
                        throw new JsonExtractModelException(throwable.getMessage());
                    }
                    throw new RuntimeException(throwable);
                });
    }

    private CompletableFuture<ConfigUnitEntity> asyncQueryConfigUnit(Long conversationId) {
        return CompletableFuture.supplyAsync(() -> conversationStoreService.queryConfigUnitOfConversation(conversationId), PROBE_GENE_THREAD_POOL)
                .exceptionally(throwable -> {
                    throw new InfrastructureExecuteException(throwable);
                });
    }

    private SystemMessage getDSLSystemPrompt(ProbeGeneParam param, String onlineConfig, boolean isRegenerate) {
        String prompt = getBaseDSLSystemPrompt(param, isRegenerate);
        if (StringUtils.isBlank(prompt)) {
            log.error("[ProbeGeneAgent] 未找到对应的Prompt。 param = {}, isRegenerate = {}", SerializeUtils.toJsonStr(param), isRegenerate);
            throw new InfrastructureExecuteException("生成阶段未找到对应的Prompt，请联系管理员处理");
        }
        return SystemMessage.from(String.format("%s \n 【线上groovy】请基于用户描述对当前线上groovy进行修改，当前线上使用groovy脚本为：%s", prompt, onlineConfig));
    }

    private String getBaseDSLSystemPrompt(ProbeGeneParam param, boolean isRegenerate) {
        // 重新生成Prompt
        if (isRegenerate) {
            if (ConfigUnitEnum.SHELF_SUBTITLE.getCode().equals(param.getConfigUnit())) {
                return agentPrompt.PROBE_REGENERATE_PROMPT;
            }
        } else {
            if (ConfigUnitEnum.SHELF_SUBTITLE.getCode().equals(param.getConfigUnit())) {
                return agentPrompt.SHELF_SUB_TITLE_GENERATE_PROMPT;
            }
        }
        return null;
    }

    public String getOnlineConfig(ProbeGeneParam param, ConfigUnitEntity configUnitInfo) {
        Map<OperatorShelfCategoryDTO, List<OperatorShelfConfigDTO>> configMap = shelfOperatorMServiceProxy.getUsedShelfOperatorConfig(param.getPoiCategoryList(), param.getProductCategoryId(), ProbeGeneConverter.convertStrategyUnit(param));
        log.info("[ProbeGeneAgent] getOnlineConfig 调用线上配置接口详细信息={}, param={}, configUnitInfo={}", SerializeUtils.toJsonStr(configMap), SerializeUtils.toJsonStr(param), SerializeUtils.toJsonStr(configUnitInfo));
        if (MapUtils.isEmpty(configMap)) {
            log.warn("[ProbeGeneAgent] getOnlineConfig 代码生成未查询到线上配置 param={}, configUnitInfo={}", SerializeUtils.toJsonStr(param), SerializeUtils.toJsonStr(configUnitInfo));
            return "";
        }
        // 当前仅支持一个类目
        if (configMap.size() > 1) {
            log.error("[ProbeGeneAgent] getOnlineConfig 当前仅支持修改一个类目 param = {}, configUnitInfo={}", SerializeUtils.toJsonStr(param), SerializeUtils.toJsonStr(configUnitInfo));
            throw new CodeGenerateModelException("当前仅支持修改一个类目，请调整类目后重试");
        }
        List<OperatorShelfConfigDTO> configDTOList = configMap.values().stream().findFirst().orElse(null);
        if (CollectionUtils.isEmpty(configDTOList)) {
            log.error("[ProbeGeneAgent] getOnlineConfig 未查询到线上配置 param = {}, configUnitInfo={}", SerializeUtils.toJsonStr(param), SerializeUtils.toJsonStr(configUnitInfo));
            throw new CodeGenerateModelException("代码生成未查询到线上配置，请联系管理员处理");
        }
        if (ShelfOperatorMServiceProxy.hasExp(configDTOList)) {
            log.error("[ProbeGeneAgent] getOnlineConfig 当前分类存在线上实验配置，详细配置={} param={}, configUnitInfo={}", SerializeUtils.toJsonStr(configMap), SerializeUtils.toJsonStr(param), SerializeUtils.toJsonStr(configUnitInfo));
            throw new CodeGenerateModelException(getOnlineConflictErrorMsg(param, configUnitInfo, configDTOList).toString());
        }
        OperatorShelfConfigDTO nonExpConfig = ShelfOperatorMServiceProxy.extractNonExpConfig(configDTOList);
        if (nonExpConfig == null || StringUtils.isBlank(nonExpConfig.getGroovyContent())) {
            log.error("[ProbeGeneAgent] getOnlineConfig 未查询到线上非实验配置 param = {}", SerializeUtils.toJsonStr(param));
            throw new CodeGenerateModelException("代码生成未查询到线上非实验配置，请联系管理员处理");
        }
        String onlineConfig = nonExpConfig.getGroovyContent();
        log.info("[ProbeGeneAgent] getOnlineConfig 线上详细配置信息={} param = {}, configUnitInfo = {}", onlineConfig, SerializeUtils.toJsonStr(param), SerializeUtils.toJsonStr(configUnitInfo));
        return onlineConfig;
    }

    private String getOnlineConflictErrorMsg(ProbeGeneParam param, ConfigUnitEntity configUnitInfo, List<OperatorShelfConfigDTO> configDTOList) {
        StringBuilder errorMsg = new StringBuilder();
        ResultMergeUtils.mergeCategoryResult(errorMsg, param.getPoiCategoryList().get(0), param.getPoiCategoryNames().get(0), param.getProductCategoryId(), param.getProductCategoryName());
        ResultMergeUtils.mergeConfigUnitResult(errorMsg, configUnitInfo);
        ResultMergeUtils.mergeExpConflictResult(errorMsg, ShelfOperatorMServiceProxy.extractExpConfig(configDTOList), probeLinkInLion.PROBE_STRATEGY_LINK);
        return errorMsg.toString();
    }

    /**
     * 校验并重试DSL
     */
    private String validateAndRetryDSL(ProbeGeneParam param, StructureInfoModel structureInfoModel, List<Message> historyMsg, String summary, String onlineConfig, String dsl) throws Exception {
        // 校验DSL生成结果，最多重试3次
        int retryCount = 0;
        while(true) {
            ValidateModel validateModel = validateDSL(param, summary, dsl);
            ValidateModel businessValidateModel = validateBusiness(param, structureInfoModel, dsl);
            if (validateModel != null && validateModel.isPassValidate()
                    && businessValidateModel != null && businessValidateModel.isPassValidate()) {
                if (CODE_STATISTIC_MODEL.get() != null) {
                    CODE_STATISTIC_MODEL.get().getExecuteSuccessCount()[retryCount]++;
                }
                break;
            }

            if (retryCount++ >= 3) {
                log.error("[ProbeGeneAgent] validateAndRetryDSL 代码生成复检失败，重试超过3次, param = {}, dsl={}, validateModel = {}, businessValidateModel = {}", SerializeUtils.toJsonStr(param),
                        dsl, SerializeUtils.toJsonStr(validateModel), SerializeUtils.toJsonStr(businessValidateModel));
                if (CODE_STATISTIC_MODEL.get() != null) {
                    CODE_STATISTIC_MODEL.get().setError(1);
                }
                throw new CodeGenerateModelException("代码生成失败，请重试");
            }
            AgentChatManager.sendCompleteMsg("代码生成存在异常，小舟正在努力重新生成", SseEventTypeEnum.MESSAGE.getEventType(), MessageTypeEnum.LOADING.getType());
            StringBuilder reasonBuilder = new StringBuilder();

            // 系统自检不通过，记录理由
            if (validateModel != null && !validateModel.isPassValidate()) {
                reasonBuilder.append(validateModel.getReason() + "\n");
            }

            // 业务检查不通过，记录理由
            if (businessValidateModel != null && !businessValidateModel.isPassValidate()) {
                reasonBuilder.append(businessValidateModel.getReason());
            }
            // 重新生成
            if (validateModel == null || businessValidateModel == null || reasonBuilder.length() > 0) {
                if (CODE_STATISTIC_MODEL.get() != null) {
                    CODE_STATISTIC_MODEL.get().getTimesCount()[retryCount]++;
                }
                dsl = retryGenerateDSL(param, historyMsg, summary, onlineConfig, dsl, reasonBuilder.toString());
            }
        }
        if (CODE_STATISTIC_MODEL.get() != null) {
            CODE_STATISTIC_MODEL.get().setDsl(dsl);
        }
        return dsl;
    }

    private ValidateModel validateBusiness(ProbeGeneParam param, StructureInfoModel structureInfoModel, String dsl) {
        OperatorShelfConfigDTO config = ProbeGeneConverter.convertShelfConfig(param, structureInfoModel, dsl);
        List<OperatorShelfExpSkDTO> shelfDouHuExpResults = Lists.newArrayList();
        if (structureInfoModel != null && CollectionUtils.isNotEmpty(structureInfoModel.getExpModels())) {
            for (ExpModel expModel : structureInfoModel.getExpModels()) {
                shelfDouHuExpResults.addAll(getShelfDouHuExpResults(expModel));
            }
        }
        try {
            OperatorShelfConfigValidateResult result = OperatorShelfConfigValidateUtil.validate(config, shelfDouHuExpResults);
            log.info("[ProbeGeneAgent] validateBusiness 工具复检结果={}, conversationId={}, config={}, shelfDouHuExpResults={}, dsl={} ",
                    SerializeUtils.toJsonStr(result), param.getConversationId(), SerializeUtils.toJsonStr(config), SerializeUtils.toJsonStr(shelfDouHuExpResults), dsl);
            if (result != null && result.isSuccess()) {
                return ValidateModel.passValidate();
            }
            return result == null ? ValidateModel.failedValidate("执行校验不通过") : ValidateModel.failedValidate(String.format("执行校验不通过:%s",result.getMsg()));
        } catch(Exception e) {
            log.error("[ProbeGeneAgent] validateBusiness error, config={}, douHuResults={}", config, SerializeUtils.toJsonStr(shelfDouHuExpResults), e);
        }
        return ValidateModel.failedValidate("执行校验不通过");
    }

    public ValidateModel validateDSL(ProbeGeneParam param, String summary, String dsl) {
        List<Message> msgList = Lists.newArrayList(SystemMessage.from(getValidateDSLPrompt(param)));
        msgList.add(UserMessage.from(String.format("请基于当前策略配置总结校验生成groovy脚本的正确性，当前策略配置总结为:\n %s，\n生成groovy表达式为:\n %s", summary, dsl)));
        AssistantMessage assistantMessage = RetryExecuteUtils.retryModelTimeoutExecute(() -> gpt4oMiniModel.sendMessages(msgList), 3);
        if (!LLMExceptionUtil.validateMsg(assistantMessage)) {
            Exception modelException = LLMExceptionUtil.extractModelException(assistantMessage);
            log.error("[ProbeGeneAgent] 模型复检结果为空 param={}, error={}", SerializeUtils.toJsonStr(param), modelException);
            return null;
        }
        log.info("[ProbeGeneAgent] validateDSL 模型复检结果={} param = {}", assistantMessage.getContent(), SerializeUtils.toJsonStr(param));
        try {
            return SerializeUtils.toObjThrowException(assistantMessage.getContent(), ValidateModel.class);
        } catch (Exception e) {
            log.error("[ProbeGeneAgent] validateDSL error, param = {}, assistantMessage= {}", SerializeUtils.toJsonStr(param), assistantMessage.getContent(), e);
            return null;
        }
    }

    private String getValidateDSLPrompt(ProbeGeneParam param) {
        return agentPrompt.PROBE_VALIDATE_DSL_PROMPT;
    }

    public String retryGenerateDSL(ProbeGeneParam param, List<Message> msgList, String summary, String onlineConfig, String dsl, String reason) {
        List<Message> newMsgList = Lists.newArrayList();
        newMsgList.addAll(Lists.newArrayList(getDSLSystemPrompt(param, onlineConfig, true)));
        newMsgList.addAll(msgList);
        if (StringUtils.isNotBlank(reason)) {
            newMsgList.add(UserMessage.from(String.format("当前groovy脚本不符合预期，理由：%s 请重新生成groovy脚本。异常groovy脚本为:\n %s", reason, dsl)));
        } else {
            newMsgList.add(UserMessage.from(String.format("当前groovy脚本不符合预期，请重新生成groovy脚本。异常groovy脚本为:\n %s", dsl)));
        }
        newMsgList.add(UserMessage.from(String.format("请基于聊天记录和当前策略配置总结重新生成groovy脚本，当前策略配置总结为: %s", summary)));
        AssistantMessage assistantMessage = RetryExecuteUtils.retryModelTimeoutExecute(() -> gpt4oCodeModel.sendMessages(newMsgList), 3);
        if (!LLMExceptionUtil.validateMsg(assistantMessage)) {
            Exception modelException = LLMExceptionUtil.extractModelException(assistantMessage);
            log.error("[ProbeGeneAgent] retryGenerateDSL 模型重新生成groovy脚本失败, param = {}, error={}", SerializeUtils.toJsonStr(param), modelException);
        }
        log.info("[ProbeGeneAgent] retryGenerateDSL 模型重新生成groovy脚本={}, param = {}", assistantMessage.getContent(), SerializeUtils.toJsonStr(param));
        return assistantMessage.getContent();
    }

    private void fillDouHuInfo(StructureInfoModel structureInfoModel) {
        if (CollectionUtils.isEmpty(structureInfoModel.getExpModels())) {
            return;
        }
        structureInfoModel.getExpModels().forEach(expModel -> {
            boolean fillResult = fillDouHuInfo(expModel);
            if (!fillResult) {
                log.error("[ProbeGeneAgent] fillDouHuInfo failed 斗斛信息填充失败, conversationId = {}, expModels = {}", structureInfoModel.getConversionId(), SerializeUtils.toJsonStr(structureInfoModel.getExpModels()));
                throw new JsonExtractModelException("斗斛信息填充失败");
            }
        });
    }

    private boolean fillDouHuInfo(ExpModel expModel) {
        if (expModel == null || StringUtils.isBlank(expModel.getExpId())) {
            return false;
        }
        ExpInfoModel expInfoModel = expManageProxy.getExpInfo(expModel.getExpId());
        log.info("[ProbeGeneAgent] fillDouHuInfo 调用斗斛实验详情接口 expModel = {}", SerializeUtils.toJsonStr(expModel));
        if (expInfoModel == null) {
            return false;
        }
        expModel.setExpName(expInfoModel.getExpName());
        expModel.setPlatform(expInfoModel.getPlatform());
        expModel.setStartTime(TimeUtils.convertLocalDateTimeToString(expInfoModel.getStartTime()));
        expModel.setEndTime(TimeUtils.convertLocalDateTimeToString(expInfoModel.getEndTime()));

        if (CollectionUtils.isEmpty(expModel.getExpStrategyModels())) {
            return false;
        }
        // 补充分组信息
        Map<String, ExpStrategyModel> expMap = expModel.getExpStrategyModels().stream().collect(Collectors.toMap(ExpStrategyModel::getExpStrategyShortName, Function.identity()));
        for (ExpStrategyModel group : expInfoModel.getExpStrategyModels()) {
            String matchShortName = matchExpShortName(expMap, group.getExpStrategyShortName());
            if (StringUtils.isNotEmpty(matchShortName) && expMap.containsKey(matchShortName)) {
                ExpStrategyModel expStrategyModel = expMap.get(matchShortName);
                expStrategyModel.setExpStrategyId(group.getExpStrategyId());
                expStrategyModel.setExpStrategyName(group.getExpStrategyName());
                expStrategyModel.setExpStrategyShortName(group.getExpStrategyShortName());
                expStrategyModel.setStrategyType(group.getStrategyType());
                expStrategyModel.setRatio(group.getRatio());
            } else {
                ExpStrategyModel reference = new ExpStrategyModel();
                reference.setExpStrategyId(group.getExpStrategyId());
                reference.setExpStrategyName(group.getExpStrategyName());
                reference.setStrategyType(group.getStrategyType());
                reference.setExpStrategyShortName(group.getExpStrategyShortName());
                reference.setRatio(group.getRatio());
                expModel.getExpStrategyModels().add(reference);
            }
        }
        return true;
    }

    private String matchExpShortName(Map<String, ExpStrategyModel> expMap, String standardExpShortName) {
        if (MapUtils.isEmpty(expMap) || StringUtils.isBlank(standardExpShortName)) {
            return null;
        }
        Set<String> keys = expMap.keySet();
        for (String key : keys) {
            if (key.toLowerCase().contains(standardExpShortName.toLowerCase())) {
                return key;
            }
        }
        return null;
    }

    public Boolean storage2Redis(ProbeGeneParam param, StructureInfoModel structureInfoModel, String code) {
        List<OperatorShelfConfigDTO> redisValue = Lists.newArrayList(ProbeGeneConverter.convertShelfConfig(param, structureInfoModel, code));
        String[] redisKey = buildRedisKey(param);
        log.info("[ProbeGeneAgent] storage2Redis 写入缓存详细信息, redisKey = {}, redisValue = {}, param = {}", SerializeUtils.toJsonStr(redisKey), SerializeUtils.toJsonStr(redisValue), SerializeUtils.toJsonStr(param));
        return squirrelProxy.syncSet(RedisConstant.SHELF_OPERATOR_CATEGORY, redisValue, redisKey);
    }

    private String[] buildRedisKey(ProbeGeneParam param) {
        String agent = OperatorShelfConfigSource.agent.toString();
        OperatorShelfConfigStrategyUnit configUnit = ProbeGeneConverter.convertStrategyUnit(param);
        String configUnitStr = configUnit == null ? "" : configUnit.toString();
        String conversationId = param.getConversationId() == null ? "" : param.getConversationId().toString();
        if (StringUtils.isBlank(configUnitStr) || StringUtils.isBlank(conversationId)) {
            log.error("[ProbeGeneAgent] 构造redis Key异常, param = {}", param);
            throw new InfrastructureExecuteException("生成代码缓存key异常，请联系管理员");
        }
        return new String[] {agent, configUnitStr, conversationId};
    }

    private String generateComponent(GenerateOutModel model) {
        return String.format(outputPrompt.SHELF_GENERATE_COMPONENT, SerializeUtils.toJsonStr(model));
    }

    private List<OperatorShelfExpSkDTO> getShelfDouHuExpResults(ExpModel expModel) {
        if (expModel == null || CollectionUtils.isEmpty(expModel.getExpStrategyModels())) {
            return Lists.newArrayList();
        }
        List<OperatorShelfExpSkDTO> result = Lists.newArrayList();
        for (ExpStrategyModel expStrategyModel : expModel.getExpStrategyModels()) {
            OperatorShelfExpSkDTO expSkDTO = new OperatorShelfExpSkDTO();
            expSkDTO.setExperimentResult(expStrategyModel.getExpStrategyId());
            if (isExpStrategy(expStrategyModel)) {
                expSkDTO.setExpSkType(OperatorShelfConfigExpSkType.experiment);
            } else {
                expSkDTO.setExpSkType(OperatorShelfConfigExpSkType.contrast);
            }
            result.add(expSkDTO);
        }
        return result;
    }

    private boolean isExpStrategy(ExpStrategyModel expStrategyModel) {
        if (StringUtils.isBlank(expStrategyModel.getStrategyType())) {
            return false;
        }
        int type = Integer.parseInt(expStrategyModel.getStrategyType());
        return ArkExpStrategyTypeEnum.EXPERIMENTAL.getCode() == type;
    }
}

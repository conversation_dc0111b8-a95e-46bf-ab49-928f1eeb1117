package com.sankuai.qpro.ai.professor.application.service.operator.probe.chat;

import com.google.gson.JsonObject;
import com.sankuai.qpro.ai.professor.application.utils.GsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.http.HttpStatus;
import org.springframework.http.codec.ServerSentEvent;
import reactor.core.publisher.FluxSink;

import java.util.Optional;
import java.util.concurrent.TimeUnit;

/**
 * @author: wu<PERSON><PERSON><PERSON>
 * @create: 2024-12-11
 * @description: 必须得在同一个线程内使用，否则流式输出不会返回
 */
@Slf4j
public class AgentChatManager {
    private static final ThreadLocal<FluxSink<ServerSentEvent<String>>> TASK_CONTEXT_THREAD_LOCAL = new ThreadLocal<>();
    private static final ThreadLocal<String> TASK_MSG_THEAD_LOCAL = new ThreadLocal<>();

    public static void init(FluxSink<ServerSentEvent<String>> sink) {
        TASK_CONTEXT_THREAD_LOCAL.set(sink);
        TASK_MSG_THEAD_LOCAL.set(StringUtils.EMPTY);
    }

    public static FluxSink<ServerSentEvent<String>> current() {
        return TASK_CONTEXT_THREAD_LOCAL.get();
    }

    public static String getCurMsg() {
        return TASK_MSG_THEAD_LOCAL.get();
    }

    public static void remove() {
        TASK_CONTEXT_THREAD_LOCAL.remove();
        TASK_MSG_THEAD_LOCAL.remove();
    }

    public static void sendMergedMsg(String text, String eventType) {
        String result = mergeMsg(text);
        send(result, eventType);
    }

    public static void send(String text, String eventType) {
        Optional.ofNullable(current()).ifPresent(sink -> sink.next(ServerSentEvent.builder(text).event(eventType).build()));
    }

    public static void simulateSendStream(String text, String eventType, String type) throws Exception {
        if (StringUtils.isBlank(text)) {
            return;
        }
        int len = text.length();
        int startIndex = 0;
        int endIndex = 0;
        // 每隔2个字符推送一次，模拟打字机效果
        while (endIndex < len) {
            endIndex = Math.min(endIndex + 2, len);
            String content = text.substring(startIndex, endIndex);
            // 出现<lui标签则直接返回
            if (text.substring(startIndex).startsWith("<lui")) {
                send(generateMessageMsg(type, text.substring(startIndex)), eventType);
                return;
            }
            startIndex += 2;
            send(generateMessageMsg(type, content), eventType);
            TimeUnit.MILLISECONDS.sleep(50);
        }
//        send(generateMessageMsg(type, text), eventType);
    }

    public static void sendCompleteMsg(String text, String eventType, String type) throws Exception {
        if (StringUtils.isBlank(text)) {
            return;
        }
        send(generateMessageMsg(type, text), eventType);
    }

    private static String mergeMsg(String text) {
        StringBuilder msgContext = new StringBuilder(getCurMsg());
        msgContext.append(text);
        TASK_MSG_THEAD_LOCAL.set(msgContext.toString());
        return msgContext.toString();
    }

    private static String generateJsonStr(String text) {
        int code = HttpStatus.OK.value();
        JsonObject jsonObject = new JsonObject();
        jsonObject.addProperty("data", text);
        jsonObject.addProperty("code", code);
        return GsonUtils.toJsonString(jsonObject);
    }

    private static JsonObject generateBaseData() {
        int code = HttpStatus.OK.value();
        JsonObject jsonObject = new JsonObject();
        jsonObject.addProperty("code", code);
        jsonObject.add("data", null);
        return jsonObject;
    }

    public static String generateContentMsg(String text) {
        JsonObject baseData = generateBaseData();
        if (StringUtils.isBlank(text)) {
            return GsonUtils.toJsonString(baseData);
        }
        JsonObject data = new JsonObject();
        data.addProperty("content", text);
        baseData.add("data", data);
        return GsonUtils.toJsonString(baseData);
    }

    public static String generateMessageMsg(String type, String text) {
        JsonObject baseData = generateBaseData();
        if (StringUtils.isBlank(type)) {
            return GsonUtils.toJsonString(baseData);
        }
        JsonObject data = new JsonObject();
        data.addProperty("type", type);
        data.addProperty("content", text);
        baseData.add("data", data);
        return GsonUtils.toJsonString(baseData);
    }

    public static String generateEndMsg(JsonObject data) {
        JsonObject baseData = generateBaseData();
        if (data == null) {
            return GsonUtils.toJsonString(baseData);
        }
        baseData.add("data", data);
        return GsonUtils.toJsonString(baseData);
    }
}

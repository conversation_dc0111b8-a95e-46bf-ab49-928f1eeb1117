package com.sankuai.qpro.ai.professor.application.service.operator.probe.chat;

import com.google.common.collect.Lists;
import com.meituan.mdp.langmodel.api.message.AssistantMessage;
import com.meituan.mdp.langmodel.api.message.Message;
import com.meituan.mdp.langmodel.api.message.UserMessage;
import com.meituan.mdp.langmodel.api.model.StreamResponseHandler;
import com.meituan.mdp.langmodel.component.model.chat.OpenAIChatModel;
import com.meituan.mdp.langmodel.component.model.chat.stream.FridayStreamChatLanguageModel;
import com.sankuai.qpro.ai.professor.application.model.operator.probe.chat.AgentChatModel;
import com.sankuai.qpro.ai.professor.application.model.operator.probe.chat.AgentOutputModel;
import com.sankuai.qpro.ai.professor.application.model.operator.probe.chat.MessageModel;
import com.sankuai.qpro.ai.professor.application.service.operator.probe.ProbeAgent;
import com.sankuai.qpro.ai.professor.application.utils.SerializeUtils;
import com.sankuai.qpro.ai.professor.entity.ConfigUnitEntity;
import com.sankuai.qpro.ai.professor.api.enums.RoleTypeEnum;
import com.sankuai.qpro.ai.professor.api.request.SendMessageRequest;
import com.sankuai.qpro.ai.professor.application.utils.GsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * @author: wuwenqiang
 * @create: 2024-12-10
 * @description:
 */
@Service
@Slf4j
public class AgentChatService {

    @Qualifier("gpt4oMiniModel")
    @Autowired
    private OpenAIChatModel openAIChatModel;
    @Autowired
    private FridayStreamChatLanguageModel streamChatModel;
    @Autowired
    private ConversationStoreService conversationStoreService;
    @Autowired
    private ProbeAgent probeAgent;

    // 由于一次请求耗时可能很长，不加事务
    public AgentChatModel chat(SendMessageRequest request) {
        if (invalidChatRequest(request)) {
            return null;
        }
        try {
            // 1.补充历史消息记录
            List<Message> messageList = Lists.newArrayList();
            List<Message> historyMsgs = conversationStoreService.queryHistoryMessages(request.getConversationId());
            if (CollectionUtils.isNotEmpty(historyMsgs)) {
                messageList.addAll(historyMsgs);
            }
            log.info("chat histToryMessage = {}, conversationId = {}", SerializeUtils.toJsonStr(messageList), request.getConversationId());

            // 2.信息交互 + 将用户输入写入数据库
            UserMessage userMessage = UserMessage.from(request.getMsg());
            messageList.add(userMessage);
            log.info("chat userMessage = {}, conversationId = {}", GsonUtils.toJsonString(userMessage), request.getConversationId());
            conversationStoreService.saveChatMsgs(request.getConversationId(), Lists.newArrayList(convertMessageModel(userMessage, RoleTypeEnum.USER, null, 0)));
            ConfigUnitEntity configUnitInfo = conversationStoreService.queryConfigUnitOfConversation(request.getConversationId());
            if (Objects.isNull(configUnitInfo)) {
                log.error("未找到当前会话管理的配置单元信息, conversationId={}", request.getConversationId());
                throw new RuntimeException("未找到当前会话管理的配置单元信息");
            }

            // 3.调用Agent
            AgentOutputModel agentOutputModel = probeAgent.execute(messageList, request.getConversationId(), configUnitInfo.getName());
            if (Objects.isNull(agentOutputModel) || StringUtils.isEmpty(agentOutputModel.getMsg())) {
                throw new RuntimeException("Agent生成消息异常");
            }

            // 4.将AI输出写入数据库
            AssistantMessage assistantMessage = AssistantMessage.from(agentOutputModel.getMsg());
            log.info("chat aiMessage = {}, conversationId = {}", assistantMessage, request.getConversationId());
            Long messageId = conversationStoreService.saveChatMsg(request.getConversationId(), convertMessageModel(assistantMessage, RoleTypeEnum.AI, agentOutputModel.getSegment().getCode(), agentOutputModel.getStatus()));
            return buildAgentChatModel(agentOutputModel, messageId);
        } catch (Exception e) {
            log.error("[AgentChatService] chat error, request = {}", GsonUtils.toJsonString(request), e);
            throw new RuntimeException("Agent生成消息异常");
        }
    }

    public void chatStream(SendMessageRequest request, StreamResponseHandler<AssistantMessage> handler) {
        if (invalidChatRequest(request)) {
            return;
        }
        List<Message> messageList = Lists.newArrayList(UserMessage.from(request.getMsg()));
        streamChatModel.sendMessages(messageList, handler);
    }

    private boolean invalidChatRequest(SendMessageRequest request) {
        return Objects.isNull(request)
                || Objects.isNull(request.getConversationId()) || request.getConversationId() <= 0
                || StringUtils.isEmpty(request.getMsg());
    }

    private MessageModel convertMessageModel(Message userMessage, RoleTypeEnum roleTypeEnum, String dialogSegment, int status) {
        if (Objects.isNull(userMessage) || Objects.isNull(userMessage.getContent())) {
            return null;
        }
        if (!(userMessage.getContent() instanceof String)) {
            return null;
        }
        String content = (String) userMessage.getContent();
        MessageModel messageModel = new MessageModel();
        messageModel.setRole(roleTypeEnum.getType());
        messageModel.setContent(content);
        messageModel.setDialogSegment(dialogSegment);
        messageModel.setStatus(status);
        return messageModel;
    }

    private AgentChatModel buildAgentChatModel(AgentOutputModel agentOutputModel, Long messageId) {
        AgentChatModel model = new AgentChatModel();
        model.setMessageId(messageId);
        model.setMsg(agentOutputModel.getMsg());
        model.setSegment(agentOutputModel.getSegment());
        return model;
    }
}

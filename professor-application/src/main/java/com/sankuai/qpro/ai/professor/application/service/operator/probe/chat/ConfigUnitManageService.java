package com.sankuai.qpro.ai.professor.application.service.operator.probe.chat;

import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import com.meituan.mdp.boot.starter.config.annotation.MdpConfig;
import com.sankuai.qpro.ai.professor.application.utils.SerializeUtils;
import com.sankuai.qpro.ai.professor.entity.ConfigUnitEntity;
import com.sankuai.qpro.ai.professor.repository.ConfigUnitRepository;
import com.sankuai.qpro.ai.professor.api.response.ConfigUnitModel;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @author: wuwen<PERSON><PERSON>
 * @create: 2024-12-31
 * @description: 配置单元管理服务
 */
@Service
@Slf4j
public class ConfigUnitManageService {

    @MdpConfig("PROBE_CONFIG_UNIT_IMAGE_CONFIG")
    private String configUnitImageConfigs;

    @Autowired
    private ConfigUnitRepository configUnitRepository;

    /**
     * 查询全量配置单元
     * @return
     */
    public List<ConfigUnitModel> queryConfigUnits() {
        List<ConfigUnitEntity> configUnitInfos = configUnitRepository.findAll();
        if (CollectionUtils.isEmpty(configUnitInfos)) {
            return Lists.newArrayList();
        }
        return configUnitInfos.stream().map(this::convert)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    /**
     * 根据scene查询配置单元
     * @param scene 场景
     * @return
     */
    public List<ConfigUnitModel> queryConfigUnits(String scene) {
        if (StringUtils.isBlank(scene)) {
            return Lists.newArrayList();
        }
        List<ConfigUnitEntity> configUnitInfos = configUnitRepository.findByScene(scene);
        if (CollectionUtils.isEmpty(configUnitInfos)) {
            return Lists.newArrayList();
        }
        return configUnitInfos.stream().map(this::convert)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    /**
     * 根据id查询配置单元
     * @param id
     * @return
     */
    public ConfigUnitEntity queryConfigUnit(Long id) {
        if (Objects.isNull(id)) {
            return null;
        }
        return configUnitRepository.findById(id);
    }

    private ConfigUnitModel convert(ConfigUnitEntity configUnit) {
        if (Objects.isNull(configUnit)) {
            return null;
        }
        ConfigUnitModel configUnitModel = new ConfigUnitModel();
        configUnitModel.setId(configUnit.getId());
        configUnitModel.setName(configUnit.getName());
        configUnitModel.setScene(configUnit.getScene());
        configUnitModel.setDesc(configUnit.getDescription());
        configUnitModel.setImageUrl(getConfigUnitImage(configUnit.getName()));
        return configUnitModel;
    }

    private String getConfigUnitImage(String name) {
        if (StringUtils.isBlank(name)) {
            return null;
        }
        Map<String, String> configUnitConfigs = SerializeUtils.toObj(configUnitImageConfigs, new TypeReference<Map<String, String>>() {});
        if (MapUtils.isNotEmpty(configUnitConfigs) && configUnitConfigs.containsKey(name) ) {
            return configUnitConfigs.get(name);
        }
        return null;
    }
}

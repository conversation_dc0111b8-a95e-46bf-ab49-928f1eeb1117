package com.sankuai.qpro.ai.professor.application.service.operator.probe.chat;

import com.sankuai.qpro.ai.professor.api.enums.ConversationStageEnum;
import com.sankuai.qpro.ai.professor.api.enums.SegmentEnum;
import com.sankuai.qpro.ai.professor.application.model.operator.exception.execute.InfrastructureExecuteException;
import com.sankuai.qpro.ai.professor.application.model.operator.exception.model.extract.ToolParamExractModelException;
import com.sankuai.qpro.ai.professor.application.model.operator.probe.agent.conf.ProbeConfParam;
import com.sankuai.qpro.ai.professor.application.model.operator.probe.agent.gene.param.ProbeGeneParam;
import com.sankuai.qpro.ai.professor.application.utils.SerializeUtils;
import com.sankuai.qpro.ai.professor.entity.ConversationParamEntity;
import com.sankuai.qpro.ai.professor.mapper.ConversationParamMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/1/14
 */
@Slf4j
@Service
public class ConversationParamService {

    @Resource
    private ConversationParamMapper conversationParamMapper;

    public void updateCategory(Long conversationId, List<Integer> poiCategoryIdList, List<String> poiCategoryNames, Long productCategoryId, String productCategoryName) {
        ConversationParamEntity conversationParamEntity = getConversationParamEntity(conversationId, SegmentEnum.CATEGORY);

        conversationParamEntity.setStage(ConversationStageEnum.CATEGORY_SELECTED.getCode());
        conversationParamEntity.setPoiCategoryIds(poiCategoryIdList.stream().map(String::valueOf).collect(Collectors.joining(",")));
        conversationParamEntity.setPoiCategoryNames(poiCategoryNames.stream().map(String::valueOf).collect(Collectors.joining(",")));
        conversationParamEntity.setProductCategoryId(productCategoryId);
        conversationParamEntity.setProductCategoryName(productCategoryName);

        // 重新选分类后清空之后的数据
        conversationParamEntity.setExpIds(null);
        conversationParamEntity.setStrategy(null);
        conversationParamEntity.setStage(ConversationStageEnum.CATEGORY_SELECTED.getCode());

        conversationParamMapper.update(conversationParamEntity);
    }

    public ConversationParamEntity updateStrategy(Long conversationId, String strategy) {
        ConversationParamEntity conversationParamEntity = getConversationParamEntity(conversationId, SegmentEnum.GENERATE);
        log.info("[ConversationParamService] updateStrategy，conversationId={}, conversationParamEntity={}", conversationId, SerializeUtils.toJsonStr(conversationParamEntity));

        conversationParamEntity.setStage(ConversationStageEnum.STRATEGY_GENERATED.getCode());
        conversationParamEntity.setStrategy(strategy);
        conversationParamMapper.update(conversationParamEntity);
        return conversationParamEntity;
    }

    public ConversationParamEntity updateStage(Long conversationId, ConversationStageEnum stageEnum, SegmentEnum segmentEnum) {
        ConversationParamEntity conversationParamEntity = getConversationParamEntity(conversationId, segmentEnum);
        log.info("[ConversationParamService] updateStage，conversationId={}, conversationParamEntity={}", conversationId, conversationParamEntity);

        conversationParamEntity.setStage(stageEnum.getCode());
        conversationParamMapper.update(conversationParamEntity);
        return conversationParamEntity;
    }

    public ConversationParamEntity updateExpIds(Long conversationId, List<String> expIds, ConversationStageEnum stageEnum) {
        ConversationParamEntity conversationParamEntity = getConversationParamEntity(conversationId, SegmentEnum.EXP);
        log.info("[ConversationParamService] updateExpIds，conversationId={}, conversationParamEntity={}", conversationId, conversationParamEntity);

        conversationParamEntity.setExpIds(String.join(",", expIds));
        conversationParamEntity.setStage(stageEnum.getCode());

        // 变更实验号后清空之后的数据
        conversationParamEntity.setStrategy(null);

        conversationParamMapper.update(conversationParamEntity);
        return conversationParamEntity;
    }

    public ProbeConfParam getProbeConfParam(ProbeConfParam probeConfParam) {
        ConversationParamEntity conversationParamEntity = getConversationParamEntity(probeConfParam.getConversationId(), SegmentEnum.CONF);
        log.info("[ConversationParamService] getProbeConfParam 查询会话参数，conversationParamEntity={}，conversationId={}", conversationParamEntity, probeConfParam.getConversationId());

        conversationParamEntity.setStage(ConversationStageEnum.CONF_ENTERED.getCode());
        conversationParamEntity.setStrategy(""); // clear 配置之后的信息
        conversationParamMapper.update(conversationParamEntity);
        log.info("[ConversationParamService] getProbeConfParam 清除策略小结，conversationId={}, conversationParamEntity={}", probeConfParam.getConversationId(), conversationParamEntity);

        List<Integer> poiCategoryIds = Arrays.stream(conversationParamEntity.getPoiCategoryIds().split(","))
                                             .map(Integer::valueOf)
                                             .collect(Collectors.toList());
        probeConfParam.setPoiCategoryList(poiCategoryIds);
        probeConfParam.setProductCategoryId(conversationParamEntity.getProductCategoryId());
        return probeConfParam;
    }

    public ProbeGeneParam getProbeGeneParam(ProbeGeneParam probeGeneParam) {
        if (probeGeneParam.getConversationId() == null) {
            throw new RuntimeException("getProbeConfParam conversionId不能为空");
        }
        ConversationParamEntity conversationParamEntity = getConversationParamEntity(probeGeneParam.getConversationId(), SegmentEnum.GENERATE);
        log.info("[ConversationParamService] getProbeConfParam 清除策略小结，conversationId={}, conversationParamEntity={}", probeGeneParam.getConversationId(), conversationParamEntity);

        List<Integer> poiCategoryIds = Arrays.stream(conversationParamEntity.getPoiCategoryIds().split(","))
                .map(Integer::valueOf)
                .collect(Collectors.toList());
        List<String> poiCategoryNames = Arrays.stream(conversationParamEntity.getPoiCategoryNames().split(","))
                .collect(Collectors.toList());
        probeGeneParam.setPoiCategoryList(poiCategoryIds);
        probeGeneParam.setPoiCategoryNames(poiCategoryNames);
        probeGeneParam.setProductCategoryId(conversationParamEntity.getProductCategoryId());
        probeGeneParam.setProductCategoryName(conversationParamEntity.getProductCategoryName());
        probeGeneParam.setStrategy(conversationParamEntity.getStrategy());
        return probeGeneParam;
    }

    public ConversationParamEntity getConversationParamEntity(Long conversationId, SegmentEnum segmentEnum) {
        ConversationParamEntity entity = conversationParamMapper.selectByConversationId(conversationId);
        if (entity == null) {
            throw new ToolParamExractModelException(segmentEnum.getCode() + "阶段，conversationId=" + conversationId + " 查询无param记录");
        }
        return entity;
    }

    public ConversationParamEntity getConversationParamEntity(Long conversationId) {
        ConversationParamEntity entity = conversationParamMapper.selectByConversationId(conversationId);
        return entity;
    }

    public ConversationParamEntity updateConversationParam(ConversationParamEntity entity) {
        if (entity == null || entity.getConversationId() == null) {
            throw new InfrastructureExecuteException("更新conversationParam失败，conversationParam为空");
        }
        conversationParamMapper.update(entity);
        return entity;
    }
}

package com.sankuai.qpro.ai.professor.application.service.operator.probe.chat;


import com.google.common.base.Preconditions;
import com.meituan.mdp.langmodel.api.message.AssistantMessage;
import com.meituan.mdp.langmodel.api.message.Message;
import com.meituan.mdp.langmodel.api.message.UserMessage;
import com.sankuai.dzviewscene.unifiedshelf.operator.api.enums.OperatorShelfConfigStrategyUnit;
import com.sankuai.qpro.ai.professor.api.enums.*;
import com.sankuai.qpro.ai.professor.application.model.operator.probe.chat.MessageModel;
import com.sankuai.qpro.ai.professor.application.utils.GsonUtils;
import com.sankuai.qpro.ai.professor.entity.ConfigUnitEntity;
import com.sankuai.qpro.ai.professor.entity.ConversationEntity;
import com.sankuai.qpro.ai.professor.entity.ConversationMsgEntity;
import com.sankuai.qpro.ai.professor.entity.ConversationParamEntity;
import com.sankuai.qpro.ai.professor.mapper.ConversationParamMapper;
import com.sankuai.qpro.ai.professor.repository.ConversationBaseRepository;
import com.sankuai.qpro.ai.professor.repository.ConversationHistoryRepository;
import com.sankuai.qpro.ai.professor.api.request.CreateConversationRequest;
import com.sankuai.qpro.ai.professor.api.response.CreateConversationResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @author: wuwenqiang
 * @create: 2024-12-10
 * @description: 会话存储Service
 */
@Service
@Slf4j
public class ConversationStoreService {
    @Autowired
    private ConversationBaseRepository conversationBaseRepository;

    @Autowired
    private ConversationHistoryRepository conversationHistoryRepository;

    @Autowired
    private ConfigUnitManageService configUnitManageService;

    @Resource
    private ConversationParamMapper conversationParamMapper;

    /**
     * 创建会话
     * @param request
     * @return
     * @throws Exception
     */
    @Transactional(rollbackFor = {Exception.class})
    public CreateConversationResponse createConversation(CreateConversationRequest request) throws Exception {
        ConversationEntity baseInfo = buildConversationBaseInfo(request);
        try {
            // 1.创建会话记录
            Long id = conversationBaseRepository.insert(baseInfo);
            if (id == null || id <= 0) {
                throw new RuntimeException("系统异常");
            }
            Long conversationId = baseInfo.getId();

            // 2. 创建会话参数表
            Long paramId = conversationParamMapper.insert(buildConversationParam(conversationId));
            if (paramId == null || paramId <= 0) {
                throw new RuntimeException("创建会话参数表异常");
            }

            return CreateConversationResponse
                    .builder()
                    .id(conversationId)
                    .previewUnitName(queryPreviewUnitName(baseInfo.getConfigUnitId()))
                    .build();
        } catch (Exception e) {
            log.error("[ConversationStoreService] createConversation error, request = {}", GsonUtils.toJsonString(request), e);
            throw new RuntimeException("创建会话记录失败", e);
        }
    }

    private ConversationParamEntity buildConversationParam(Long conversationId) {
        ConfigUnitEntity configUnitEntity = queryConfigUnitOfConversation(conversationId);
        if (configUnitEntity == null) {
            throw new RuntimeException("未找到配置单元信息");
        }
        return ConversationParamEntity.of(conversationId, configUnitEntity.getName());
    }

    /**
     * 插入历史消息
     * @param conversationId 会话ID
     * @param messageModels 消息模型
     */
    @Transactional(rollbackFor = {Exception.class})
    public void saveChatMsgs(Long conversationId, List<MessageModel> messageModels) {
        try {
            Preconditions.checkArgument(conversationId != null && conversationId > 0, "会话ID不合法");
            Preconditions.checkArgument(CollectionUtils.isNotEmpty(messageModels), "消息不能为空");
            List<ConversationMsgEntity> historys = messageModels.stream()
                    .filter(Objects::nonNull)
                    .map(messageModel -> buildConversationHistory(conversationId, messageModel))
                    .collect(Collectors.toList());
            conversationHistoryRepository.batchInsert(historys);
        } catch (Exception e) {
            log.error("[ConversationStoreService] insertChatMsg error, conversationId = {}, messageModels = {}", conversationId, GsonUtils.toJsonString(messageModels), e);
            throw new RuntimeException("历史消息写入失败", e);
        }
    }

    @Transactional(rollbackFor = {Exception.class})
    public Long saveChatMsg(Long conversationId, MessageModel messageModel) {
        try {
            Preconditions.checkArgument(conversationId != null && conversationId > 0, "会话ID不合法");
            Preconditions.checkArgument(messageModel != null, "消息不能为空");
            ConversationMsgEntity history = buildConversationHistory(conversationId, messageModel);
            Long id = conversationHistoryRepository.insert(history);
            if (id == null || id <= 0) {
                throw new RuntimeException("系统异常");
            }
            return id;
        } catch (Exception e) {
            log.error("[ConversationStoreService] saveChatMsg error, conversationId = {}, messageModels = {}", conversationId, GsonUtils.toJsonString(messageModel), e);
            throw new RuntimeException("历史消息写入失败", e);
        }
    }

    @Transactional(rollbackFor = {Exception.class})
    public void updateReviewType(Long msgId, int reviewType) {
        try {
            conversationHistoryRepository.updateReviewType(msgId, reviewType);
        } catch (Exception e) {
            log.error("[ConversationStoreService] updateReviewType error, messageId = {}, reviewType= {} ", msgId, reviewType, e);
            throw new RuntimeException("更新消息评论失败", e);
        }
    }

    /**
     * 查询历史消息
     * @param conversationId
     * @return
     */
    public List<Message> queryHistoryMessages(Long conversationId) {
        return queryMessages(conversationId, false);
    }

    /**
     * 查询非系统历史消息
     * @param conversationId
     * @return
     */
    public List<Message> queryHistoryMessagesWithoutSystem(Long conversationId) {
        return queryMessages(conversationId, true);
    }

    /**
     * 查询当前会话的最新消息
     * @param conversationId
     * @return
     */
    public Message queryLatestMessage(Long conversationId) {
        ConversationMsgEntity message = conversationHistoryRepository.findLatestMsgByConversationId(conversationId);
        return convertMessageModel(message);
    }

    /**
     * 查询与配置相关的历史消息（包含最新的实验信息）
     * @param conversationId
     * @return
     */
    public List<Message> queryHistoryMessagesForConfigSegment(Long conversationId) {
        // 1. 查找最新的exp对话片段
        ConversationMsgEntity expLatestHistory = conversationHistoryRepository.findLatestSegment(conversationId, SegmentEnum.EXP.getCode());
        if (expLatestHistory == null) {
            return null;
        }
        Long expLatestHistoryId = expLatestHistory.getId();

        // 2. 查找exp对话片段之前的对话记录
        List<ConversationMsgEntity> historys = conversationHistoryRepository.findByConversationIdAndStartId(conversationId, expLatestHistoryId);
        if (CollectionUtils.isEmpty(historys)) {
            return null;
        }
        return historys.stream()
                .filter(Objects::nonNull)
                .filter(history -> filterGenerateAndError(history))
                .map(this::convertMessageModel)
                .collect(Collectors.toList());
    }

    public Message queryLatestCategoryMessage(Long conversationId) {
        // 查找最新的分类对话
        ConversationMsgEntity categoryLatestHistory = conversationHistoryRepository.findLatestSegment(conversationId, SegmentEnum.CATEGORY.getCode());
        if (categoryLatestHistory == null) {
            return null;
        }
        return convertMessageModel(categoryLatestHistory);
    }

    public ConversationMsgEntity queryLatestValidAIMessage(Long conversationId) {
        return conversationHistoryRepository.findLatestAIMsg(conversationId, MessageStatusEnum.SUCCESS.getCode());
    }

    private boolean filterGenerateAndError(ConversationMsgEntity history) {
        return !SegmentEnum.GENERATE.getCode().equals(history.getDialogSegment()) && !SegmentEnum.ERROR.getCode().equals(history.getDialogSegment())
                && MessageStatusEnum.ERROR.getCode() != history.getStatus();
    }

    /**
     * 查询会话关联配置单元信息
     * @param conversationId 会话ID
     * @return 配置单元信息
     */
    public ConfigUnitEntity queryConfigUnitOfConversation(Long conversationId) {
        ConversationEntity baseInfo = conversationBaseRepository.findById(conversationId);
        if (baseInfo == null) {
            return null;
        }
        Long configUnitId = baseInfo.getConfigUnitId();
        return configUnitManageService.queryConfigUnit(configUnitId);
    }

    /**
     * 查询会话关联用户ID
     * @param conversationId
     * @return
     */
    public String queryUserId(Long conversationId) {
        ConversationEntity baseInfo = conversationBaseRepository.findById(conversationId);
        if (baseInfo == null) {
            return null;
        }
        return baseInfo.getUserId();
    }

    private List<Message> queryMessages(Long conversationId, boolean excludeSystem) {
        if (conversationId == null || conversationId <= 0) {
            return null;
        }
        try {
            List<ConversationMsgEntity> historyMsgs = conversationHistoryRepository.findByConversationId(conversationId);
            if (CollectionUtils.isEmpty(historyMsgs)) {
                return null;
            }
            return historyMsgs.stream()
                    .filter(Objects::nonNull)
                    .filter(history -> !excludeSystem || history.getRole() != RoleTypeEnum.SYSTEM.getType())
                    .map(this::convertMessageModel)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("[ConversationStoreService] queryMessages error, conversationId = {}, excludeSystem = {}", conversationId, excludeSystem, e);
        }
        return null;
    }

    private ConversationEntity buildConversationBaseInfo(CreateConversationRequest request) {
        ConversationEntity baseInfo = new ConversationEntity();
        baseInfo.setConfigUnitId(request.getConfigUnitId());
        baseInfo.setUserId(request.getUserMis());
        baseInfo.setUserType(UserTypeEnum.EMPLOYEE.getType());
        return baseInfo;
    }

    private ConversationMsgEntity buildConversationHistory(Long conversationId, MessageModel messageModel) {
        ConversationMsgEntity history = new ConversationMsgEntity();
        history.setConversationId(conversationId);
        history.setRole(messageModel.getRole());
        history.setMessage(messageModel.getContent());
        history.setDialogSegment(messageModel.getDialogSegment());
        history.setStatus(messageModel.getStatus());
        return history;
    }


    private Message convertMessageModel(ConversationMsgEntity history) {
        if (history == null || history.getRole() == null || StringUtils.isEmpty(history.getMessage())) {
            return null;
        }
        if (RoleTypeEnum.USER.getType() == history.getRole()) {
            return UserMessage.from(history.getMessage());
        } else if (RoleTypeEnum.AI.getType() == history.getRole()) {
            return AssistantMessage.from(history.getMessage());
        }
        return null;
    }

    private String queryPreviewUnitName(Long configUnitId) {
        if (configUnitId == null || configUnitId <= 0) {
            return null;
        }
        ConfigUnitEntity configUnitInfo = configUnitManageService.queryConfigUnit(configUnitId);
        if (configUnitInfo == null) {
            return null;
        }
        String configUnitName = configUnitInfo.getName();
        if (ConfigUnitEnum.SHELF_SUBTITLE.getCode().equals(configUnitName)) {
            return OperatorShelfConfigStrategyUnit.subTitle.toString();
        } else if (ConfigUnitEnum.TITLE_PREFIX.getCode().equals(configUnitName)) {
            return OperatorShelfConfigStrategyUnit.preTitle.toString();
        }
        return null;
    }


}

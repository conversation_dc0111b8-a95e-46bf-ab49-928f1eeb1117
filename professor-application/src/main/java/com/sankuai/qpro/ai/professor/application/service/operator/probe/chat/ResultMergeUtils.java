package com.sankuai.qpro.ai.professor.application.service.operator.probe.chat;

import com.dianping.lion.Environment;
import com.sankuai.dzviewscene.unifiedshelf.operator.api.dto.OperatorShelfConfigDTO;
import com.sankuai.qpro.ai.professor.application.model.operator.probe.agent.category.UnifiedCategoryModel;
import com.sankuai.qpro.ai.professor.entity.ConfigUnitEntity;
import org.apache.commons.lang.StringUtils;

/**
 * @author: wuwenqiang
 * @create: 2025-01-15
 * @description:
 */
public class ResultMergeUtils {

    public static void mergeCategoryResult(StringBuilder result, UnifiedCategoryModel shopCategory, UnifiedCategoryModel productCategory) {
        result.append("POI分类=").append(String.format("%s（%s）", shopCategory.getCategoryName(), shopCategory.getCategoryId()))
                .append("，团购分类=").append(String.format("%s（%s）", productCategory.getCategoryName(), productCategory.getPlatformCategoryId()))
                .append("\n\n");
    }

    public static void mergeCategoryResult(StringBuilder result, Integer shopCategoryId, String shopCategoryName, Long productCategoryId, String productCategoryName) {
        result.append("POI分类=").append(String.format("%s（%s）", shopCategoryName, shopCategoryId))
                .append("，团购分类=").append(String.format("%s（%s）", productCategoryName, productCategoryId))
                .append("\n\n");
    }

    public static void mergeConfigUnitResult(StringBuilder result, ConfigUnitEntity configUnitInfo) {
        if (configUnitInfo == null || StringUtils.isBlank(configUnitInfo.getDescription())) {
            return;
        }
        result.append("在").append(configUnitInfo.getDescription()).append("\n");
    }

    public static void mergeExpConflictResult(StringBuilder result, OperatorShelfConfigDTO expConfig, String linkFormat) {
        if (expConfig == null) {
            return;
        }
        // 非探针实验
        if (expConfig.getStrategyId() == -1) {
            result.append(String.format("存在线上斗斛实验，暂无法配置探针策略。详细信息可咨询平台研发。\n"));
            return;
        }
        // 探针实验
        result.append(String.format("存在生效中的实验策略（策略ID： [%s](%s) )，暂无法进行实验。\n", expConfig.getStrategyId(), buildStrategyLink(linkFormat, expConfig.getStrategyId())));
    }

    private static String buildStrategyLink(String linkFormat, long strategyId) {
        return String.format(linkFormat, strategyId);
    }
}

package com.sankuai.qpro.ai.professor.application.service.operator.probe.proxy;

import com.dianping.general.category.dto.GeneralCategoryDTO;
import com.dianping.general.category.response.Response;
import com.dianping.general.category.service.GeneralCategoryService;
import com.dianping.poi.cateproperty.api.dto.query.POICategoryQuery;
import com.dianping.poi.cateproperty.api.service.POICategoryService;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.meituan.nibscp.domain.accessory.utils.general.GeneralCategoryMappingUtils;
import com.sankuai.qpro.ai.professor.application.model.operator.exception.execute.RpcExecuteException;
import com.sankuai.qpro.ai.professor.application.model.operator.probe.agent.category.UnifiedCategoryModel;
import com.sankuai.qpro.ai.professor.application.utils.RetryExecuteUtils;
import com.sankuai.qpro.ai.professor.application.utils.SerializeUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * @author: wuwenqiang
 * @create: 2025-01-02
 * @description: 类目查询服务(POI/商品)
 */
@Service
@Slf4j
public class CategoryServiceProxy {

    private final static int DEAL_GROUP_TYPE = 3;

    @Autowired
    private GeneralCategoryService generalCategoryService;

    @Autowired
    private POICategoryService poiCategoryService;

    /**
     * 查询商品平台类目
     * @param categoryIds
     * @return
     */
    public List<UnifiedCategoryModel> getProductCategoryListByPlatformIds(List<Long> categoryIds) {
        if (CollectionUtils.isEmpty(categoryIds)) {
            return Lists.newArrayList();
        }
        List<GeneralCategoryDTO> categories = queryCategoryTree(DEAL_GROUP_TYPE);
        if (CollectionUtils.isEmpty(categories)) {
            return Lists.newArrayList();
        }
        Map<Long, UnifiedCategoryModel> categoryModelMap = Maps.newHashMap();
        for (GeneralCategoryDTO category : categories) {
            matchPlatformCategory(category, categoryIds, categoryModelMap);
        }
        return categoryIds.stream().filter(categoryModelMap::containsKey)
                .map(categoryModelMap::get).filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    /**
     * 转换平台商品类目
     * @param categoryIds
     * @return
     */
    public List<UnifiedCategoryModel> getProductCategoryListByBpIds(List<Long> categoryIds) {
        if (CollectionUtils.isEmpty(categoryIds)) {
            return Lists.newArrayList();
        }
        List<Long> platformCategoryIds = convertCategory2PlatformCategoryId(categoryIds);
        return getProductCategoryListByPlatformIds(platformCategoryIds);
    }

    /**
     * 转换平台商品类目
     * @param categoryIds
     * @return
     */
    public List<Long> convertCategory2PlatformCategoryId(List<Long> categoryIds) {
        if (CollectionUtils.isEmpty(categoryIds)) {
            return Lists.newArrayList();
        }
        return categoryIds.stream().filter(Objects::nonNull)
                .map(id -> GeneralCategoryMappingUtils.dealGetPlatformCategoryByBpCategory(id.intValue()))
                .collect(Collectors.toList());
    }

    /**
     * 获取商户分类
     * @param categoryIds
     * @return
     */
    public List<UnifiedCategoryModel> getPoiCategoryListByIds(List<Long> categoryIds) {
        if (CollectionUtils.isEmpty(categoryIds)) {
            return Lists.newArrayList();
        }
        List<Integer> intCategoryIds = categoryIds.stream().map(Long::intValue).collect(Collectors.toList());
        List<POICategoryQuery> categoryQueries = null;
        try {
            categoryQueries = RetryExecuteUtils.retryExecute(() -> poiCategoryService.findCategories(intCategoryIds), 3, 200L);
        } catch (Exception e) {
            throw new RpcExecuteException("查询类目信息的RPC接口报错", e);
        }
        if (CollectionUtils.isEmpty(categoryQueries)) {
            return Lists.newArrayList();
        }
        return categoryQueries.stream().filter(Objects::nonNull)
                .map(this::convertPoiCategoryModel).collect(Collectors.toList());
    }

    private void matchPlatformCategory(GeneralCategoryDTO category, List<Long> categoryIds, Map<Long, UnifiedCategoryModel> result) {
        if (category == null) {
            return;
        }
        if (categoryIds.contains(category.getPlatformCategoryId())) {
            // 仅添加第一个匹配到的类目
            if (!result.containsKey(category.getPlatformCategoryId())) {
                result.put(category.getPlatformCategoryId(), convertCategoryModel(category));
            }
        }
        if (CollectionUtils.isEmpty(category.getChildList())) {
            return;
        }
        for (GeneralCategoryDTO child : category.getChildList()) {
            matchPlatformCategory(child, categoryIds, result);
        }
    }

    private UnifiedCategoryModel convertCategoryModel(GeneralCategoryDTO category) {
        UnifiedCategoryModel model = new UnifiedCategoryModel();
        model.setCategoryId(category.getCategoryId());
        model.setCategoryName(category.getCategoryName());
        model.setLevel(category.getLevel());
        model.setPlatformCategoryId(category.getPlatformCategoryId());
        return model;
    }

    private UnifiedCategoryModel convertPoiCategoryModel(POICategoryQuery category) {
        UnifiedCategoryModel model = new UnifiedCategoryModel();
        model.setCategoryId((long) Optional.ofNullable(category.getCategoryId()).orElse(0));
        model.setCategoryName(category.getCategoryName());
        model.setLevel(category.getCategoryLevel());
        return model;
    }

    private List<GeneralCategoryDTO> queryCategoryTree(int tradeType) {
        Response<String> response = null;
        try {
            response = RetryExecuteUtils.retryExecute(() -> generalCategoryService.getCategoryTree(tradeType), 3, 200L);
        } catch (Exception e) {
            throw new RpcExecuteException(e);
        }
        if (response == null || !response.isSuccess()) {
            log.error("generalCategoryService.getCategoryTree error, tradeType:{}, response:{}", tradeType, response);
            return Lists.newArrayList();
        }
        String result = response.getResult();
        return SerializeUtils.toObj(result, new TypeReference<List<GeneralCategoryDTO>>() {});
    }

}

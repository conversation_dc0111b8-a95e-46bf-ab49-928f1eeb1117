package com.sankuai.qpro.ai.professor.application.service.operator.probe.proxy;

import com.dianping.cat.Cat;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.meituan.nibscp.common.api.enums.BizLineEnum;
import com.sankuai.nibscp.cpv.api.common.enums.BizApplicationEnum;
import com.sankuai.nibscp.cpv.api.common.enums.PropertySourceEnum;
import com.sankuai.nibscp.cpv.api.remote.dto.BusinessPropertyListDTO;
import com.sankuai.nibscp.cpv.api.remote.request.GetBusinessPropertyDraftByPropertySourceRequest;
import com.sankuai.nibscp.cpv.api.remote.response.GetBusinessPropertyResponse;
import com.sankuai.nibscp.cpv.api.remote.service.CpvQueryService;
import com.sankuai.qpro.ai.professor.application.model.operator.exception.execute.RpcExecuteException;
import com.sankuai.qpro.ai.professor.application.model.operator.probe.agent.conf.CPVModel;
import com.sankuai.qpro.ai.professor.application.utils.RetryExecuteUtils;
import deps.redis.clients.util.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/1/8
 */
@Service
public class CpvServiceProxy {

    @Autowired
    private CpvQueryService cpvQueryService;

    /**
     * 获取cpv Map，key:属性名，value:属性信息
     * @param productCategoryId 商品分类ID
     */
    public Map<String, CPVModel> getCpvInfo(Long productCategoryId) {
        // key:属性名，value:属性信息
        Map<String, CPVModel> cpvMap = Maps.newHashMap();

        // key:属性id，value:属性名
        Map<Long, String> propertyMap = Maps.newHashMap();

        GetBusinessPropertyResponse response = null;
        try {
            response = RetryExecuteUtils.retryExecute(() -> cpvQueryService.getBusinessPropertyDraftByPropertySource(buildRequest(productCategoryId)), 3, 200L);
        } catch (Exception e) {
            throw new RpcExecuteException(e);
        }
        if (response == null || !response.isSuccess() || response.getData() == null) {
            Cat.logError(String.format("getCpvInfo error, productCategoryId:%s", productCategoryId), new RuntimeException("getCpvInfo error"));
            return null;
        }
        BusinessPropertyListDTO propertyListDTO = response.getData();

        // 属性名
        if (CollectionUtils.isEmpty(propertyListDTO.getBusinessPropertyList())) {
            return cpvMap;
        }
        propertyListDTO.getBusinessPropertyList().forEach(property -> {
            if (!productCategoryId.equals(property.getBizCategoryId())) {
                return;
            }
            propertyMap.put(property.getMetaAttrId(), property.getAttrName());
            cpvMap.put(property.getAttrName(), CPVModel.from(productCategoryId, property.getAttrName(), property.getAttrCnName()));
        });

        // 属性value
        if (CollectionUtils.isEmpty(propertyListDTO.getDataTableList())) {
            return cpvMap;
        }
        propertyListDTO.getDataTableList().forEach(dto->{
            if (CollectionUtils.isEmpty(dto.getDataObjectList())) {
                return;
            }
            dto.getDataObjectList().forEach(dataObject -> {
                if (CollectionUtils.isEmpty(dataObject.getObjectAttrValueList())) {
                    return;
                }
                dataObject.getObjectAttrValueList().forEach(value -> {
                    if (propertyMap.containsKey(value.getMetaAttrId())) {
                        String propertyName = propertyMap.get(value.getMetaAttrId());
                        CPVModel cpvModel = cpvMap.computeIfAbsent(propertyName, k -> new CPVModel());
                        cpvModel.getValue().add(value.getAttrValue());
                    }
                });
            });
        });

        return cpvMap;
    }

    private GetBusinessPropertyDraftByPropertySourceRequest buildRequest(Long productCategoryId) {
        GetBusinessPropertyDraftByPropertySourceRequest request = new GetBusinessPropertyDraftByPropertySourceRequest();
        request.setBizLine(BizLineEnum.GENERAL.getCode());
        request.setBizApplication(BizApplicationEnum.SUPPLY_CHAIN.getCode());
        request.setBizCategoryId(productCategoryId);
        request.setPropertySourceList(Lists.newArrayList(PropertySourceEnum.PRODUCTION_OPERATION_ADDED.getCode(), PropertySourceEnum.PROBE_GENERATED.getCode()));
        return request;
    }
}

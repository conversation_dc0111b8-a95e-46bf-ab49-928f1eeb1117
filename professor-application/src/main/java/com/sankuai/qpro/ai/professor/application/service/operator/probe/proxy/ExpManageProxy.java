package com.sankuai.qpro.ai.professor.application.service.operator.probe.proxy;

import com.dianping.rhino.Rhino;
import com.dianping.rhino.threadpool.DefaultThreadPoolProperties;
import com.sankuai.qpro.ai.professor.api.enums.ConfigUnitEnum;
import com.sankuai.qpro.ai.professor.api.enums.ExpPlatformEnum;
import com.sankuai.qpro.ai.professor.application.model.operator.exception.execute.RpcExecuteException;
import com.sankuai.qpro.ai.professor.application.model.operator.probe.agent.exp.ExpCheckModel;
import com.sankuai.qpro.ai.professor.application.model.operator.probe.agent.exp.ExpInfoModel;
import com.sankuai.qpro.ai.professor.application.model.operator.probe.agent.exp.ExpModel;
import com.sankuai.qpro.ai.professor.application.model.operator.probe.agent.exp.ExpStrategyModel;
import com.sankuai.qpro.ai.professor.application.model.operator.probe.agent.exp.param.ProbeExpParam;
import com.sankuai.qpro.ai.professor.application.service.operator.probe.chat.ConversationStoreService;
import com.sankuai.qpro.ai.professor.application.utils.RetryExecuteUtils;
import com.sankuai.qpro.ai.professor.application.utils.SerializeUtils;
import com.sankuai.qpro.ai.professor.application.utils.TimeUtils;
import com.sankuai.spt.ark.api.dto.exp.ArkExpInfoDTO;
import com.sankuai.spt.ark.api.dto.exp.ArkExpStrategyDTO;
import com.sankuai.spt.ark.api.remote.request.exp.ArkQueryExpInfoRequest;
import com.sankuai.spt.ark.api.remote.response.exp.ArkQueryExpInfoResponse;
import com.sankuai.spt.ark.api.remote.service.exp.ArkExpInfoQueryService;
import com.sankuai.spt.ark.common.enums.exp.ArkExpSourceEnum;
import com.sankuai.spt.gray.api.dto.probe.ProbeStrategyUnitCheckResultDTO;
import com.sankuai.spt.gray.api.enums.probe.ProbeStrategyUnitPlatformEnum;
import com.sankuai.spt.gray.api.enums.probe.ProbeStrategyUnitSceneEnum;
import com.sankuai.spt.gray.api.request.probe.ProbeStrategyUnitCheckExpRequest;
import com.sankuai.spt.gray.api.response.probe.ProbeStrategyUnitCheckExpResponse;
import com.sankuai.spt.gray.api.service.ProbeStrategyUnitCheckService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * @author: wuwenqiang
 * @create: 2024-12-14
 * @description:
 */
@Service
@Slf4j
public class ExpManageProxy {

    @Autowired
    private ArkExpInfoQueryService arkExpInfoQueryService;

    @Autowired
    private ProbeStrategyUnitCheckService probeStrategyUnitCheckService;

    @Autowired
    private ConversationStoreService conversationStoreService;

    private static final ExecutorService QUERY_EXP_THREAD_POOL = Rhino.newThreadPool("queryExp", new DefaultThreadPoolProperties.Setter()
            .withCoreSize(10).withMaxQueueSize(100).withMaxSize(20)).getExecutor();

    private static final Long QUERY_EXP_TIMEOUT = 5000L;

    public ExpCheckModel checkExp(ProbeExpParam param) throws Exception {
        log.info("[ExpManageService] checkExp param={}", SerializeUtils.toJsonStr(param));
        if (param == null || CollectionUtils.isEmpty(param.getExpIdList())) {
            return buildEmptyParamExpCheckModel();
        }
        log.info("[ExpManageService] checkExp param={}", param);
        ProbeStrategyUnitCheckExpResponse checkResponse = null;
        try {
            checkResponse = probeStrategyUnitCheckService.checkExps(convertRequest(param));
        } catch (Exception e) {
            throw new RpcExecuteException(e);
        }
        if (!checkResponse.isSuccess()) {
            log.error("[ExpManageService] invoke probeStrategyUnitCheckService.checkExps failed, param={}, resp={}",
                    SerializeUtils.toJsonStr(param), SerializeUtils.toJsonStr(checkResponse));
            return ExpCheckModel.builder()
                    .checkResult(false).canSkip(false).checkErrorMsg(checkResponse.getErrorMessage())
                    .expInfoModel(null).expInfoModelList(Collections.emptyList())
                    .build();
        }
        List<ArkExpInfoDTO> exps = RetryExecuteUtils.retryExecute(() -> queryExps(param.getExpIdList()), 3, 200L);
        log.info("[ExpManageService] checkExp RPC invoke 斗斛实验信息 Response={}, exptIdList={}", SerializeUtils.toJsonStr(exps), param.getExpIdList());

        return convertExpCheckModel(checkResponse.getData(), exps);
    }

    private List<ArkExpInfoDTO> queryExps(List<String> expIds) {
        List<Future<ArkExpInfoDTO>> expFutures = expIds.stream().map(expId -> QUERY_EXP_THREAD_POOL.submit(() ->
                arkExpInfoQueryService.queryExpInfo(buildQueryRequest(expId)).getData())
        ).collect(Collectors.toList());

        return expFutures.stream().map(future -> {
            try {
                return future.get(QUERY_EXP_TIMEOUT, TimeUnit.MILLISECONDS);
            } catch (Exception e) {
                log.error("[ExpManageService] queryExps failed", e);
                throw new RpcExecuteException(e);
            }
        }).collect(Collectors.toList());
    }

    private ExpCheckModel convertExpCheckModel(ProbeStrategyUnitCheckResultDTO src, List<ArkExpInfoDTO> exps) {
        ExpCheckModel tar = new ExpCheckModel();
        tar.setCheckResult(src.getPass());
        tar.setCanSkip(src.getCanSkip());
        tar.setCheckErrorMsg(String.join("\n", src.getReasons()));
        tar.setExpInfoModel(convert(exps.get(0)));
        tar.setExpInfoModelList(exps.stream().map(this::convert).collect(Collectors.toList()));
        return tar;
    }

    private ExpModel convert(ArkExpInfoDTO src) {
        if (src == null) {
            return null;
        }
        ExpModel tar = new ExpModel();
        tar.setExpId(src.getExpId());
        tar.setExpName(src.getExpName());
        tar.setStartTime(getFormattedTime(src.getStartTime()));
        tar.setEndTime(getFormattedTime(src.getEndTime()));
        tar.setExpStrategyModels(src.getStrategyList().stream().map(this::convert).collect(Collectors.toList()));
        return tar;
    }

    private String getFormattedTime(Long src) {
        return DateFormatUtils.format(src, "yyyy-MM-dd HH:mm:ss");
    }

    private ExpStrategyModel convert(ArkExpStrategyDTO src) {
        ExpStrategyModel tar = new ExpStrategyModel();
        tar.setExpStrategyId(src.getStrategyKey());
        tar.setExpStrategyName(src.getStrategyName());
        tar.setExpStrategyShortName(convertShortName(src.getStrategyKey()));
        tar.setStrategyType(convertStrategyType(src.getStrategyType()));
        tar.setRatio(src.getRatio());
        return tar;
    }

    private String convertShortName(String strategyName) {
        if (strategyName == null) {
            return null;
        }
        String result = strategyName;
        if (strategyName.matches("EXP\\d+_.*")) {
            result = strategyName.substring(strategyName.lastIndexOf('_') + 1);
        }
        return result;
    }

    private String convertStrategyType(Integer arkStrategyType) {
        return String.valueOf(arkStrategyType);
    }

    private ArkQueryExpInfoRequest buildQueryRequest(String expId) {
        ArkQueryExpInfoRequest tar = new ArkQueryExpInfoRequest();
        tar.setExpSource(ArkExpSourceEnum.DOUHU.getCode());
        tar.setExpId(expId);
        return tar;
    }

    private ProbeStrategyUnitCheckExpRequest convertRequest(ProbeExpParam param) {
        ProbeStrategyUnitCheckExpRequest request = new ProbeStrategyUnitCheckExpRequest();
        request.setExpSource(ArkExpSourceEnum.DOUHU.getCode());
        request.setExpIds(param.getExpIdList());

        request.setOperatorMis(conversationStoreService.queryUserId(param.getConversationId()));
        if (ConfigUnitEnum.SHELF_SUBTITLE.getCode().equals(param.getConfigUnit())) {
            request.setScene(ProbeStrategyUnitSceneEnum.SHELF_SUBTITLE.getCode());
        }
        return request;
    }
    /**
     * 获取实验详情
     *
     * @param expId 实验ID
     * @return
     */
    public ExpInfoModel getExpInfo(String expId) {
        if (StringUtils.isBlank(expId)) {
            return null;
        }
        ArkQueryExpInfoResponse queryResponse = null;
        try {
            queryResponse = RetryExecuteUtils.retryExecute(() -> arkExpInfoQueryService.queryExpInfo(buildQueryRequest(expId)), 3, 200L);
        } catch (Exception e) {
            throw new RpcExecuteException(e);
        }
        if (!queryResponse.isSuccess()) {
            return null;
        }
        return convertExpInfoModel(queryResponse.getData());
    }

    private ExpInfoModel convertExpInfoModel(ArkExpInfoDTO src) {
        ExpInfoModel tar = new ExpInfoModel();
        tar.setExpId(src.getExpId());
        tar.setExpName(src.getExpName());
        tar.setPlatform(convertPlatform(src.getPlatform()));
        tar.setExpStatus(src.getExpStatus());
        tar.setStartTime(TimeUtils.convertMillSecondToLocalDateTime(src.getStartTime()));
        tar.setEndTime(TimeUtils.convertMillSecondToLocalDateTime(src.getEndTime()));
        tar.setExpStrategyModels(src.getStrategyList().stream().map(this::convert).collect(Collectors.toList()));
        return tar;
    }

    private ExpCheckModel buildEmptyParamExpCheckModel() {
        ExpCheckModel tar = new ExpCheckModel();
        tar.setCheckResult(false);
        tar.setCanSkip(false);
        tar.setCheckErrorMsg("没有检测到实验号");
        return tar;
    }

    private Integer convertPlatform(Integer arkPlatform) {
        if (arkPlatform == null) {
            return null;
        }
        if (ExpPlatformEnum.DP.getType() == arkPlatform.intValue()) {
            return ProbeStrategyUnitPlatformEnum.DP.getCode();
        } else if (ExpPlatformEnum.MT.getType() == arkPlatform.intValue()) {
            return ProbeStrategyUnitPlatformEnum.MT.getCode();
        }
        return null;
    }
}

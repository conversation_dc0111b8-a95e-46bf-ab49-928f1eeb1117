package com.sankuai.qpro.ai.professor.application.service.operator.probe.proxy;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.dzviewscene.unifiedshelf.operator.api.dto.OperatorShelfCategoryDTO;
import com.sankuai.dzviewscene.unifiedshelf.operator.api.dto.OperatorShelfConfigDTO;
import com.sankuai.dzviewscene.unifiedshelf.operator.api.enums.OperatorShelfConfigStrategyUnit;
import com.sankuai.dzviewscene.unifiedshelf.operator.api.res.CommonResponse;
import com.sankuai.dzviewscene.unifiedshelf.operator.api.service.UnifiedShelfOperatorMService;
import com.sankuai.qpro.ai.professor.application.model.operator.exception.execute.RpcExecuteException;
import com.sankuai.qpro.ai.professor.application.utils.RetryExecuteUtils;
import com.sankuai.qpro.ai.professor.application.utils.SerializeUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * @author: wuwenqiang
 * @create: 2025-01-10
 * @description:
 */
@Service
@Slf4j
public class ShelfOperatorMServiceProxy {

    @Autowired
    private UnifiedShelfOperatorMService unifiedShelfOperatorMService;

    public Map<OperatorShelfCategoryDTO, List<OperatorShelfConfigDTO>> getUsedShelfOperatorConfig(List<Integer> poiCategoryIdList, Long productCategoryId, OperatorShelfConfigStrategyUnit unit) {
        try {
            CommonResponse<Map<OperatorShelfCategoryDTO, List<OperatorShelfConfigDTO>>> response = RetryExecuteUtils.retryExecute(() -> unifiedShelfOperatorMService.getUsedShelfOperatorConfig(poiCategoryIdList, Lists.newArrayList(productCategoryId), unit),
                    3, 200L);
            if (response == null || response.getCode() != 200) {
                throw new RpcExecuteException(response.getMsg());
            }
            log.info("[ShelfOperatorMServiceProxy]getUsedShelfOperatorConfig查询货架线上配置PRC response={}, poiCategoryIdList={}, productCategoryId={}, unit={}"
                    , SerializeUtils.toJsonStr(response), poiCategoryIdList, productCategoryId, unit);
            return response.getData();
        } catch (Exception e) {
            log.error("[UnifiedShelfOperatorMServiceProxy] getShelfConfig error, shopBackCategories:{}, productCategories:{}, unit:{}", poiCategoryIdList, productCategoryId, unit, e);
            throw new RpcExecuteException(e);
        }
    }

    public static Map<OperatorShelfCategoryDTO, OperatorShelfConfigDTO> extractNonExpConfig(Map<OperatorShelfCategoryDTO, List<OperatorShelfConfigDTO>> configMap) {
        if (MapUtils.isEmpty(configMap)) {
            return null;
        }
        Map<OperatorShelfCategoryDTO, OperatorShelfConfigDTO> result = Maps.newHashMap();
        configMap.forEach((category, configList) -> {
            OperatorShelfConfigDTO nonExpConfig = extractNonExpConfig(configList);
            if (nonExpConfig != null) {
                result.put(category, nonExpConfig);
            }
        });
        return result;
    }

    public static boolean hasExp(List<OperatorShelfConfigDTO> configDTOList) {
        if (CollectionUtils.isEmpty(configDTOList)) {
            return false;
        }
        return configDTOList.stream().anyMatch(config -> hasExp(config));
    }

    public static OperatorShelfConfigDTO extractNonExpConfig(List<OperatorShelfConfigDTO> configDTOList) {
        if (CollectionUtils.isEmpty(configDTOList)) {
            return null;
        }
        return configDTOList.stream()
                .filter(Objects::nonNull)
                .filter(configDTO -> !hasExp(configDTO)).findFirst().orElse(null);
    }

    public static OperatorShelfConfigDTO extractExpConfig(List<OperatorShelfConfigDTO> configDTOList) {
        if (CollectionUtils.isEmpty(configDTOList)) {
            return null;
        }
        return configDTOList.stream()
                .filter(Objects::nonNull)
                .filter(configDTO -> hasExp(configDTO)).findFirst().orElse(null);
    }

    public static boolean hasExp(OperatorShelfConfigDTO configDTO) {
        if (configDTO == null) {
            return false;
        }
        return StringUtils.isNotBlank(configDTO.getMtExperimentId()) || StringUtils.isNotBlank(configDTO.getDpExperimentId());
    }
}

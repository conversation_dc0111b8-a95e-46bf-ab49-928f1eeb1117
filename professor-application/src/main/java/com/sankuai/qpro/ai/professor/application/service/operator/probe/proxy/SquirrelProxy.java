package com.sankuai.qpro.ai.professor.application.service.operator.probe.proxy;

import com.dianping.squirrel.asyncclient.core.SquirrelClient;
import com.dianping.squirrel.asyncclient.core.SquirrelFuture;
import com.dianping.squirrel.asyncclient.core.StoreKey;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;


/**
 * @author: wuwenqiang
 * @create: 2024-12-12
 * @description:
 */
@Service
public class SquirrelProxy {

    @Autowired
    @Qualifier("redisClient0")
    private SquirrelClient squirrelClient;

    public <T> T syncGet(String category, String... key) {
        StoreKey storeKey = new StoreKey(category, key);
        return squirrelClient.sync().get(storeKey);
    }

    public <T> SquirrelFuture<T> asyncGet(String category, String... key) {
        StoreKey storeKey = new StoreKey(category, key);
        return squirrelClient.async().get(storeKey);
    }

    public Boolean syncSet(String category, Object value, String... key) {
        StoreKey storeKey = new StoreKey(category, key);
        return squirrelClient.sync().set(storeKey, value);
    }

    public SquirrelFuture<Boolean> asyncSet(String category, Object value, String... key) {
        StoreKey storeKey = new StoreKey(category, key);
        return squirrelClient.async().set(storeKey, value);
    }
}

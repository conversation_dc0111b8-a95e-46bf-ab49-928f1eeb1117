package com.sankuai.qpro.ai.professor.application.utils;

import org.apache.commons.lang.StringUtils;

/**
 * 描述：代码块处理工具类
 *
 * <AUTHOR>
 * @since 2025/8/10
 */
public class CodeBlockUtils {
    public static String getCodeBlock(String sourceCode, Integer startLine, Integer endLine) {
        if (StringUtils.isBlank(sourceCode)) {
            return "";
        }
        // 按行分割
        String[] lines = sourceCode.split("\\R"); // \\R 匹配所有换行符
        // 校验参数
        if (startLine < 1 || endLine > lines.length || startLine > endLine) {
            throw new IllegalArgumentException("行号不合法");
        }
        // 提取指定行
        StringBuilder sb = new StringBuilder();
        for (int i = startLine - 1; i < endLine; i++) {
            sb.append(lines[i]);
            if (i < endLine - 1) sb.append(System.lineSeparator());
        }
        return sb.toString();
    }
}

package com.sankuai.qpro.ai.professor.application.utils;

import lombok.extern.slf4j.Slf4j;

/**
 * @author: wu<PERSON><PERSON><PERSON>
 * @create: 2025-01-02
 * @description: copy对象必须实现Serializable接口
 */
@Slf4j
public class DeepCopyUtils {

    public static <T> T deepCopy(T obj) {
        if (obj == null) {
            return null;
        }
        try {
            String jsonStr = SerializeUtils.toJsonStr(obj);
            return SerializeUtils.toObj(jsonStr, (Class<T>) obj.getClass());
        } catch (Exception e) {
            log.error("[DeepCopyUtils] deepCopy error, obj={}", SerializeUtils.toJsonStr(obj), e);
            return null;
        }
    }
}

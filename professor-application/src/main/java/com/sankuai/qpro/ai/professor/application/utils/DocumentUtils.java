package com.sankuai.qpro.ai.professor.application.utils;

import com.fasterxml.jackson.databind.JsonNode;
import com.google.common.collect.Lists;
import com.sankuai.qpro.ai.professor.application.model.codegenerate.KmJsonData;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;

import java.util.List;
import java.util.Map;

/**
 * @author: wu<PERSON><PERSON><PERSON>
 * @create: 2025-05-22
 * @description: 文档工具类
 */
@Slf4j
public class DocumentUtils {

    /**
     * 过滤出特定场景下的文档
     * @param node 文档json节点
     * @param scene 场景：货架/团详
     * @return
     */
    public static KmJsonData filterForSpecialScene(KmJsonData node, String scene) {
        if (node == null || !"doc".equals(node.getType()) || CollectionUtils.isEmpty(node.getContent())) {
            return null;
        }
        List<KmJsonData> content = node.getContent();
        KmJsonData result = new KmJsonData();
        result.setType("doc");
        List<KmJsonData> resultContent = Lists.newArrayList();
        boolean startRecord = false;
        int startLevel = -1;
        for (KmJsonData child : content) {
            if (child == null) {
                continue;
            }
            // 添加标题
            if ("title".equals(child.getType())) {
                resultContent.add(child);
                continue;
            }
            if ("heading".equals(child.getType())) {
                String headingText = extractHeadingText(child);
                int level = computeHeadingLevel(child);
                // 如果标题包含场景则标记
                if (StringUtils.isNotBlank(headingText) && headingText.contains(scene)) {
                    startRecord = true;
                    startLevel = level;
                } else if (startRecord == true && level > startLevel) {
                    // 如果是被标记标题的子标题，会继续保持startRecord的标识
                } else {
                    startRecord = false;
                    startLevel = -1;
                }
            }
            if (startRecord) {
                resultContent.add(child);
            }
        }
        result.setContent(resultContent);
        return result;
    }

    private static String extractHeadingText(KmJsonData node) {
        if (node == null || !"heading".equals(node.getType()) || CollectionUtils.isEmpty(node.getContent())) {
            return null;
        }
        StringBuilder sb = new StringBuilder();
        processTextListNode(sb, node.getContent(), "");
        return sb.toString();
    }

    /**
     * 提取文档所有文本
     * @param node 文档节点
     * @return
     */
    public static String extractAllText(KmJsonData node) {
        if (node == null) {
            return null;
        }
        StringBuilder sb = new StringBuilder();
        // 主标题设置
        if ("title".equals(node.getType()) && CollectionUtils.isNotEmpty(node.getContent())) {
            sb.append("Title: ");
        }
        // 多级标题设置
        if ("heading".equals(node.getType())) {
            sb.append(convertHeadingPrefix(node));
        }
        // 表格设置
        if ("table".equals(node.getType())) {
            processHtmlTableNode(sb, node);
            return sb.toString();
        }
        // 特殊叶子节点
        if (isSpecialLeafNode(node)) {
            processSpecialLeafNode(sb, node, "\n\n");
            return sb.toString();
        }
        if (CollectionUtils.isEmpty(node.getContent())) {
            return sb.toString();
        }
        // 子节点是文本则特殊处理
        if (isChildTextNode(node)) {
            processTextListNode(sb, node.getContent(), "\n\n");
            return sb.toString();
        }
        for (KmJsonData child : node.getContent()) {
            sb.append(extractAllText(child));
        }
        return sb.toString();
    }

    private static String convertHeadingPrefix(KmJsonData node) {
        int level = computeHeadingLevel(node);
        if (level <= 0) {
            return StringUtils.EMPTY;
        }
        return "#".repeat(level) + " ";
    }

    private static int computeHeadingLevel(KmJsonData node) {
        if (node == null || !"heading".equals(node.getType()) || MapUtils.isEmpty(node.getAttrs())) {
            return 0;
        }
        Map<String, Object> attrs = node.getAttrs();
        return attrs.get("level") == null ? 0 : (int) attrs.get("level");
    }

    private static void processTableNode(StringBuilder sb, KmJsonData node) {
        if (node == null || !"table".equals(node.getType())) {
            return;
        }
        List<KmJsonData> rows = node.getContent();
        if (CollectionUtils.isEmpty(rows)) {
            return;
        }
        for (KmJsonData row : rows) {
            if (row == null || !"table_row".equals(row.getType()) || CollectionUtils.isEmpty(row.getContent())) {
                continue;
            }
            List<KmJsonData> cells = row.getContent();
            boolean isHeaderRow = "table_header".equals(cells.get(0).getType());
            for (KmJsonData cell : cells) {
                String cellContent = extractCellContent(cell);
                sb.append("| ").append(cellContent).append(" ");
            }
            sb.append("|\n");
            // 表头设置
            if (isHeaderRow) {
                for (int i = 0; i < cells.size(); i++) {
                    sb.append("| --- ");
                }
                sb.append("|\n");
            }
        }
        sb.append("\n\n");
    }

    private static void processHtmlTableNode(StringBuilder sb, KmJsonData node) {
        if (node == null || !"table".equals(node.getType())) {
            return;
        }
        List<KmJsonData> rows = node.getContent();
        if (CollectionUtils.isEmpty(rows)) {
            return;
        }

        sb.append("<table>");
        for (KmJsonData row : rows) {
            if (row == null || !"table_row".equals(row.getType()) || CollectionUtils.isEmpty(row.getContent())) {
                continue;
            }

            sb.append("<tr>");
            List<KmJsonData> cells = row.getContent();
            boolean isHeaderRow = "table_header".equals(cells.get(0).getType());

            for (KmJsonData cell : cells) {
                String tag = isHeaderRow ? "th" : "td";
                String cellSpan = extractCellSpan(cell);
                sb.append("<").append(tag).append(cellSpan).append(">");
                String cellContent = extractCellContent(cell);
                sb.append(cellContent);
                sb.append("</").append(tag).append(">");
            }
            sb.append("</tr>");
        }
        sb.append("</table>\n\n");
    }

    private static String extractCellSpan(KmJsonData cell) {
        if (cell == null || MapUtils.isEmpty(cell.getAttrs())) {
            return null;
        }
        Map<String, Object> attrs = cell.getAttrs();
        String rowSpan = attrs.get("rowspan") == null ? null : String.valueOf(attrs.get("rowspan"));
        String colSpan = attrs.get("colspan") == null ? null :  String.valueOf(attrs.get("colspan"));
        StringBuilder sb = new StringBuilder();
        if (StringUtils.isNotBlank(rowSpan) && !"1".equals(rowSpan)) {
            sb.append(String.format(" rowspan=\"%s\" ", rowSpan));
        }
        if (StringUtils.isNotBlank(colSpan) && !"1".equals(colSpan)) {
            sb.append(String.format(" colspan=\"%s\" ", colSpan));
        }
        return sb.toString();
    }

    private static String extractCellContent(KmJsonData node) {
        if (node == null) {
            return null;
        }
        StringBuilder sb = new StringBuilder();
        // 表格设置
        if ("table".equals(node.getType())) {
            // 使用html的表格表示嵌套表格
            processHtmlTableNode(sb, node);
            return sb.toString();
        }
        // 特殊叶子节点
        if (isSpecialLeafNode(node)) {
            processSpecialLeafNode(sb, node, "<br>");
            return sb.toString();
        }
        if (CollectionUtils.isEmpty(node.getContent())) {
            return sb.toString();
        }
        // 子节点是文本则特殊处理
        if (isChildTextNode(node)) {
            processTextListNode(sb, node.getContent(), "<br>");
            return sb.toString();
        }
        for (KmJsonData child : node.getContent()) {
            sb.append(extractCellContent(child));
        }
        return sb.toString();
    }

    private static boolean isSpecialLeafNode(KmJsonData node) {
        // 文本
        if ("text".equals(node.getType()) && StringUtils.isNotBlank(node.getText())) {
            return true;
        }
        // 外部链接
        if ("open_link".equals(node.getType()) || "link".equals(node.getType())) {
            return true;
        }
        // 图像
        if ("image".equals(node.getType())) {
            return true;
        }
        return false;
    }

    private static void processSpecialLeafNode(StringBuilder sb, KmJsonData node, String endLine) {
        // 文本设置
        if ("text".equals(node.getType()) && StringUtils.isNotBlank(node.getText())) {
            sb.append(node.getText());
            sb.append(endLine);
        }
        // 外部链接设置
        if ("open_link".equals(node.getType()) || "link".equals(node.getType())) {
            sb.append(extractLinkContent(node));
            sb.append(endLine);
        }
        // 图像链接设置
        if ("image".equals(node.getType())) {
            sb.append(extractImageContent(node));
            sb.append(endLine);
        }
    }

    private static boolean isChildTextNode(KmJsonData node) {
        if (node == null || CollectionUtils.isEmpty(node.getContent())) {
            return false;
        }
        return node.getContent().stream().anyMatch(child -> "text".equals(child.getType()));
    }

    private static void processTextListNode(StringBuilder sb, List<KmJsonData> nodes, String endLine) {
        if (CollectionUtils.isEmpty(nodes)) {
            return;
        }
        // 处理文本或跳链
        for (KmJsonData node : nodes) {
            if ("text".equals(node.getType())) {
                sb.append(node.getText());
            }
            if ("open_link".equals(node.getType()) || "link".equals(node.getType())) {
                sb.append(extractLinkContent(node));
            }
        }
        sb.append(endLine);
    }

    private static String extractLinkContent(KmJsonData node) {
        if (node == null) {
            return null;
        }
        Map<String, Object> attrs = node.getAttrs();
        if (MapUtils.isEmpty(attrs)) {
            return null;
        }
        String title = attrs.get("title") == null ? null : attrs.get("title").toString();
        String href = attrs.get("href") == null ? null : attrs.get("href").toString();
        if (StringUtils.isBlank(href)) {
            return null;
        }
        if (href.equals(title)) {
            return href;
        }
        return String.format("[%s](%s)", title, href);
    }

    private static String extractImageContent(KmJsonData node) {
        if (node == null) {
            return null;
        }
        Map<String, Object> attrs = node.getAttrs();
        if (MapUtils.isEmpty(attrs)) {
            return null;
        }
        String imageUrl = attrs.get("src") == null ? null : attrs.get("src").toString();
        if (StringUtils.isBlank(imageUrl)) {
            return null;
        }
        return String.format("<img src=%s> ", imageUrl);
    }
}

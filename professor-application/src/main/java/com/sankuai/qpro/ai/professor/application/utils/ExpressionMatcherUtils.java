package com.sankuai.qpro.ai.professor.application.utils;
import java.util.regex.Pattern;

public class ExpressionMatcherUtils {
    public static boolean matcher(String str, String pattern) {
        String regex = Pattern.quote(pattern);
        regex = regex.replace("%", "\\E.*\\Q")
                .replace("_", "\\E.\\Q");
        regex = regex.replaceAll("\\\\Q\\\\E", "");
        return str.matches(regex);
    }
}


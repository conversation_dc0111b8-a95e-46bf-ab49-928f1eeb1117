package com.sankuai.qpro.ai.professor.application.utils;

import com.jcraft.jsch.JSch;
import com.jcraft.jsch.JSchException;
import com.jcraft.jsch.Session;
import com.meituan.service.inf.kms.client.Kms;
import com.meituan.service.inf.kms.utils.KmsResultNullException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.eclipse.jgit.api.*;
import org.eclipse.jgit.api.errors.GitAPIException;
import org.eclipse.jgit.diff.DiffEntry;
import org.eclipse.jgit.diff.DiffFormatter;
import org.eclipse.jgit.lib.ObjectId;
import org.eclipse.jgit.lib.ObjectReader;
import org.eclipse.jgit.lib.Repository;
import org.eclipse.jgit.revwalk.RevCommit;
import org.eclipse.jgit.storage.file.FileRepositoryBuilder;
import org.eclipse.jgit.transport.*;
import org.eclipse.jgit.treewalk.CanonicalTreeParser;
import org.eclipse.jgit.treewalk.FileTreeIterator;
import org.eclipse.jgit.treewalk.filter.PathFilter;
import org.eclipse.jgit.util.FS;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Slf4j
public class GitWrapper {

    // 设置ssh
    public static final TransportConfigCallback transportConfigCallback = new TransportConfigCallback() {
        private final SshSessionFactory sshSessionFactory = new JschConfigSessionFactory() {
            @Override
            protected void configure(OpenSshConfig.Host host, Session session) {
                session.setConfig("StrictHostKeyChecking", "no");
            }

            @Override
            protected JSch createDefaultJSch(FS fs) throws JSchException {
                String prvkey = null;
                try {
                    prvkey = Kms.getByName("com.sankuai.mdp.automend.service", "git.ras.private.key");
                } catch (KmsResultNullException e) {
                    throw new RuntimeException("获取Kms私钥异常", e);
                }

                JSch sch = super.createDefaultJSch(fs);
                sch.addIdentity("id_rsa", prvkey.getBytes(), null, null);
                return sch;
            }
        };

        @Override
        public void configure(Transport transport) {
            SshTransport sshTransport = (SshTransport) transport;
            sshTransport.setSshSessionFactory(sshSessionFactory);
        }
    };


    public static List<String> metaOfGitUrl(String gitUrl) {
        Pattern pattern = Pattern.compile("ssh://[^/]+/([^/]+)/(.*)\\.git");
        Matcher matcher = pattern.matcher(gitUrl);
        if (matcher.find()) {
            return Arrays.asList(matcher.group(1), matcher.group(2));
        } else {
            return null;
        }
    }

    public static String clone(String git, String branch, String path) throws GitAPIException, IOException {
        String[] gitSplits = git.split("/");
        String project = gitSplits[gitSplits.length - 2];
        String repo = gitSplits[gitSplits.length - 1].split(".git")[0];
        Path dir = Paths.get("/opt/meituan/appdatas/" + path + "/");
        if (!Files.exists(dir)) {
            Files.createDirectories(dir);
        }
        String workspace = String.format("%s/%s-%s-%s", dir.toAbsolutePath(), project, repo, System.currentTimeMillis());
//        String workspace = String.format("%s/%s-%s", dir.toAbsolutePath(), project, repo);
        cloneBranch(git, branch, workspace);
        return workspace;
    }

    public static void push(String projectDir) {
        try (Git git = Git.open(new File(projectDir))) {
            git.pull().setTransportConfigCallback(transportConfigCallback).call();
            git.push().setTransportConfigCallback(transportConfigCallback)
                    .setForce(false).call();
        } catch (Exception ex) {
            throw new RuntimeException(ex);
        }
    }

    public static void commit(String projectDir, List<String> files, String message) {
        try (Git git = Git.open(new File(projectDir))) {
            AddCommand addCommand = git.add();
            files.forEach(file -> {
                addCommand.addFilepattern(file.replace(projectDir, "").substring(1));
            });
            addCommand.call();
            git.commit().setMessage(message).setAuthor("MDP", "<EMAIL>").call();
            git.push().setTransportConfigCallback(transportConfigCallback)
                    .setForce(false).call();
        } catch (Exception ex) {
            throw new RuntimeException(ex);
        }
    }

    public static String checkout(String repository, String originBranch, String targetBranch, String path) {
        String projectDir = "";
        boolean success = false;
        try {
            projectDir = clone(repository, originBranch, path);
            Git git = Git.open(new File(projectDir));
            createBranch(git, targetBranch);
            success = pushBranch(git) && branchExists(git, targetBranch);
            if (success) {
                return projectDir;
            }
        } catch (GitAPIException | IOException e) {
            log.error("创建分支失败，仓库: {}, 原始分支: {}, 目标分支: {}", repository, originBranch, targetBranch, e);
            return null;
        } finally {
            if (!success) {
                log.info("delete file, projectDir={}", projectDir);
                try {
                    FileUtils.deleteDirectory(new File(projectDir));
                } catch (IOException e) {
                    log.error("delete file error, file={}", projectDir, e);
                }
            }
        }
        return null;
    }

    public static boolean checkHasPlugin(String repository, String originBranch, String keyword) {
        String projectDir = "";
        try {
            projectDir = clone(repository, originBranch, "preUpgrade");
            Git.open(new File(projectDir));
            File pomFile = new File(projectDir, "pom.xml");
            if (pomFile.exists() && pomFile.isFile()) {
                if (hasAthenaBootLoaderPlugin(pomFile, keyword)) {
                    log.info("The pom.xml contains {} plugin.", keyword);
                    return true;
                } else {
                    log.info("The pom.xml does not contain {} plugin.", keyword);
                    return false;
                }
            } else {
                log.warn("pom.xml file not found in the project directory.");
                return false;
            }
        } catch (GitAPIException | IOException e) {
            log.error("[checkHasPlugin]失败，仓库: {}, 分支: {}, keyword: {}", repository, originBranch, keyword, e);
            return false;
        } finally {
            if (StringUtils.isNotBlank(projectDir)) {
                log.info("[checkHasPlugin] delete file, projectDir={}", projectDir);
                try {
                    FileUtils.deleteDirectory(new File(projectDir));
                } catch (IOException e) {
                    log.error("[checkHasPlugin]delete file error, file={}", projectDir, e);
                }
            }
        }
    }

    private static boolean hasAthenaBootLoaderPlugin(File pomFile, String keyword) {
        try {
            String content = new String(Files.readAllBytes(Paths.get(pomFile.getAbsolutePath())));
            log.info("content");
            return content.contains(keyword);
        } catch (IOException e) {
            log.error("Error reading pom.xml file", e);
            return false;
        }
    }

    private static boolean branchExists(Git git, String branchName) throws GitAPIException {
        return git.branchList().call().stream().anyMatch(r -> r.getName().endsWith(branchName));
    }

    private static void createBranch(Git git, String branchName) throws GitAPIException {
        git.checkout().setCreateBranch(true)
                .setName(branchName)
                .setUpstreamMode(CreateBranchCommand.SetupUpstreamMode.TRACK)
                .setForced(true).call();
    }

    private static void checkoutBranch(Git git, String branchName) throws GitAPIException {
        git.checkout()
                .setCreateBranch(true)
                .setName(branchName)
                .setUpstreamMode(CreateBranchCommand.SetupUpstreamMode.TRACK)
                .setStartPoint("origin/" + branchName)
                .call();
    }

    private static boolean pushBranch(Git git) throws GitAPIException {
        boolean pushStatus = true;
        Iterable<PushResult> pushResults = git.push().setTransportConfigCallback(transportConfigCallback)
                .setForce(false)
                .call();
        for (PushResult pushResult : pushResults) {
            String message = pushResult.getMessages();
            if (message.contains("error")) {
                pushStatus = false;
                break;
            }
        }
        return pushStatus;
    }

    public static String getLatestCommitId(String projectDir) {
        try (Git git = Git.open(new File(projectDir))) {
            // 获取最新的提交
            Iterable<RevCommit> commits = git.log().setMaxCount(1).call();
            for (RevCommit commit : commits) {
                // 获取提交 ID
                ObjectId commitId = commit.getId();
                return commitId.getName();
            }
        } catch (Exception e) {
            log.error("git get latest commit error", e);
        }
        return "";
    }

    private static void cloneBranch(String git, String branch, String projectPath) throws GitAPIException {
        Git.cloneRepository()
                .setTransportConfigCallback(transportConfigCallback)
                .setURI(git)
                .setBranch(branch)
                .setBranchesToClone(Collections.singletonList("refs/heads/" + branch))
                .setDirectory(new File(projectPath))
                .call()
                .close();
    }

    private static void reset(String branch, String repoPath) throws IOException, GitAPIException {
        try (Repository repository = FileRepositoryBuilder.create(new File(repoPath, ".git"))) {
            try (Git git = new Git(repository)) {
                // 1. 拉取远程最新元数据
                git.fetch()
                        .setTransportConfigCallback(transportConfigCallback)
                        .setRemote("origin")
                        .setForceUpdate(true)
                        .call();

                // 2. 获取远程分支引用（例如 origin/main）
                String remoteBranchRef = "refs/remotes/origin/" + branch;

                // 3. 硬重置到远程分支最新提交
                git.reset()
                        .setMode(ResetCommand.ResetType.HARD)
                        .setRef(remoteBranchRef)
                        .call();

                // 4. 清理未跟踪文件（包括.gitignore忽略的）
                git.clean()
                        .setCleanDirectories(true)
                        .setForce(true)
                        // 包含.gitignore忽略的文件
                        .setIgnore(false)
                        .call();

                log.info("强制同步完成，本地分支已与 origin/{} 完全一致", branch);
            }
        }
    }


    public static void cloneAndSync(String git, String branch, String projectPath) throws GitAPIException, IOException {
        int i = projectPath.lastIndexOf("/");

        String workspace = projectPath.substring(0, i);
        Path dir = Paths.get(workspace);
        if (!Files.exists(dir)) {
            Files.createDirectories(dir);
        }

        Path repoDir = Paths.get(projectPath);

        // 如果已存在
        if (Files.exists(repoDir)) {
            try {
                reset(branch, projectPath);
                return;
            } catch (Exception e) {
                log.error("强制同步异常，本地分支为 origin/{}", branch, e);
                FileUtils.deleteDirectory(repoDir.toFile());
            }
        }

        // clone代码
        cloneBranch(git, branch, projectPath);
    }

    public static void commitAndPush(String projectPath, String message) throws IOException, GitAPIException {
        try (Repository repository = FileRepositoryBuilder.create(new File(projectPath, ".git"))) {
            try (Git git = new Git(repository)) {
                // 1. 添加所有文件到暂存区
                git.add().addFilepattern(".").call();
                // 处理删除的文件
                git.add().setUpdate(true).addFilepattern(".").call();

                // 2. 提交更改
                git.commit()
                        .setMessage(message)
                        // 设置作者信息
                        .setAuthor("MDP", "<EMAIL>")
                        .call();

                // 3. 推送到远程仓库
                Iterable<PushResult> pushResults = git.push()
                        .setTransportConfigCallback(transportConfigCallback)
                        .setForce(false)
                        .call();

                // 检查推送结果
                for (PushResult result : pushResults) {
                    // 分支名
                    RemoteRefUpdate update = result.getRemoteUpdate("refs/heads/" + repository.getBranch());
                    if (update.getStatus() == RemoteRefUpdate.Status.OK) {
                        log.info("推送成功: {}", projectPath);
                    } else {
                        log.info("推送失败: {}, msg: {}", projectPath, update.getMessage());
                    }
                }
            }
        }
    }

    public static String getWorkingDiff(String projectDir, String absoluteFilePath) {
        try (Git git = Git.open(Paths.get(projectDir).toFile())) {
            Repository repo = git.getRepository();

            // 转换绝对路径为相对仓库的路径
            Path projectPath = Paths.get(projectDir).toAbsolutePath();
            Path filePath = Paths.get(absoluteFilePath).toAbsolutePath();
            String relativePath = projectPath.relativize(filePath).toString().replace(File.separatorChar, '/');

            // 解析HEAD的树对象
            ObjectId headTreeId = repo.resolve("HEAD^{tree}");
            if (headTreeId == null) {
                return ""; // 初始提交时无HEAD的情况
            }

            // 准备新旧树比较器
            try (ObjectReader reader = repo.newObjectReader()) {
                // 旧树：HEAD提交
                CanonicalTreeParser oldTree = new CanonicalTreeParser();
                oldTree.reset(reader, headTreeId);

                // 新树：工作目录
                FileTreeIterator newTree = new FileTreeIterator(repo);

                // 使用DiffFormatter生成差异
                ByteArrayOutputStream diffOutput = new ByteArrayOutputStream();
                try (DiffFormatter formatter = new DiffFormatter(diffOutput)) {
                    formatter.setRepository(repo);
                    formatter.setPathFilter(PathFilter.create(relativePath));

                    // 扫描差异条目
                    for (DiffEntry entry : formatter.scan(oldTree, newTree)) {
                        formatter.format(entry);
                    }
                    return diffOutput.toString("UTF-8");
                }
            }
        } catch (Exception e) {

        }
        return "";
    }

}
package com.sankuai.qpro.ai.professor.application.utils;

import com.dianping.cat.Cat;
import com.google.common.collect.Lists;
import com.sankuai.dzviewscene.unifiedshelf.operator.api.utils.GroovyUtils;
import com.sankuai.dzviewscene.unifiedshelf.operator.api.utils.OperatorShelfConfigValidateUtil.DouHuM;
import com.sankuai.dzviewscene.unifiedshelf.operator.api.utils.OperatorShelfConfigValidateUtil.Param;
import com.sankuai.dzviewscene.unifiedshelf.operator.api.utils.OperatorShelfConfigValidateUtil.ProductM;
import com.sankuai.dzviewscene.unifiedshelf.operator.api.utils.OperatorShelfConfigValidateUtil.AttrM;
import groovy.lang.Binding;
import groovy.lang.GroovyShell;
import groovy.lang.Script;
import lombok.Builder;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.StringUtils;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutionException;

/**
 * @author: wuwenqiang
 * @create: 2025-02-15
 * @description:
 */
public class GroovyExecuteUtils {
    private static GroovyShell groovyShell = new GroovyShell();
    public static final String TEST = "test";
    public static final String DOU_HU_MOCK = "mock";
    public static final String DOU_HU_SUCCESS = "200";
    public static final String DOU_HU_SUCCESS_MSG = "成功";
    public static final String PARAM = "param";
    public static final String PRODUCT_M = "productM";
    public static final String DOU_HU_LIST = "douHuList";

    private static final String GROOVY_SHELL_IMPORT = "import com.sankuai.dzviewscene.unifiedshelf.operator.api.utils.OperatorShelfConfigValidateUtil.Param;\n" +
            "import com.sankuai.dzviewscene.unifiedshelf.operator.api.utils.OperatorShelfConfigValidateUtil.ProductM;\n" +
            "import com.sankuai.dzviewscene.unifiedshelf.operator.api.utils.OperatorShelfConfigValidateUtil.DouHuM;\n" +
            "import com.sankuai.dzviewscene.unifiedshelf.operator.api.utils.OperatorShelfConfigValidateUtil.StyleTextModel;\n" +
            "import com.sankuai.dzviewscene.unifiedshelf.operator.api.utils.OperatorShelfConfigValidateUtil.ItemSubTitleVO;\n" +
            "import com.sankuai.dzviewscene.unifiedshelf.operator.api.utils.OperatorShelfConfigValidateUtil.TextStyleEnum;\n";

    public static Object getGroovyResult(String groovyContent, List<AttrM> mockAttrs, String mockDouHuResult) {
        groovyContent = addImport(groovyContent);
        Param param = buildMockParam(mockAttrs, mockDouHuResult);
        Map<String, Object> params = new HashMap<>();
        params.put(PARAM, param);
        // 兼容处理
        params.put(PRODUCT_M, param.getProductM());
        params.put(DOU_HU_LIST, param.getDouHuList());
        return GroovyUtils.execute(groovyContent, params);
    }

    private static String addImport(String groovyContent) {
        return GROOVY_SHELL_IMPORT + groovyContent;
    }

    private static Param buildMockParam(List<AttrM> mockAttrs, String mockDouHuResult) {
        ProductM productM = buildMockProductM(mockAttrs);
        if (StringUtils.isBlank(mockDouHuResult)) {
            return new Param(productM, Collections.emptyList());
        }
        DouHuM douHuM = buildMockDouHuM(mockDouHuResult);
        return new Param(productM, Collections.singletonList(douHuM));
    }

    private static ProductM buildMockProductM(List<AttrM> mockAttrs) {
        ProductM productM = new ProductM();
        productM.setProductId(1);
        productM.setCategoryId(1);
        productM.setCategoryName(TEST);
        productM.setSpuType(1);
        productM.setTitle(TEST);
        if (CollectionUtils.isEmpty(mockAttrs)) {
            return productM;
        }
        setAttr(productM, mockAttrs);
        return productM;
    }

    private static void setAttr(ProductM productM, List<AttrM> mockAttrs) {
        if (productM == null ||CollectionUtils.isEmpty(mockAttrs)) {
            return;
        }
        if (CollectionUtils.isEmpty(productM.getExtAttrs())) {
            productM.setExtAttrs(Lists.newArrayList(mockAttrs));
            return;
        }
        productM.getExtAttrs().addAll(mockAttrs);
    }

    private static DouHuM buildMockDouHuM(String douHuResult) {
        DouHuM douHuResponse = new DouHuM();
        douHuResponse.setCode(DOU_HU_SUCCESS);
        douHuResponse.setMsg(DOU_HU_SUCCESS_MSG);
        douHuResponse.setExpId(getExpId(douHuResult));
        douHuResponse.setSk(douHuResult);
        douHuResponse.setAbQueryId(douHuResult);
        douHuResponse.setBucket(DOU_HU_MOCK);
        return douHuResponse;
    }

    private static String getExpId(String mockDouHu) {
        return mockDouHu.split("_")[0];
    }
}

package com.sankuai.qpro.ai.professor.application.utils;

import com.google.gson.Gson;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.google.gson.reflect.TypeToken;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;

/**
 * @author: wuwenqiang
 * @create: 2024-12-10
 * @description:
 */
@Slf4j
public class GsonUtils {
    private final static Gson GSON = new Gson();
    private final static JsonParser JSON_PARSER = new JsonParser();

    public static String toJsonString(Object object) {
        try {
            return GSON.toJson(object);
        } catch (Exception e) {
            log.error("[GsonUtils] toJsonString error",  e);
        }
        return StringUtils.EMPTY;
    }

    public static <T> T fromJson(String json, Class<T> clazz) {
        try {
            return GSON.fromJson(json, clazz);
        } catch (Exception e) {
            log.error("[GsonUtils] fromJson error, str={}", json, e);
        }
        return null;
    }

    public static <T> T fromJson(String json, TypeToken<T> typeToken) {
        try {
            return GSON.fromJson(json, typeToken.getType());
        } catch (Exception e) {
            log.error("[GsonUtils] fromJson error, str={}", json, e);
        }
        return null;
    }

    public static JsonElement parse(String json) {
        if (StringUtils.isBlank(json)) {
            return null;
        }
        try {
            return JSON_PARSER.parse(json);
        } catch (Exception e) {
            log.error("[GsonUtils] parseJsonObject error, str={}", json, e);
        }
        return null;
    }
}

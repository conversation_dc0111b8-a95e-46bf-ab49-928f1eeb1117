package com.sankuai.qpro.ai.professor.application.utils;

import com.google.common.collect.Maps;
import com.sankuai.qpro.ai.professor.application.constants.RequestContextConstant;
import com.sankuai.qpro.ai.professor.application.model.codegenerate.RequestContext;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;

import java.util.Map;

/**
 * @author: wu<PERSON><PERSON><PERSON>
 * @create: 2025-07-01
 * @description:
 */
public class HeaderUtils {

    private static final String COOKIE_FOMAT = "com.sankuai.it.ead.citadel_ssoid=%s";

    private static final String KM_COOKIE_KEY = "com.sankuai.it.ead.citadel_ssoid";

    public static Map<String, String> buildSSOHeaders(String cookieValue) {
        Map<String, String> headers = Maps.newHashMap();
        headers.put("Cookie", String.format(COOKIE_FOMAT, cookieValue));
        headers.put("Accept", "application/json");
        headers.put("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36");
        return headers;
    }

    public static Map<String, String> buildNormalHeaders(String cookieValue) {
        Map<String, String> headers = Maps.newHashMap();
        headers.put("Cookie", cookieValue);
        headers.put("Accept", "application/json");
        headers.put("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36");
        return headers;
    }

    public static String getKmCookieValue() {
        // 1. 先尝试从线程上下文中获取
        Map<String, String> cookieMap = RequestContext.getAttribute(RequestContextConstant.COOKIE_HEADER);
        String kmCookieValue = getKmCookieValueFromMap(cookieMap);
        if (StringUtils.isNotBlank(kmCookieValue)) {
            return kmCookieValue;
        }
        // 2. 线程上下文不存在，则从本地环境变量获取
        return System.getenv("KM_COOKIE_VALUE");
    }

    public static String getCookie() {
        // 1. 先尝试从线程上下文中获取
        String cookie = RequestContext.getAttribute(RequestContextConstant.COOKIE_HEADER);
        if (StringUtils.isNotBlank(cookie)) {
            return cookie;
        }
        // 2. 线程上下文不存在，则从本地环境变量获取
        return System.getenv("KM_COOKIE_VALUE");
    }

    private static String getKmCookieValueFromMap(Map<String, String> cookieMap) {
        if (MapUtils.isEmpty(cookieMap) || !cookieMap.containsKey(KM_COOKIE_KEY)) {
            return null;
        }
        return cookieMap.get(KM_COOKIE_KEY);
    }

    public static Map<String, String> parseCookieHeader(String cookieHeader) {
        if (StringUtils.isBlank(cookieHeader)) {
            return Maps.newHashMap();
        }
        Map<String, String> cookieMap = Maps.newHashMap();
        String[] cookies = cookieHeader.split(";");
        for (String cookie : cookies) {
            String[] cookieParts = cookie.trim().split("=");
            if (cookieParts.length == 2) {
                cookieMap.put(cookieParts[0], cookieParts[1]);
            }
        }
        return cookieMap;
    }
}

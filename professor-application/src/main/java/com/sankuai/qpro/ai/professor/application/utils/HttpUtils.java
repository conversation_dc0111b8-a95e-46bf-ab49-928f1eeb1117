package com.sankuai.qpro.ai.professor.application.utils;

import com.dianping.cat.util.StringUtils;
import com.sankuai.qpro.ai.professor.api.enums.ResponseCodeEnum;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.apache.commons.collections.MapUtils;

import java.util.Iterator;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import com.sankuai.qpro.ai.professor.api.response.RemoteResponse;
import org.apache.commons.lang3.tuple.Pair;

/**
 * @author: wuwen<PERSON>ang
 * @create: 2024-12-13
 * @description:
 */
@Slf4j
public class HttpUtils {
    private static OkHttpClient client;
    static {
        OkHttpClient.Builder builder = new OkHttpClient.Builder();
        builder.readTimeout(240, TimeUnit.SECONDS);
        builder.connectTimeout(240, TimeUnit.SECONDS);
        client = builder.build();
    }

    public static RemoteResponse get(String url, Map<String, Object> params, Map<String, String> headers) {
        url = addParamToUrl(url, params);
        return get(url, headers);
    }

    public static RemoteResponse get(String url, Map<String, String> headers) {
        Request.Builder requestBuilder = new Request.Builder().url(url);
        addHeader(requestBuilder, headers);
        Request request = requestBuilder.build();
        try (Response response = client.newCall(request).execute()) {
            return convertResponse(response);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    public static RemoteResponse<Pair<String, byte[]>> getBytes(String url, Map<String, Object> params, Map<String, String> headers) {
        url = addParamToUrl(url, params);
        return getBytes(url, headers);
    }

    public static RemoteResponse<Pair<String, byte[]>> getBytes(String url, Map<String, String> headers) {
        Request.Builder requestBuilder = new Request.Builder().url(url);
        addHeader(requestBuilder, headers);
        Request request = requestBuilder.build();
        try (Response response = client.newCall(request).execute()) {
            if (response == null) {
                return RemoteResponse.fail(ResponseCodeEnum.SERVER_ERROR, "Response is null");
            }
            if (!response.isSuccessful()) {
                return RemoteResponse.fail(ResponseCodeEnum.SERVER_ERROR, "http request failed with code: " + response.code());
            }
            String contentType = response.header("Content-Type", "");
            return RemoteResponse.success(Pair.of(contentType, response.body().bytes()));
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    public static RemoteResponse post(String url, Map<String, Object> params, String body, Map<String, String> headers) {
        url = addParamToUrl(url, params);
        RequestBody requestBody = RequestBody.create(MediaType.parse("application/json; charset=utf-8"), body);
        Request.Builder requestBuilder = new Request.Builder().url(url).post(requestBody);
        addHeader(requestBuilder, headers);
        Request request = requestBuilder.build();
        try (Response response = client.newCall(request).execute()) {
            return convertResponse(response);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    private static String addParamToUrl(String url, Map<String, Object> params) {
        if (StringUtils.isEmpty(url) || MapUtils.isEmpty(params)) {
            return url;
        }
        StringBuilder sb = new StringBuilder(url);
        sb.append("?");
        Iterator<Map.Entry<String, Object>> iterator = params.entrySet().iterator();
        while (iterator.hasNext()) {
            Map.Entry<String, Object> entry = iterator.next();
            sb.append(entry.getKey()).append("=").append(entry.getValue());
            if (iterator.hasNext()) {
                sb.append("&");
            }
        }
        return sb.toString();
    }

    private static void addHeader(Request.Builder requestBuilder, Map<String, String> headers) {
        if (MapUtils.isEmpty(headers)) {
            return;
        }
        for (Map.Entry<String, String> entry : headers.entrySet()) {
            requestBuilder.addHeader(entry.getKey(), entry.getValue());
        }
    }

    private static RemoteResponse convertResponse(Response response) {
        if (response == null) {
            return null;
        }
        try {
            RemoteResponse remoteResponse = new RemoteResponse();
            remoteResponse.setCode(response.code());
            remoteResponse.setMsg(response.message());
            if (!response.isSuccessful()) {
                return remoteResponse;
            }
            remoteResponse.setData(response.body().string());
            return remoteResponse;
        } catch (Exception e) {
            log.error("[HttpUtils convertResponse error]", e);
        }
        return null;
    }

}

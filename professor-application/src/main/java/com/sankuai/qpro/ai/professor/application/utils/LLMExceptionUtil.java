package com.sankuai.qpro.ai.professor.application.utils;

import com.meituan.mdp.langmodel.api.message.AssistantMessage;
import com.meituan.mdp.langmodel.component.exceptions.FunctionExecutionException;
import org.apache.commons.lang.StringUtils;

import java.lang.reflect.InvocationTargetException;

/**
 * @author: w<PERSON><PERSON><PERSON><PERSON>
 * @create: 2025-01-24
 * @description: 大模型异常处理类
 */
public class LLMExceptionUtil {

    public static boolean validateMsg(AssistantMessage assistantMessage) {
        return assistantMessage != null && StringUtils.isNotEmpty(assistantMessage.getContent())
                && assistantMessage.getE() == null;
    }

    public static Exception extractModelException(AssistantMessage assistantMessage) {
        // 不存在异常则不需要返回异常
        if (validateMsg(assistantMessage)) {
            return null;
        }
        if (assistantMessage == null) {
            return new RuntimeException("大模型返回结果为空");
        }
        return assistantMessage.getE() == null ? new RuntimeException("大模型返回结果为空") : assistantMessage.getE();
    }

    /**
     * 解析大模型函数执行异常（提取内层异常）
     * @param e
     * @return
     */
    public static Throwable parseModelFunctionException(Throwable e) {
        if (!(e instanceof FunctionExecutionException)) {
            return e;
        }
        if (e.getCause() == null || !(e.getCause() instanceof InvocationTargetException)) {
            return e;
        }
        if (((InvocationTargetException) e.getCause()).getTargetException() != null) {
            return ((InvocationTargetException) e.getCause()).getTargetException();
        }
        return e;
    }
}

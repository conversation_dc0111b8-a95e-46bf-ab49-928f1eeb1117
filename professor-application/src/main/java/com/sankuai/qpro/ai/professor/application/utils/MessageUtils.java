package com.sankuai.qpro.ai.professor.application.utils;

import org.springframework.ai.chat.messages.AssistantMessage;
import org.springframework.ai.chat.messages.Message;
import org.springframework.ai.chat.messages.MessageType;
import org.springframework.ai.chat.messages.ToolResponseMessage;
import org.springframework.ai.chat.metadata.ChatGenerationMetadata;
import org.springframework.ai.chat.model.Generation;
import org.springframework.ai.model.tool.ToolExecutionResult;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * MessageUtils
 *
 * <AUTHOR>
 * @since 2025/8/4
 */
public class MessageUtils {

    public static Message getLast(List<Message> messages, MessageType type) {
        if (CollectionUtils.isEmpty(messages) || Objects.isNull(type)) {
            return null;
        }
        for (int i = messages.size() - 1; i > 0; i--) {
            Message message = messages.get(i);
            if (type.getValue().equals(message.getMessageType().getValue())) {
                return message;
            }
        }
        return null;
    }

}
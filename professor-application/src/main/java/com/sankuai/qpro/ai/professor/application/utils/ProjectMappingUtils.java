package com.sankuai.qpro.ai.professor.application.utils;

import com.dianping.lion.client.Lion;
import com.meituan.mdp.boot.starter.MdpContextUtils;
import org.springframework.util.StringUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * ProjectMappingUtils
 *
 * <AUTHOR>
 * @since 2025/8/4
 */
public class ProjectMappingUtils {

    public static Map<String, String> PROJECT_PATH_MAPPING = Map.of(
//            "ssh://*******************/vc/dzviewscene-dealshelf-home.git", "/Users/<USER>/Data/dev/code/dzviewscene-dealshelf-home"
            "ssh://*******************/dztech/professor.git","/Users/<USER>/IdeaProjects/professor"
    );
//    public static Map<String, String> PROJECT_PATH_MAPPING = Lion.getMap(MdpContextUtils.getAppKey(),"project.path.mapping", String.class, new HashMap<>());

    public static String getPathByGit(String gitSsh) {
        if (StringUtils.hasText(gitSsh)) {
            return PROJECT_PATH_MAPPING.get(gitSsh);
        }
        return null;
    }

}
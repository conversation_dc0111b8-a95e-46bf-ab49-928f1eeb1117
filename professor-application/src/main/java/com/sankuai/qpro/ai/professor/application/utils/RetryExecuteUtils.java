package com.sankuai.qpro.ai.professor.application.utils;

import com.meituan.mdp.langmodel.api.message.AssistantMessage;

import java.net.SocketTimeoutException;
import java.util.function.Supplier;

/**
 * @author: w<PERSON><PERSON><PERSON><PERSON>
 * @create: 2025-02-09
 * @description:
 */
public class RetryExecuteUtils {

    /**
     * 重试执行
     * @param operation 可执行操作
     * @param maxRetries 最大重试次数
     * @param retryIntervalMills 重试间隔时间（毫秒）
     * @return 返回操作执行结果
     * @param <T> 返回类型
     * @throws Exception 如果所有重试都失败，则抛出最后一次的异常
     */
    public static <T> T retryExecute(Supplier<T> operation, int maxRetries, long retryIntervalMills) throws Exception {
        int retryCount = 0;
        while (retryCount < maxRetries) {
            try {
                return operation.get();
            } catch (Exception e) {
                retryCount++;
                if (retryCount >= maxRetries) {
                    throw e;
                }
                Thread.sleep(retryIntervalMills);
            }
        }
        return null;
    }

    public static AssistantMessage retryModelTimeoutExecute(Supplier<AssistantMessage> operation, int maxRetries) {
        int retryCount = 0;
        while(retryCount < maxRetries) {
            AssistantMessage assistantMessage = operation.get();
            // 有效结果则直接返回
            if (LLMExceptionUtil.validateMsg(assistantMessage)) {
                return assistantMessage;
            }
            // 非超时异常直接返回
            Exception modelException = LLMExceptionUtil.extractModelException(assistantMessage);
            if (!(modelException instanceof SocketTimeoutException)) {
                return assistantMessage;
            }
            // 超过重试次数则直接返回
            retryCount++;
            if (retryCount >= maxRetries) {
                return assistantMessage;
            }
        }
        return null;
    }
}

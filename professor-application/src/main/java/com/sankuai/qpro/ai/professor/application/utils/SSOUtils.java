package com.sankuai.qpro.ai.professor.application.utils;

import com.google.common.collect.Maps;
import org.apache.commons.codec.binary.Base64;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import com.sankuai.qpro.ai.professor.api.response.RemoteResponse;

/**
 * @author: wu<PERSON><PERSON><PERSON>
 * @create: 2025-05-20
 * @description:
 */
public class SSOUtils {

    public static Map<String, String> getSignedHeaders(String method, String uri, String clientId, String secret) {
        //method为当前方法的method（"POST" | "GET"）
        if (method == null || uri == null) {
            return Maps.newHashMap();
        }
        String date = getAuthDate(new Date());
        method = method.toUpperCase();
        String authorization = getAuthorization(uri, method, date, clientId, secret);
        Map<String, String> headers = Maps.newHashMap();
        headers.put("Content-Type", "application/json;charset=UTF-8");
        headers.put("Authorization", authorization);
        headers.put("Date", date);
        return headers;
    }

    public static String getAuthDate(Date date) {
        DateFormat df = new SimpleDateFormat("EEE, dd MMM yyyy HH:mm:ss z", Locale.ENGLISH);
        df.setTimeZone(TimeZone.getTimeZone("GMT"));
        return df.format(date);
    }

    public static String getAuthorization(String uri, String method, String date, String clientId, String secret) {
        String stringToSign = method + " " + uri + "\n" + date;
        String signature = getSignature(stringToSign, secret);
        String authorization = "MWS" + " " + clientId + ":" + signature;
        return authorization;
    }

    public static String getSignature(String data, String secret) {
        String result;
        try {
            SecretKeySpec signingKey = new SecretKeySpec(secret.getBytes(), "HmacSHA1");
            Mac mac = Mac.getInstance("HmacSHA1");
            mac.init(signingKey);
            byte[] rawHmac = mac.doFinal(data.getBytes());
            result = Base64.encodeBase64String(rawHmac);
        } catch (Exception e) {
            throw new RuntimeException("Failed to generate HMAC : " + e.getMessage());
        }
        return result;
    }

    public static void main(String[] args)  {
        try {
            String clientId = "41be7fc50a";
            String secret = "47917fd213dd4b3fb01a5b2f28c43324";
            String uri = "/api/login/smscode";
            String method = "POST";
            Map<String, String> signedHeaders = getSignedHeaders(method, uri, clientId, secret);
            String url = "https://ssosv.it.test.sankuai.com/sson/api/login/smscode";
            String body = "{\"loginName\":\"wuwenqiang06\"}";
            RemoteResponse response = HttpUtils.post(url, null, body, signedHeaders);
            String respBody = (String) Optional.ofNullable(response.getData()).orElse("");
            System.out.println(respBody);
        } catch (Exception e) {
            System.out.println(e);
        }
    }
}

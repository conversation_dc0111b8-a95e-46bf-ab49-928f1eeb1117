package com.sankuai.qpro.ai.professor.application.utils;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.databind.type.MapType;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/8/3
 */
@Slf4j
public class SerializeUtils {

    private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper();

    static {
        OBJECT_MAPPER.disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES);
        OBJECT_MAPPER.configure(SerializationFeature.FAIL_ON_SELF_REFERENCES, false);
        OBJECT_MAPPER.setSerializationInclusion(JsonInclude.Include.NON_NULL);
    }

    public static String toJsonStr(Object obj) {
        try {
            return OBJECT_MAPPER.writeValueAsString(obj);
        } catch (JsonProcessingException e) {
            log.error("[SerializeUtils#toJson] jackson read error", e);
            return obj.toString();
        }
    }

    public static String toJsonNullable(Object obj) {
        if (obj == null) {
            return "";
        }

        try {
            return OBJECT_MAPPER.writeValueAsString(obj);
        } catch (JsonProcessingException e) {
            log.error("[SerializeUtils#toJson] jackson read error, json={}", obj, e);
            return null;
        }
    }

    public static <T> T toObj(String str, Class<T> clazz) {
        try {
            return OBJECT_MAPPER.readValue(str, clazz);
        } catch (Exception e) {
            log.error("toObj error, str={}", str, e);
            return null;
        }
    }

    public static <T> T toObj(String str, TypeReference<T> clazz) {
        try {
            return OBJECT_MAPPER.readValue(str, clazz);
        } catch (JsonProcessingException e) {
            log.error("toObj error, str={}", str, e);
            return null;
        }
    }

    public static <K, V> Map<K, V> toMap(String jsonStr, Class<K> kClass, Class<V> vClass) {
        if (StringUtils.isBlank(jsonStr)) {
            return new HashMap<>();
        }

        try {
            MapType mapType = OBJECT_MAPPER.getTypeFactory().constructMapType(Map.class, kClass, vClass);
            return OBJECT_MAPPER.readValue(jsonStr, mapType);
        } catch (JsonProcessingException e) {
            log.error("[SerializeUtils#toMap] jackson read map error, json={}", jsonStr, e);
            return new HashMap<>();
        }
    }

    public static <T> T toObjThrowException(String str, TypeReference<T> clazz) throws IOException {
        return OBJECT_MAPPER.readValue(str, clazz);
    }

    public static <T> T toObjThrowException(String str, Class<T> valueType) throws IOException {
        return OBJECT_MAPPER.readValue(str, valueType);
    }

    public static JsonNode readTree(String content) throws Exception {
        return OBJECT_MAPPER.readTree(content);
    }
}

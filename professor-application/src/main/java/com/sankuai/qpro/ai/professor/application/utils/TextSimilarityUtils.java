package com.sankuai.qpro.ai.professor.application.utils;

import com.google.common.collect.Lists;
import edu.stanford.nlp.ling.CoreAnnotations;
import edu.stanford.nlp.ling.CoreLabel;
import edu.stanford.nlp.pipeline.Annotation;
import edu.stanford.nlp.pipeline.StanfordCoreNLP;
import edu.stanford.nlp.util.CoreMap;

import java.util.List;
import java.util.Properties;

/**
 * @author: wuwen<PERSON><PERSON>
 * @create: 2025-02-17
 * @description:
 */
public class TextSimilarityUtils {

    /**
     * 计算Rouge-L
     * @param referenceText
     * @param hypothesisText
     * @return
     */
    public static double calculateRougeL(String referenceText, String hypothesisText) {
        // 创建Stanford CoreNLP管道
        Properties props = new Properties();
        props.setProperty("annotators", "tokenize, ssplit"); //加, pos, lemma 会报错
        props.setProperty("ssplit.eolonly", "true");
        StanfordCoreNLP pipeline = new StanfordCoreNLP(props);

        // 处理参考答案和假设答案
        Annotation referenceAnnotation = new Annotation(referenceText);
        Annotation hypothesisAnnotation = new Annotation(hypothesisText);
        pipeline.annotate(referenceAnnotation);
        pipeline.annotate(hypothesisAnnotation);

        // 获取参考答案和假设答案的分词列表
        List<String> referenceTokens = extractTokens(referenceAnnotation);
        List<String> hypothesisTokens = extractTokens(hypothesisAnnotation);

        // 计算 Rouge-L
        double rougeL = computeRougeL(referenceTokens, hypothesisTokens);
        return rougeL;
    }

    private static List<String> extractTokens(Annotation annotation) {
        // 提取分词结果
        List<String> tokens = Lists.newArrayList();
        for (CoreMap sentence : annotation.get(CoreAnnotations.SentencesAnnotation.class)) {
            for (CoreLabel token : sentence.get(CoreAnnotations.TokensAnnotation.class)) {
                tokens.add(token.word());
            }
        }
        return tokens;
    }

    private static double computeRougeL(List<String> referenceTokens, List<String> hypothesisTokens) {
        if (referenceTokens == null || hypothesisTokens == null || referenceTokens.isEmpty() || hypothesisTokens.isEmpty()) {
            return 0.0;
        }

        int lcsLength = calculateLCS(referenceTokens, hypothesisTokens);
        int referenceLength = referenceTokens.size();
        int hypothesisLength = hypothesisTokens.size();

        // 计算 Precision, Recall, F1-Score
        double precision = (double) lcsLength / hypothesisLength;
        double recall = (double) lcsLength / referenceLength;
        return (Math.abs(precision + recall) < 1e-9) ? 0.0 : (2 * precision * recall) / (precision + recall);
    }

    private static int calculateLCS(List<String> referenceTokens, List<String> hypothesisTokens) {
        int m = referenceTokens.size();
        int n = hypothesisTokens.size();
        int[][] dp = new int[m + 1][n + 1];

        // 动态规划计算LCS长度
        for (int i = 1; i <= m; i++) {
            for (int j = 1; j <= n; j++) {
                if (referenceTokens.get(i - 1).equals(hypothesisTokens.get(j - 1))) {
                    dp[i][j] = dp[i - 1][j - 1] + 1;
                } else {
                    dp[i][j] = Math.max(dp[i - 1][j], dp[i][j - 1]);
                }
            }
        }

        return dp[m][n];
    }
}

package com.sankuai.qpro.ai.professor.application.utils;

import org.apache.commons.lang.StringUtils;

import java.sql.Timestamp;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;

/**
 * @author: wuwen<PERSON><PERSON>
 * @create: 2024-12-14
 * @description:
 */
public class TimeUtils {

    public static final String TIME_PATTERN = "yyyy-MM-dd HH:mm:ss";

    public static String convertLocalDateTimeToString(LocalDateTime localDateTime) {
        if (localDateTime == null) {
            return null;
        }
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(TIME_PATTERN);
        return localDateTime.format(formatter);
    }

    public static LocalDateTime convertMillSecondToLocalDateTime(long timestamp) {
        if (timestamp <= 0) {
            return null;
        }
        return LocalDateTime.ofInstant(Instant.ofEpochMilli(timestamp), ZoneId.systemDefault());
    }

    public static LocalDateTime convertSecondToLocalDateTime(long timestamp) {
        if (timestamp <= 0) {
            return null;
        }
        return LocalDateTime.ofInstant(Instant.ofEpochSecond(timestamp), ZoneId.systemDefault());
    }


    public static LocalDateTime convertStringToLocalDateTime(String dateTimeStr, String pattern) {
        if (StringUtils.isBlank(dateTimeStr)) {
            return null;
        }
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(pattern);
        return LocalDateTime.parse(dateTimeStr, formatter);
    }

    public static LocalDateTime convertStringToLocalDateTime(String dateTimeStr) {
        if (StringUtils.isBlank(dateTimeStr)) {
            return null;
        }
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(TIME_PATTERN);
        return LocalDateTime.parse(dateTimeStr, formatter);
    }

    public static long convertStringToMillSecond(String dateTimeStr) {
        if (StringUtils.isBlank(dateTimeStr)) {
            return 0;
        }
        return convertLocalDateTimeToMillSecond(convertStringToLocalDateTime(dateTimeStr));
    }

    public static long convertLocalDateTimeToMillSecond(LocalDateTime localDateTime) {
        if (localDateTime == null) {
            return 0;
        }
        return localDateTime.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli();
    }

    public static String convertTimeStamp2DateStr(Timestamp timestamp) {
        if (timestamp == null) {
            return null;
        }
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(TIME_PATTERN);
        return formatter.format(timestamp.toLocalDateTime());
    }
}

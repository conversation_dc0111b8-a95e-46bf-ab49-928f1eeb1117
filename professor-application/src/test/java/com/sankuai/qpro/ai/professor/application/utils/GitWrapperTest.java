package com.sankuai.qpro.ai.professor.application.utils;

import org.junit.jupiter.api.Test;

import java.util.List;

import static org.junit.jupiter.api.Assertions.assertNotNull;

class GitWrapperTest {

    @Test
    void metaOfGitUrl() {
        List<String> list = GitWrapper.metaOfGitUrl("ssh://*******************/dztech/professor.git");
        assertNotNull(list);
        list.forEach(System.out::println);
    }

    @Test
    void testClone() {
        String path = GitWrapper.checkout("ssh://*******************/dztech/professor.git", "origin/master", "master", System.getProperty("user.home") + "/temp");
        System.out.println(path);
    }

}
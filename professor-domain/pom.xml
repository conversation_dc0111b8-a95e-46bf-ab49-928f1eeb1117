<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.sankuai.qpro.ai</groupId>
        <artifactId>professor</artifactId>
        <version>${revision}</version>
    </parent>

    <artifactId>professor-domain</artifactId>
    <version>${revision}</version>
    <packaging>jar</packaging>
    <name>professor-domain</name>

    <dependencies>
        <!-- Project module -->
        <dependency>
            <groupId>com.sankuai.qpro.ai</groupId>
            <artifactId>professor-infrastructure</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-context</artifactId>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>
    </dependencies>

</project>

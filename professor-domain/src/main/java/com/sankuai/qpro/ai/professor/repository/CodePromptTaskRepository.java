package com.sankuai.qpro.ai.professor.repository;

import com.sankuai.qpro.ai.professor.entity.CodePromptTaskEntity;
import com.sankuai.qpro.ai.professor.entity.CodePromptTaskExtraEntity;

import java.util.List;
import java.util.Map;

/**
 * @author: wu<PERSON><PERSON><PERSON>
 * @create: 2025-06-03
 * @description:
 */
public interface CodePromptTaskRepository {
    public boolean insertCodePromptTask(CodePromptTaskEntity taskEntity);

    public boolean updateCodePromptTask(CodePromptTaskEntity taskEntity);

    public CodePromptTaskEntity queryCodePromptTaskById(Long id);

    public CodePromptTaskEntity queryCodePromptTaskByTaskIdAndUserId(Long taskId, String userId);

    public List<CodePromptTaskEntity> queryCodePromptsTaskByUserId(String userId);

    public long countCodePromptsTaskByUserId(String userId);

    public List<CodePromptTaskEntity> queryCodePromptsTaskByUserIdAndPage(String userId, int offset, int size);
}

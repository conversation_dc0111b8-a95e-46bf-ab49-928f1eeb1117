package com.sankuai.qpro.ai.professor.repository;

import com.sankuai.qpro.ai.professor.entity.ConfigUnitEntity;

import java.util.List;

/**
 * @author: wuwen<PERSON><PERSON>
 * @create: 2024-12-31
 * @description:
 */
public interface ConfigUnitRepository {
    Long insert(ConfigUnitEntity configUnitInfo);

    ConfigUnitEntity findById(Long id);

    List<ConfigUnitEntity> findAll();

    List<ConfigUnitEntity> findByScene(String scene);

    void update(ConfigUnitEntity configUnitInfo);

    void delete(Long id);
}

package com.sankuai.qpro.ai.professor.repository;

import com.sankuai.qpro.ai.professor.entity.ConversationEntity;

import java.util.List;

/**
 * @author: wu<PERSON><PERSON><PERSON>
 * @create: 2024-12-10
 * @description:
 */
public interface ConversationBaseRepository {
    Long insert(ConversationEntity conversationBaseInfo);

    ConversationEntity findById(Long id);

    List<ConversationEntity> findAll();

    void update(ConversationEntity conversationBaseInfo);

    void delete(Long id);
}

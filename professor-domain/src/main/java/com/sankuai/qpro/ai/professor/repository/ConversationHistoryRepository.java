package com.sankuai.qpro.ai.professor.repository;

import com.sankuai.qpro.ai.professor.entity.ConversationMsgEntity;

import java.util.List;

/**
 * @author: wu<PERSON><PERSON><PERSON>
 * @create: 2024-12-10
 * @description:
 */
public interface ConversationHistoryRepository {
    Long insert(ConversationMsgEntity conversationHistory);

    int batchInsert(List<ConversationMsgEntity> conversationHistories);

    ConversationMsgEntity findById(Long id);

    List<ConversationMsgEntity> findByConversationId(Long conversationId);

    void update(ConversationMsgEntity conversationHistory);
    ConversationMsgEntity findLatestMsgByConversationId(Long conversationId);

    /**
     * 查询最新的对话片段
     * @param conversationId
     * @param dialogSegment
     * @return
     */
    ConversationMsgEntity findLatestSegment(Long conversationId, String dialogSegment);

    /**
     * 根据会话id和起始id查询历史记录，包含起始ID
     * @param conversationId
     * @param startId
     * @return
     */
    List<ConversationMsgEntity> findByConversationIdAndStartId(Long conversationId, Long startId);

    /**
     * 查询最新的AI消息
     * @param conversationId 会话id
     * @param status 消息状态
     * @return
     */
    ConversationMsgEntity findLatestAIMsg(Long conversationId, Integer status);

    void updateReviewType(Long id, Integer reviewType);

    void delete(Long id);
}

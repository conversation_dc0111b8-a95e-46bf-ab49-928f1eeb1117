package com.sankuai.qpro.ai.professor.repository.impl;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.qpro.ai.professor.entity.CodePromptTaskEntity;
import com.sankuai.qpro.ai.professor.entity.CodePromptTaskExtraEntity;
import com.sankuai.qpro.ai.professor.mapper.CodePromptTaskExtraMapper;
import com.sankuai.qpro.ai.professor.mapper.CodePromptTaskMapper;
import com.sankuai.qpro.ai.professor.repository.CodePromptTaskRepository;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * @author: wuwen<PERSON><PERSON>
 * @create: 2025-06-03
 * @description:
 */
@Repository
@Slf4j
public class CodePromptTaskRepositoryImpl implements CodePromptTaskRepository {

    @Resource
    private CodePromptTaskMapper codePromptTaskMapper;
    @Resource
    private CodePromptTaskExtraMapper codePromptTaskExtraMapper;

    @Override
    public boolean insertCodePromptTask(CodePromptTaskEntity taskEntity) {
        if (!validateTaskEntity(taskEntity)) {
            return false;
        }
        // 1.生成任务
        Long promptTaskRes = codePromptTaskMapper.insert(taskEntity);
        if (promptTaskRes <= 0) {
            return false;
        }
        // 2.插入数据
        List<CodePromptTaskExtraEntity> extraEntities = buildCodePromptTaskExtraEntity(taskEntity);
        if (CollectionUtils.isEmpty(extraEntities)) {
            return true;
        }
        int batchRes = codePromptTaskExtraMapper.batchInsert(extraEntities);
        return batchRes > 0;
    }

    @Override
    public boolean updateCodePromptTask(CodePromptTaskEntity taskEntity) {
        if (!validateTaskEntity(taskEntity) || !validateTaskId(taskEntity.getId())) {
            return false;
        }
        // 1. 判断任务是否存在
        CodePromptTaskEntity baseTaskEntity = codePromptTaskMapper.findById(taskEntity.getId());
        if (baseTaskEntity == null) {
            return false;
        }
        // 2. 更新任务（仅在任务阶段变更时执行更新）
        if (isStageChange(baseTaskEntity.getStage(), taskEntity.getStage())) {
            codePromptTaskMapper.update(taskEntity);
        }
        // 3. 插入/更新数据
        List<CodePromptTaskExtraEntity> extraEntities = buildCodePromptTaskExtraEntity(taskEntity);
        if (CollectionUtils.isEmpty(extraEntities)) {
            return true;
        }
        int batchRes = codePromptTaskExtraMapper.batchUpsert(extraEntities);
        return batchRes > 0;
    }

    @Override
    public CodePromptTaskEntity queryCodePromptTaskById(Long id) {
        if (!validateTaskId(id)) {
            return null;
        }
        // 1. 查询主任务表
        CodePromptTaskEntity taskEntity = codePromptTaskMapper.findById(id);
        if (taskEntity == null) {
            return null;
        }
        // 2. 填充扩展数据
        fillExtraData(taskEntity);
        return taskEntity;
    }

    @Override
    public CodePromptTaskEntity queryCodePromptTaskByTaskIdAndUserId(Long taskId, String userId) {
        if (!validateTaskId(taskId) || StringUtils.isBlank(userId)) {
            return null;
        }
        // 1. 查询主任务表
        CodePromptTaskEntity taskEntity = codePromptTaskMapper.findByIdAndUserId(taskId, userId);
        if (taskEntity == null) {
            return null;
        }
        // 2. 填充扩展数据
        fillExtraData(taskEntity);
        return taskEntity;
    }

    @Override
    public List<CodePromptTaskEntity> queryCodePromptsTaskByUserId(String userId) {
        if (StringUtils.isBlank(userId)) {
            return null;
        }
        // 1. 查询主任务表
        List<CodePromptTaskEntity> taskEntities = codePromptTaskMapper.findByUserId(userId);
        if (CollectionUtils.isEmpty(taskEntities)) {
            return null;
        }
        // 2. 填充扩展信息
        taskEntities.forEach(entity -> fillExtraData(entity));
        return taskEntities;
    }

    @Override
    public long countCodePromptsTaskByUserId(String userId) {
        if (StringUtils.isBlank(userId)) {
            return 0;
        }
        return codePromptTaskMapper.countByUserId(userId);
    }

    @Override
    public List<CodePromptTaskEntity> queryCodePromptsTaskByUserIdAndPage(String userId, int offset, int size) {
        if (StringUtils.isBlank(userId) || offset < 0 || size <= 0) {
            return null;
        }
        // 1. 查询主任务表
        List<CodePromptTaskEntity> taskEntities = codePromptTaskMapper.findByUserIdWithPage(userId, offset, size);
        // 2. 填充扩展信息
        taskEntities.forEach(entity -> fillExtraData(entity));
        return taskEntities;
    }

    private boolean validateTaskEntity(CodePromptTaskEntity taskEntity) {
        return taskEntity != null && StringUtils.isNotBlank(taskEntity.getUserId());
    }

    private boolean validateTaskId(Long id)  {
        return id != null && id > 0;
    }

    private boolean isStageChange(int baseStage, int targetStage) {
        return baseStage != targetStage;
    }

    private List<CodePromptTaskExtraEntity> buildCodePromptTaskExtraEntity(CodePromptTaskEntity taskEntity) {
        if (taskEntity == null || MapUtils.isEmpty(taskEntity.getExtraData())) {
            return null;
        }
        List<CodePromptTaskExtraEntity> result = Lists.newArrayList();
        for (Map.Entry<String, String> entry : taskEntity.getExtraData().entrySet()) {
            CodePromptTaskExtraEntity extraData = buildBaseExtraEntity(entry.getKey(), entry.getValue());
            if (extraData == null) {
                continue;
            }
            extraData.setTaskId(taskEntity.getId());
            result.add(extraData);
        }
        return result;
    }

    private CodePromptTaskExtraEntity buildBaseExtraEntity(String key, String value) {
        if (StringUtils.isBlank(key)) {
            return null;
        }
        CodePromptTaskExtraEntity extraEntity = new CodePromptTaskExtraEntity();
        extraEntity.setName(key);
        extraEntity.setValue(value);
        return extraEntity;
    }

    private Map<String, String> buildExtraData(List<CodePromptTaskExtraEntity> extraEntities) {
        if (CollectionUtils.isEmpty(extraEntities)) {
            return null;
        }
        Map<String, String> result = Maps.newHashMap();
        for (CodePromptTaskExtraEntity extraEntity : extraEntities) {
            if (extraEntity == null || StringUtils.isBlank(extraEntity.getName())) {
                continue;
            }
            result.put(extraEntity.getName(), extraEntity.getValue());
        }
        return result;
    }

    private void fillExtraData(CodePromptTaskEntity taskEntity) {
        if (taskEntity == null || !validateTaskId(taskEntity.getId())) {
            return;
        }
        // 1. 查询额外数据
        List<CodePromptTaskExtraEntity> extraEntities = codePromptTaskExtraMapper.findByTaskId(taskEntity.getId());
        if (CollectionUtils.isEmpty(extraEntities)) {
            return;
        }
        // 2. 组装数据
        Map<String, String> extraData = buildExtraData(extraEntities);
        taskEntity.setExtraData(extraData);
    }
}

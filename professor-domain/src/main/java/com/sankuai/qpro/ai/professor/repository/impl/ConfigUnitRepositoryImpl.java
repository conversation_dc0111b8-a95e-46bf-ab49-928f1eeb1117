package com.sankuai.qpro.ai.professor.repository.impl;

import com.sankuai.qpro.ai.professor.entity.ConfigUnitEntity;
import com.sankuai.qpro.ai.professor.mapper.ConfigUnitMapper;
import com.sankuai.qpro.ai.professor.repository.ConfigUnitRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * @author: wuwenqiang
 * @create: 2024-12-31
 * @description:
 */
@Repository
public class ConfigUnitRepositoryImpl implements ConfigUnitRepository {
    @Autowired
    private ConfigUnitMapper configUnitMapper;

    @Override
    public Long insert(ConfigUnitEntity configUnitInfo) {
        configUnitMapper.insert(configUnitInfo);
        return configUnitInfo.getId();
    }

    @Override
    public ConfigUnitEntity findById(Long id) {
        return configUnitMapper.findById(id);
    }

    @Override
    public List<ConfigUnitEntity> findAll() {
        return configUnitMapper.findAll();
    }

    @Override
    public List<ConfigUnitEntity> findByScene(String scene) {
        return configUnitMapper.findByScene(scene);
    }

    @Override
    public void update(ConfigUnitEntity configUnitInfo) {
        configUnitMapper.update(configUnitInfo);
    }

    @Override
    public void delete(Long id) {
        configUnitMapper.delete(id);
    }
}

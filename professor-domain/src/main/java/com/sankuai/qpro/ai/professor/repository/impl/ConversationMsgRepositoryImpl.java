package com.sankuai.qpro.ai.professor.repository.impl;

import com.sankuai.qpro.ai.professor.api.enums.RoleTypeEnum;
import com.sankuai.qpro.ai.professor.entity.ConversationMsgEntity;
import com.sankuai.qpro.ai.professor.mapper.ConversationMsgMapper;
import com.sankuai.qpro.ai.professor.repository.ConversationHistoryRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * @author: wuwen<PERSON>ang
 * @create: 2024-12-10
 * @description:
 */
@Repository
public class ConversationMsgRepositoryImpl implements ConversationHistoryRepository {
    @Autowired
    private ConversationMsgMapper conversationHistoryMapper;

    @Override
    public Long insert(ConversationMsgEntity conversationHistory) {
        conversationHistoryMapper.insert(conversationHistory);
        return conversationHistory.getId();
    }

    @Override
    public int batchInsert(List<ConversationMsgEntity> conversationHistories) {
        return conversationHistoryMapper.batchInsert(conversationHistories);
    }

    @Override
    public ConversationMsgEntity findById(Long id) {
        return conversationHistoryMapper.findById(id);
    }

    @Override
    public List<ConversationMsgEntity> findByConversationId(Long conversationId) {
        return conversationHistoryMapper.findByConversationId(conversationId);
    }

    @Override
    public ConversationMsgEntity findLatestMsgByConversationId(Long conversationId) {
        return conversationHistoryMapper.findLatestMsgByConversationId(conversationId);
    }

    @Override
    public ConversationMsgEntity findLatestSegment(Long conversationId, String dialogSegment) {
        return conversationHistoryMapper.findLatestSegment(conversationId, dialogSegment);
    }

    @Override
    public List<ConversationMsgEntity> findByConversationIdAndStartId(Long conversationId, Long startId) {
        return conversationHistoryMapper.findByConversationIdAndStartId(conversationId, startId);
    }

    @Override
    public ConversationMsgEntity findLatestAIMsg(Long conversationId, Integer status) {
        return conversationHistoryMapper.findLatestMsg(conversationId, RoleTypeEnum.AI.getType(), status);
    }

    @Override
    public void update(ConversationMsgEntity conversationHistory) {
        conversationHistoryMapper.update(conversationHistory);
    }

    @Override
    public void updateReviewType(Long id, Integer reviewType) {
        conversationHistoryMapper.updateReviewType(id, reviewType);
    }

    @Override
    public void delete(Long id) {
        conversationHistoryMapper.delete(id);
    }
}

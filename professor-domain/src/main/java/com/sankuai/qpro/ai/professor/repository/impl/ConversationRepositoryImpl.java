package com.sankuai.qpro.ai.professor.repository.impl;

import com.sankuai.qpro.ai.professor.entity.ConversationEntity;
import com.sankuai.qpro.ai.professor.mapper.ConversationMapper;
import com.sankuai.qpro.ai.professor.repository.ConversationBaseRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * @author: wu<PERSON><PERSON><PERSON>
 * @create: 2024-12-10
 * @description:
 */
@Repository
public class ConversationRepositoryImpl implements ConversationBaseRepository {

    @Autowired
    private ConversationMapper conversationBaseMapper;

    @Override
    public Long insert(ConversationEntity conversationBaseInfo) {
        conversationBaseMapper.insert(conversationBaseInfo);
        return conversationBaseInfo.getId();
    }

    @Override
    public ConversationEntity findById(Long id) {
        return conversationBaseMapper.findById(id);
    }

    @Override
    public List<ConversationEntity> findAll() {
        return conversationBaseMapper.findAll();
    }

    @Override
    public void update(ConversationEntity conversationBaseInfo) {
        conversationBaseMapper.update(conversationBaseInfo);
    }

    @Override
    public void delete(Long id) {
        conversationBaseMapper.delete(id);
    }

}

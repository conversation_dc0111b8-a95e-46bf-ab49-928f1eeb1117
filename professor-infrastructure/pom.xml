<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.sankuai.qpro.ai</groupId>
        <artifactId>professor</artifactId>
        <version>${revision}</version>
    </parent>

    <artifactId>professor-infrastructure</artifactId>
    <version>${revision}</version>
    <packaging>jar</packaging>
    <name>professor-infrastructure</name>

    <dependencies>
        <dependency>
            <groupId>com.sankuai.qpro.ai</groupId>
            <artifactId>professor-api</artifactId>
        </dependency>
        <dependency>
            <groupId>org.mybatis</groupId>
            <artifactId>mybatis</artifactId>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>
</dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.mybatis.generator</groupId>
                <artifactId>mybatis-generator-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>com.meituan.mdp.maven.plugins</groupId>
                <artifactId>mdp-mybatis-generator-maven-plugin</artifactId>
            </plugin>
        </plugins>
    </build>

</project>
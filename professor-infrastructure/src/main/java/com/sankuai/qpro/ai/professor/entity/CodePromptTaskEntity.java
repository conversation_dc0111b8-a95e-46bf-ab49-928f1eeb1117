package com.sankuai.qpro.ai.professor.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.sql.Timestamp;
import java.util.Map;

/**
 * @author: wu<PERSON><PERSON><PERSON>
 * @create: 2025-06-03
 * @description:
 */
@Data
@EqualsAndHashCode(exclude = {"createTime", "updateTime"})
public class CodePromptTaskEntity {
    private Long id;
    private String userId;
    private String title;
    private int source;
    private int stage;
    Map<String, String> extraData;
    private Timestamp createTime;
    private Timestamp updateTime;
}

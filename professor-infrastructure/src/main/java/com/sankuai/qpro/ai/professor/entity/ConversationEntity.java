package com.sankuai.qpro.ai.professor.entity;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * @author: w<PERSON><PERSON><PERSON><PERSON>
 * @create: 2024-12-10
 * @description: 会话基本信息表
 */
@Data
public class ConversationEntity {
    private Long id;
    private Integer userType;
    private String userId;
    private Long configUnitId;
    private String result;
    private LocalDateTime createTime;
    private LocalDateTime updateTime;
}

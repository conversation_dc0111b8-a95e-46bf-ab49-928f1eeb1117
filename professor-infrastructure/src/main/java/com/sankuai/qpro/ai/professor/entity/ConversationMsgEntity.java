package com.sankuai.qpro.ai.professor.entity;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * @author: wu<PERSON><PERSON><PERSON>
 * @create: 2024-12-10
 * @description:
 */
@Data
public class ConversationMsgEntity {
    private Long id;
    private Long conversationId;
    private Integer role;
    private String message;
    private Integer flag;
    private Integer reviewType;
    private String dialogSegment;
    private Integer status;
    private LocalDateTime createTime;
    private LocalDateTime updateTime;
}

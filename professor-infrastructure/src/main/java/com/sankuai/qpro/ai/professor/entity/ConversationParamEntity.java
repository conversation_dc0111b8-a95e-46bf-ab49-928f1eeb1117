package com.sankuai.qpro.ai.professor.entity;

import com.sankuai.qpro.ai.professor.api.enums.ConversationStageEnum;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/1/14
 */
@Data
public class ConversationParamEntity {

    private Long id;
    /**
     * 会话ID
     */
    private Long conversationId;
    /**
     * 配置单元名称
     */
    private String configUnitName;
    /**
     * 会话进行到的阶段
     * {@link com.sankuai.qpro.ai.professor.api.enums.ConversationStageEnum}
     */
    private Integer stage = ConversationStageEnum.START.getCode();
    /**
     * POI分类ID
     */
    private String poiCategoryIds;
    /**
     * POI分类名称
     */
    private String poiCategoryNames;
    /**
     * 商品分类ID
     */
    private Long productCategoryId;
    /**
     * 商品分类名称
     */
    private String productCategoryName;
    /**
     * 实验号，两个实验号时使用英文逗号拼接
     */
    private String expIds;
    /**
     * 包含对线上逻辑的修改，0 不包括 1 包括，默认是 0
     */
    private String strategy;

    public static ConversationParamEntity of(long conversationId, String configUnitName) {
        ConversationParamEntity entity = new ConversationParamEntity();
        entity.setConversationId(conversationId);
        entity.setConfigUnitName(configUnitName);
        return entity;
    }
}

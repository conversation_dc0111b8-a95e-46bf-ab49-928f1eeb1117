package com.sankuai.qpro.ai.professor.mapper;

import com.sankuai.qpro.ai.professor.entity.CodePromptTaskExtraEntity;
import org.apache.ibatis.annotations.*;

import java.util.List;

/**
 * @author: w<PERSON><PERSON><PERSON><PERSON>
 * @create: 2025-01-01
 * @description: 需求prompt生成任务额外信息Mapper
 */
@Mapper
public interface CodePromptTaskExtraMapper {

    @Insert("INSERT INTO ai_coding_prompt_generate_task_extra_data (task_id, `name`, `value`, create_time, update_time) " +
            "VALUES (#{taskId}, #{name}, #{value}, #{createTime}, #{updateTime})")
    @Options(useGeneratedKeys = true, keyProperty = "id", keyColumn = "id")
    Long insert(CodePromptTaskExtraEntity codePromptTaskExtra);
    /**
     * 批量插入任务额外数据
     * @param codePromptTaskExtras 任务额外数据列表
     * @return 插入的记录数
     */
    @Insert("<script>" +
            "INSERT INTO ai_coding_prompt_generate_task_extra_data (task_id, `name`, `value`, create_time, update_time) VALUES " +
            "<foreach collection='list' item='item' separator=','>" +
            "(#{item.taskId}, #{item.name}, #{item.value}, #{item.createTime}, #{item.updateTime})" +
            "</foreach>" +
            "</script>")
    @Options(useGeneratedKeys = true, keyProperty = "id", keyColumn = "id")
    int batchInsert(List<CodePromptTaskExtraEntity> codePromptTaskExtras);

    /**
     * 批量插入或更新任务额外数据
     * 根据task_id和name判断数据是否存在，不存在则插入，存在则更新
     * 注意：此方法需要在表上创建唯一索引：UNIQUE KEY uk_task_name (task_id, `name`)
     * @param codePromptTaskExtras 任务额外数据列表
     * @return 影响的记录数
     */
    @Insert("<script>" +
            "INSERT INTO ai_coding_prompt_generate_task_extra_data (task_id, `name`, `value`, create_time, update_time) VALUES " +
            "<foreach collection='list' item='item' separator=','>" +
            "(#{item.taskId}, #{item.name}, #{item.value}, #{item.createTime}, #{item.updateTime})" +
            "</foreach>" +
            " ON DUPLICATE KEY UPDATE " +
            "`value` = VALUES(`value`), " +
            "update_time = VALUES(update_time)" +
            "</script>")
    int batchUpsert(List<CodePromptTaskExtraEntity> codePromptTaskExtras);

    @Select("SELECT * FROM ai_coding_prompt_generate_task_extra_data WHERE id = #{id}")
    @Results(id = "codePromptTaskExtraResult", value = {
            @Result(property = "id", column = "id"),
            @Result(property = "taskId", column = "task_id"),
            @Result(property = "name", column = "name"),
            @Result(property = "value", column = "value"),
            @Result(property = "createTime", column = "create_time"),
            @Result(property = "updateTime", column = "update_time")
    })
    CodePromptTaskExtraEntity findById(Long id);

    @Select("SELECT * FROM ai_coding_prompt_generate_task_extra_data WHERE task_id = #{taskId}")
    @ResultMap("codePromptTaskExtraResult")
    List<CodePromptTaskExtraEntity> findByTaskId(Long taskId);

    @Select("SELECT * FROM ai_coding_prompt_generate_task_extra_data WHERE task_id = #{taskId} AND `name` = #{name}")
    @ResultMap("codePromptTaskExtraResult")
    CodePromptTaskExtraEntity findByTaskIdAndName(@Param("taskId") Long taskId, @Param("name") String name);

    @Update("UPDATE ai_coding_prompt_generate_task_extra_data SET task_id = #{taskId}, `name` = #{name}, `value` = #{value}, " +
            "create_time = #{createTime}, update_time = #{updateTime} WHERE id = #{id}")
    void update(CodePromptTaskExtraEntity codePromptTaskExtra);

    @Delete("DELETE FROM ai_coding_prompt_generate_task_extra_data WHERE id = #{id}")
    void delete(Long id);

    @Delete("DELETE FROM ai_coding_prompt_generate_task_extra_data WHERE task_id = #{taskId}")
    void deleteByTaskId(Long taskId);

    @Delete("DELETE FROM ai_coding_prompt_generate_task_extra_data WHERE task_id = #{taskId} AND `name` = #{name}")
    void deleteByTaskIdAndName(@Param("taskId") Long taskId, @Param("name") String name);
}
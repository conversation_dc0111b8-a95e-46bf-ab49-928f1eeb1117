package com.sankuai.qpro.ai.professor.mapper;

import com.sankuai.qpro.ai.professor.entity.CodePromptTaskEntity;
import org.apache.ibatis.annotations.*;

import java.util.List;

/**
 * @author: wu<PERSON><PERSON><PERSON>
 * @create: 2025-01-01
 * @description: 需求prompt生成任务Mapper
 */
@Mapper
public interface CodePromptTaskMapper {

    @Insert("INSERT INTO ai_coding_prompt_generate_task (user_id, source, stage, title, create_time, update_time) " +
            "VALUES (#{userId}, #{source}, #{stage}, #{title}, #{createTime}, #{updateTime})")
    @Options(useGeneratedKeys = true, keyProperty = "id", keyColumn = "id")
    Long insert(CodePromptTaskEntity codePromptTask);

    @Select("SELECT * FROM ai_coding_prompt_generate_task WHERE id = #{id}")
    @Results(id = "codePromptTaskResult", value = {
            @Result(property = "id", column = "id"),
            @Result(property = "userId", column = "user_id"),
            @Result(property = "source", column = "source"),
            @Result(property = "stage", column = "stage"),
            @Result(property = "title", column = "title"),
            @Result(property = "createTime", column = "create_time"),
            @Result(property = "updateTime", column = "update_time")
    })
    CodePromptTaskEntity findById(Long id);

    @Select("SELECT * FROM ai_coding_prompt_generate_task WHERE id = #{id} AND user_id = #{userId}")
    CodePromptTaskEntity findByIdAndUserId(Long id, String userId);

    @Select("SELECT * FROM ai_coding_prompt_generate_task WHERE user_id = #{userId}")
    @ResultMap("codePromptTaskResult")
    List<CodePromptTaskEntity> findByUserId(String userId);

    @Update("<script>" +
            "UPDATE ai_coding_prompt_generate_task " +
            "<set>" +
            "<if test='userId != null and userId != \"\"'>" +
            "user_id = #{userId}," +
            "</if>" +
            "<if test='source != null'>" +
            "source = #{source}," +
            "</if>" +
            "<if test='stage != null'>" +
            "stage = #{stage}," +
            "</if>" +
            "<if test='title != null'>" +
            "title = #{title}," +
            "</if>" +
            "</set>" +
            "WHERE id = #{id}" +
            "</script>")
    void update(CodePromptTaskEntity codePromptTask);

    @Delete("DELETE FROM ai_coding_prompt_generate_task WHERE id = #{id}")
    void delete(Long id);

    @Delete("DELETE FROM ai_coding_prompt_generate_task WHERE user_id = #{userId}")
    void deleteByUserId(String userId);

    /**
     * 根据用户ID分页查询任务
     * @param userId 用户ID
     * @param offset 偏移量
     * @param limit 每页大小
     * @return 任务列表
     */
    @Select("SELECT * FROM ai_coding_prompt_generate_task WHERE user_id = #{userId} ORDER BY id DESC LIMIT #{limit} OFFSET #{offset}")
    @ResultMap("codePromptTaskResult")
    List<CodePromptTaskEntity> findByUserIdWithPage(@Param("userId") String userId, @Param("offset") int offset, @Param("limit") int limit);

    /**
     * 根据用户ID查询记录数
     * @param userId 用户ID
     * @return 记录数
     */
    @Select("SELECT COUNT(*) FROM ai_coding_prompt_generate_task WHERE user_id = #{userId}")
    long countByUserId(String userId);
}
package com.sankuai.qpro.ai.professor.mapper;

import com.sankuai.qpro.ai.professor.entity.ConfigUnitEntity;
import org.apache.ibatis.annotations.*;

import java.util.List;

/**
 * @author: wuwen<PERSON>ang
 * @create: 2024-12-31
 * @description:
 */
@Mapper
public interface ConfigUnitMapper {

    @Insert("INSERT INTO ai_operator_config_unit (name, description, scene, context, rule, create_time, update_time) " +
            "VALUES (#{name}, #{description}, #{scene}, #{context}, #{rule}, #{createTime}, #{updateTime})")
    @Options(useGeneratedKeys = true, keyProperty = "id", keyColumn = "id")
    Long insert(ConfigUnitEntity configUnitInfo);

    @Select("SELECT * FROM ai_operator_config_unit WHERE id = #{id}")
    @Results(id = "configUnitInfoResult", value = {
            @Result(property = "id", column = "id"),
            @Result(property = "name", column = "name"),
            @Result(property = "description", column = "description"),
            @Result(property = "scene", column = "scene"),
            @Result(property = "context", column = "context"),
            @Result(property = "rule", column = "rule"),
            @Result(property = "createTime", column = "create_time"),
            @Result(property = "updateTime", column = "update_time")
    })
    ConfigUnitEntity findById(Long id);

    @Select("SELECT * FROM ai_operator_config_unit")
    @ResultMap("configUnitInfoResult")
    List<ConfigUnitEntity> findAll();

    @Select("SELECT * FROM ai_operator_config_unit WHERE scene = #{scene}")
    @ResultMap("configUnitInfoResult")
    List<ConfigUnitEntity> findByScene(String scene);

    @Update("UPDATE ai_operator_config_unit SET name = #{name}, description = #{description}, scene = #{scene}, " +
            "context = #{context}, rule = #{rule}, create_time = #{createTime}, update_time = #{updateTime} WHERE id = #{id}")
    void update(ConfigUnitEntity configUnitInfo);

    @Delete("DELETE FROM ai_operator_config_unit WHERE id = #{id}")
    void delete(Long id);
}

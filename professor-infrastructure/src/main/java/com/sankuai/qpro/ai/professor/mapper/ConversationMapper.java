package com.sankuai.qpro.ai.professor.mapper;

import com.sankuai.qpro.ai.professor.entity.ConversationEntity;
import org.apache.ibatis.annotations.*;

import java.util.List;

/**
 * @author: wuwen<PERSON><PERSON>
 * @create: 2024-12-10
 * @description:
 */
@Mapper
public interface ConversationMapper {

    @Insert("INSERT INTO ai_operator_conversation (user_type, user_id, config_unit_id, result, create_time, update_time) " +
            "VALUES (#{userType}, #{userId}, #{configUnitId}, #{result}, #{createTime}, #{updateTime})")
    @Options(useGeneratedKeys = true, keyProperty = "id", keyColumn = "id")
    Long insert(ConversationEntity conversationBaseInfo);

    @Select("SELECT * FROM ai_operator_conversation WHERE id = #{id}")
    @Results(id = "conversationBaseInfoMap", value = {
            @Result(column = "id", property = "id", id = true),
            @Result(column = "user_type", property = "userType"),
            @Result(column = "user_id", property = "userId"),
            @Result(column = "config_unit_id", property = "configUnitId"),
            @Result(column = "result", property = "result"),
            @Result(column = "create_time", property = "createTime"),
            @Result(column = "update_time", property = "updateTime")
    })
    ConversationEntity findById(Long id);

    @Select("SELECT * FROM ai_operator_conversation")
    @ResultMap("conversationBaseInfoMap")
    List<ConversationEntity> findAll();

    @Update("UPDATE ai_operator_conversation SET user_type = #{userType}, user_id = #{userId}, config_unit_id = #{configUnitId}, " +
            "result = #{result}, create_time = #{createTime}, update_time = #{updateTime} WHERE id = #{id}")
    void update(ConversationEntity conversationBaseInfo);

    @Delete("DELETE FROM ai_operator_conversation WHERE id = #{id}")
    void delete(Long id);
}

package com.sankuai.qpro.ai.professor.mapper;

import com.sankuai.qpro.ai.professor.entity.ConversationMsgEntity;
import org.apache.ibatis.annotations.*;

import java.util.List;

/**
 * @author: wuwen<PERSON>ang
 * @create: 2024-12-10
 * @description:
 */
@Mapper
public interface ConversationMsgMapper {
    @Insert("INSERT INTO ai_operator_conversation_msg (conversation_id, role, message, flag, review_type, dialog_segment, status, create_time, update_time) " +
            "VALUES (#{conversationId}, #{role}, #{message}, #{flag}, #{reviewType}, #{dialogSegment}, #{status}, #{createTime}, #{updateTime})")
    @Options(useGeneratedKeys = true, keyProperty = "id", keyColumn = "id")
    Long insert(ConversationMsgEntity conversationHistory);

    @Insert({
            "<script>",
            "INSERT INTO ai_operator_conversation_msg (conversation_id, role, message, flag, review_type, dialog_segment, status, create_time, update_time) VALUES ",
            "<foreach collection='list' item='item' index='index' separator=','>",
            "(#{item.conversationId}, #{item.role}, #{item.message}, #{item.flag}, #{item.reviewType}, #{item.dialogSegment}, #{item.status}, #{item.createTime}, #{item.updateTime})",
            "</foreach>",
            "</script>"
    })
    @Options(useGeneratedKeys = true, keyProperty = "id", keyColumn = "id")
    int batchInsert(@Param("list") List<ConversationMsgEntity> conversationHistories);

    @Select("SELECT * FROM ai_operator_conversation_msg WHERE id = #{id}")
    @Results(id = "conversationHistoryResult", value = {
            @Result(property = "id", column = "id"),
            @Result(property = "conversationId", column = "conversation_id"),
            @Result(property = "role", column = "role"),
            @Result(property = "message", column = "message"),
            @Result(property = "flag", column = "flag"),
            @Result(property = "reviewType", column = "review_type"),
            @Result(property = "dialogSegment", column = "dialog_segment"),
            @Result(property = "status", column = "status"),
            @Result(property = "createTime", column = "create_time"),
            @Result(property = "updateTime", column = "update_time")
    })
    ConversationMsgEntity findById(Long id);

    @Select("SELECT * FROM ai_operator_conversation_msg WHERE conversation_id = #{conversationId}")
    @ResultMap("conversationHistoryResult")
    List<ConversationMsgEntity> findByConversationId(Long conversationId);

    @Select("SELECT * FROM ai_operator_conversation_msg WHERE conversation_id = #{conversationId} ORDER BY id DESC LIMIT 1")
    @ResultMap("conversationHistoryResult")
    ConversationMsgEntity findLatestMsgByConversationId(Long conversationId);

    @Select("SELECT * FROM ai_operator_conversation_msg WHERE conversation_id = #{conversationId} AND dialog_segment = #{dialogSegment} ORDER BY id DESC LIMIT 1")
    @ResultMap("conversationHistoryResult")
    ConversationMsgEntity findLatestSegment(Long conversationId, String dialogSegment);

    @Select("SELECT * FROM ai_operator_conversation_msg WHERE conversation_id = #{conversationId} AND id >= #{startId}")
    @ResultMap("conversationHistoryResult")
    List<ConversationMsgEntity> findByConversationIdAndStartId(Long conversationId, Long startId);

    @Select("SELECT * FROM ai_operator_conversation_msg WHERE conversation_id = #{conversationId} AND role = #{role} AND status = #{status} AND dialog_segment != 'error' ORDER BY id DESC LIMIT 1")
    @ResultMap("conversationHistoryResult")
    ConversationMsgEntity findLatestMsg(Long conversationId, int role, int status);

    @Update("UPDATE ai_operator_conversation_msg SET conversation_id = #{conversationId}, role = #{role}, message = #{message}, " +
            "flag = #{flag}, review_type = #{reviewType}, dialog_segment = #{dialogSegment}, status = #{status}, create_time = #{createTime}, update_time = #{updateTime} WHERE id = #{id}")
    void update(ConversationMsgEntity conversationHistory);

    @Update("UPDATE ai_operator_conversation_msg SET review_type = #{reviewType} WHERE id = #{id}")
    void updateReviewType(Long id, Integer reviewType);

    @Delete("DELETE FROM ai_operator_conversation_msg WHERE id = #{id}")
    void delete(Long id);

}

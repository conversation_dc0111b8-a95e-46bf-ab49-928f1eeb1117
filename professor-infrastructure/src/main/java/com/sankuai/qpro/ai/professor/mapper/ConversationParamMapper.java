package com.sankuai.qpro.ai.professor.mapper;

import com.sankuai.qpro.ai.professor.entity.ConversationParamEntity;
import org.apache.ibatis.annotations.*;

/**
 * <AUTHOR>
 * @date 2025/1/14
 */
@Mapper
public interface ConversationParamMapper {

    @Insert("INSERT INTO ai_operator_conversation_param (conversation_id, config_unit_name, stage, poi_category_ids, poi_category_names, product_category_id, product_category_name, exp_ids) " +
            "VALUES (#{conversationId}, #{configUnitName}, #{stage}, #{poiCategoryIds}, #{poiCategoryNames}, #{productCategoryId}, #{productCategoryName}, #{expIds})")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    Long insert(ConversationParamEntity param);

    @Select("SELECT * FROM ai_operator_conversation_param WHERE conversation_id = #{conversationId}")
    @Results({
            @Result(property = "id", column = "id"),
            @Result(property = "conversationId", column = "conversation_id"),
            @Result(property = "configUnitName", column = "config_unit_name"),
            @Result(property = "stage", column = "stage"),
            @Result(property = "poiCategoryIds", column = "poi_category_ids"),
            @Result(property = "poiCategoryNames", column = "poi_category_names"),
            @Result(property = "productCategoryId", column = "product_category_id"),
            @Result(property = "productCategoryName", column = "product_category_name"),
            @Result(property = "expIds", column = "exp_ids"),
            @Result(property = "strategy", column = "strategy")
    })
    ConversationParamEntity selectByConversationId(Long conversationId);

    @Update({
        "UPDATE ai_operator_conversation_param",
        "SET config_unit_name = #{configUnitName},",
        "    stage = #{stage},",
        "    poi_category_ids = #{poiCategoryIds},",
        "    poi_category_names = #{poiCategoryNames},",
        "    product_category_id = #{productCategoryId},",
        "    product_category_name = #{productCategoryName},",
        "    exp_ids = #{expIds},",
        "    strategy = #{strategy}",
        "WHERE id = #{id}"
    })
    void update(ConversationParamEntity param);
}


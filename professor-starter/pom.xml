<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.sankuai.qpro.ai</groupId>
        <artifactId>professor</artifactId>
        <version>${revision}</version>
    </parent>

    <artifactId>professor-starter</artifactId>
    <version>${revision}</version>
    <packaging>jar</packaging>
    <name>professor-starter</name>

    <dependencies>
        <!-- Project module -->
        <dependency>
            <groupId>com.sankuai.qpro.ai</groupId>
            <artifactId>professor-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sankuai.qpro.ai</groupId>
            <artifactId>professor-application</artifactId>
        </dependency>

        <!-- region MDP 脚手架生成代码统计埋点 内容为空，不引入其他依赖，请勿删除 -->
        <!-- MDP LA Initializer -->
        <dependency>
            <groupId>com.sankuai.mdp</groupId>
            <artifactId>mdp-boot-initializr-mdp</artifactId>
            <version>1.0.0</version>
        </dependency>
        <!-- endregion MDP 脚手架生成代码统计埋点 -->

        <!-- Mdp Boot Starter -->
        <dependency>
            <groupId>com.meituan.mdp.boot</groupId>
            <artifactId>mdp-boot-starter</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>mdp-boot-starter-bean-copy</artifactId>
                    <groupId>com.meituan.mdp.boot</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.meituan.mdp.component</groupId>
            <artifactId>mdp-doc</artifactId>
            <optional>true</optional>
        </dependency>

        <!-- 第三方依赖 -->
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-collections4</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
        </dependency>
        <dependency>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpcore</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-annotations</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-databind</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.meituan.mdp.boot</groupId>
            <artifactId>mdp-boot-starter-web</artifactId>
        </dependency>
        <dependency>
            <groupId>com.meituan.mdp.boot</groupId>
            <artifactId>mdp-boot-starter-zebra</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-webflux</artifactId>
        </dependency>
        <dependency>
            <groupId>com.meituan.mdp.boot</groupId>
            <artifactId>mdp-boot-starter-squirrel</artifactId>
        </dependency>
        <dependency>
            <groupId>edu.stanford.nlp</groupId>
            <artifactId>stanford-corenlp</artifactId>
        </dependency>
        <!--   MCP配置     -->
        <!--   本地MCP     -->
        <dependency>
            <groupId>com.meituan.mdp.ai</groupId>
            <artifactId>mdp-ai-starter-mcp-server</artifactId>
        </dependency>
        <!--   远程MCP     -->
        <!--   基于webflux开发  -->
<!--        <dependency>-->
<!--            <groupId>com.meituan.mdp.ai</groupId>-->
<!--            <artifactId>mdp-ai-starter-mcp-server-webflux</artifactId>-->
<!--        </dependency>-->
        <dependency>
            <groupId>com.meituan.mdp.ai</groupId>
            <artifactId>mdp-ai-starter-mcp-server-webmvc</artifactId>
        </dependency>
    </dependencies>

    <build>
        <finalName>${project.artifactId}</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <configuration>
                    <jvmArguments>-ea --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.lang.reflect=ALL-UNNAMED --add-opens=java.base/java.nio=ALL-UNNAMED --add-opens=java.base/java.text=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.util.concurrent=ALL-UNNAMED --add-exports=java.base/sun.reflect.generics.reflectiveObjects=ALL-UNNAMED --add-exports=java.base/sun.net.util=ALL-UNNAMED</jvmArguments>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.mybatis.generator</groupId>
                <artifactId>mybatis-generator-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>com.meituan.mdp.maven.plugins</groupId>
                <artifactId>mdp-mybatis-generator-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>com.meituan.mdp.maven.plugins</groupId>
                <artifactId>mdp-graphql-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>com.meituan.mdp.maven.plugins</groupId>
                <artifactId>mdp-statistics-maven-plugin</artifactId>
                <executions>
                    <execution>
                        <id>register</id>
                        <phase>compile</phase>
                        <goals>
                            <goal>make</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

</project>
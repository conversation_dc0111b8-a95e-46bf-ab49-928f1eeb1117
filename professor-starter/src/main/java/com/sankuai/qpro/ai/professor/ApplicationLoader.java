package com.sankuai.qpro.ai.professor;

import com.meituan.mdp.boot.starter.MdpContextUtils;
import com.sankuai.qpro.ai.professor.application.code.ast.resolver.AstResolver;
import com.sankuai.qpro.ai.professor.application.utils.ProjectMappingUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.boot.CommandLineRunner;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.transaction.annotation.EnableTransactionManagement;

import java.util.Collection;
import java.util.List;

@SpringBootApplication(exclude={DataSourceAutoConfiguration.class})
@EnableTransactionManagement
@Slf4j
public class ApplicationLoader {

    public static void main(String[] args) {
        SpringApplication application = new SpringApplication(ApplicationLoader.class);
        application.setAdditionalProfiles(MdpContextUtils.getHostEnvStr());
        application.run(args);
    }

    @Bean
    public CommandLineRunner initProjectIndex() {
        return args -> {
            log.info("ast parse start");
            Collection<String> projectPathList = ProjectMappingUtils.PROJECT_PATH_MAPPING.values();
            if(!CollectionUtils.isEmpty(projectPathList)) {
                projectPathList.forEach(e -> {
                    AstResolver.getInstance().parseProjectAst(e);
                    log.info("ast parse success, {}", e);
                });
            }
        };
    }

}
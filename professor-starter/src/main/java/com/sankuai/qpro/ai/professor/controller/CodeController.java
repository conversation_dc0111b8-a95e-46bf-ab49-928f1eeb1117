package com.sankuai.qpro.ai.professor.controller;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Maps;
import com.sankuai.ee.mcode.analyze.api.thrift.dto.ClassInfoDTO;
import com.sankuai.ee.mcode.analyze.api.thrift.dto.MethodDefWithoutInvocationsDTO;
import com.sankuai.ee.mcode.analyze.api.thrift.response.ProjectClassesResponse;
import com.sankuai.ee.mcode.analyze.api.thrift.service.AnalysisProjectThriftService;
import com.sankuai.qpro.ai.professor.api.enums.ResponseCodeEnum;
import com.sankuai.qpro.ai.professor.api.response.RemoteResponse;
import com.sankuai.qpro.ai.professor.application.code.TopologyService;
import com.sankuai.qpro.ai.professor.application.code.agent.AgentMan;
import com.sankuai.qpro.ai.professor.application.code.agent.consts.Consts;
import com.sankuai.qpro.ai.professor.application.code.agent.state.ResearchState;
import com.sankuai.qpro.ai.professor.application.code.agent.utils.JsonUtils;
import com.sankuai.qpro.ai.professor.application.code.pojo.CallTopology;
import com.sankuai.qpro.ai.professor.application.code.tools.CodeTools;
import com.sankuai.qpro.ai.professor.utils.MatcherUtils;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.bsc.async.AsyncGenerator;
import org.bsc.langgraph4j.CompiledGraph;
import org.bsc.langgraph4j.GraphInput;
import org.bsc.langgraph4j.NodeOutput;
import org.bsc.langgraph4j.RunnableConfig;
import org.bsc.langgraph4j.state.StateSnapshot;
import org.bsc.langgraph4j.streaming.StreamingOutput;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.chat.messages.AssistantMessage;
import org.springframework.ai.chat.messages.UserMessage;
import org.springframework.ai.chat.model.ChatResponse;
import org.springframework.ai.chat.prompt.ChatOptions;
import org.springframework.ai.chat.prompt.Prompt;
import org.springframework.ai.mcp.SyncMcpToolCallbackProvider;
import org.springframework.ai.model.tool.ToolCallingChatOptions;
import org.springframework.ai.model.tool.ToolCallingManager;
import org.springframework.ai.model.tool.ToolExecutionResult;
import org.springframework.ai.support.ToolCallbacks;
import org.springframework.ai.tool.ToolCallback;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.codec.ServerSentEvent;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Sinks;

import javax.annotation.PreDestroy;
import javax.annotation.Resource;
import java.time.Duration;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.*;

/**
 * CodeController
 *
 * <AUTHOR>
 * @since 2025/5/29
 */
@RestController
@RequestMapping("/api/code")
@Slf4j
public class CodeController {

    @Autowired
    private TopologyService topologyService;

    @Resource
    private AnalysisProjectThriftService analysisProjectThriftService;

    private final ChatClient chatClient;

    private static final String BASEURL = "http://10.98.14.201:8080/ai/probe/chat/api/code";

    @Autowired
    private SyncMcpToolCallbackProvider toolCallbackProvider;

    private final WebClient webClient = WebClient.builder()
            .codecs(configurer -> configurer.defaultCodecs().maxInMemorySize(20 * 1024 * 1024))
            .build();

    private final ObjectMapper objectMapper = new ObjectMapper();
    /**
     * 执行器
     */
    final CompiledGraph<ResearchState> codeSearchAgent;


    public CodeController(ChatClient.Builder builder, AgentMan agentMan) throws Throwable {
        this.chatClient = builder.build();
        this.codeSearchAgent = agentMan.codeSearch();
    }

    @SneakyThrows
    @GetMapping(value = "/project/classes")
    public RemoteResponse<?> classes(@RequestParam("repo") String repo) {
        return RemoteResponse.success(analysisProjectThriftService.listJavaProjectClasses(repo, null));
    }

    @SneakyThrows
    @GetMapping(value = "/project/classes/expression")
    public RemoteResponse<?> classesWithExpression(@RequestParam("repo") String repo, @RequestParam("expression") String expression) {
        ProjectClassesResponse projectClassesResponse = analysisProjectThriftService.listJavaProjectClasses(repo, null);
        Map<String, ClassInfoDTO> filteredClasses = new HashMap<>();
        projectClassesResponse.getClasses().forEach((k, v)-> {
            if(k.startsWith(expression)){
                filteredClasses.put(k, v);
            }
        });
        return RemoteResponse.success(filteredClasses);
    }

    @SneakyThrows
    @GetMapping(value = "/project/classes/expression/local")
    public RemoteResponse<?> classesWithExpressionLocal(@RequestParam("repo") String repo, @RequestParam("expression") String expression) {
        ProjectClassesResponse projectClassesResponse = null;
        try {
            // 获取HTTP响应的JSON字符串
            String url = BASEURL + "/project/classes?repo=" + repo;
            String jsonResponse = getHttpResponse(url);

            if (jsonResponse == null || jsonResponse.startsWith("HTTP请求失败")) {
                log.warn("HTTP请求失败");
                return new RemoteResponse<>();
            }
            JsonNode root = objectMapper.readTree(jsonResponse);
            JsonNode dataNode = root.get("data");
            projectClassesResponse = objectMapper.convertValue(dataNode,new TypeReference<ProjectClassesResponse>() {});

        } catch (Exception e) {
            log.error("解析HTTP响应JSON失败: {}", e.getMessage(), e);
            return new RemoteResponse<>();
        }
        Map<String, ClassInfoDTO> filteredClasses = new HashMap<>();
        projectClassesResponse.getClasses().forEach((k, v)-> {
            if(MatcherUtils.matcher(k, expression)){
                filteredClasses.put(k, v);
            }
        });
        return RemoteResponse.success(filteredClasses);
    }

    @SneakyThrows
    @GetMapping(value = "/project/methods")
    public RemoteResponse<?> methods(@RequestParam("repo") String repo) {
        return RemoteResponse.success(analysisProjectThriftService.listJavaProjectMethodsWithoutInvocations(repo, null));
    }

    @SneakyThrows
    @GetMapping(value = "/project/methods/expression")
    public RemoteResponse<?> methodsWithExpression(@RequestParam("repo") String repo, @RequestParam("expression") String expression) {
        Map<String, MethodDefWithoutInvocationsDTO> stringMethodDef = analysisProjectThriftService.listJavaProjectMethodsWithoutInvocations(repo, null);
        Map<String, MethodDefWithoutInvocationsDTO> filteredMethods = new HashMap<>();
        stringMethodDef.forEach((k, v) -> {
            if (MatcherUtils.matcher(k, expression)) {
                filteredMethods.put(k, v);
            }
        });
        return RemoteResponse.success(filteredMethods);
    }
    @SneakyThrows
    @GetMapping(value = "/project/methods/expression/local")
    public RemoteResponse<?> methodsWithExpressionLocal(@RequestParam("repo") String repo, @RequestParam("expression") String expression) {
        Map<String, MethodDefWithoutInvocationsDTO> stringMethodDef = null;
        try {
            // 获取HTTP响应的JSON字符串
            String url = BASEURL + "/project/methods?repo=" + repo;
            String jsonResponse = getHttpResponse(url);

            if (jsonResponse == null || jsonResponse.startsWith("HTTP请求失败")) {
                log.warn("HTTP请求失败");
                return new RemoteResponse<>();
            }
            JsonNode root = objectMapper.readTree(jsonResponse);
            JsonNode dataNode = root.get("data");
            stringMethodDef = objectMapper.convertValue(dataNode,new TypeReference<Map<String, MethodDefWithoutInvocationsDTO>>() {});

        } catch (Exception e) {
            log.error("解析HTTP响应JSON失败: {}", e.getMessage(), e);
            return new RemoteResponse<>();
        }
        Map<String, MethodDefWithoutInvocationsDTO> filteredMethods = new HashMap<>();
        stringMethodDef.forEach((k, v) -> {
            if (MatcherUtils.matcher(k, expression)) {
                filteredMethods.put(k, v);
            }
        });
        return RemoteResponse.success(filteredMethods);
    }

    @SneakyThrows
    @GetMapping(value = "/project/call-graph")
    public RemoteResponse<?> methods(@RequestParam("repo") String repo, @RequestParam("method-signature") String methodSignature) {
        return RemoteResponse.success(analysisProjectThriftService.getJavaProjectCallGraphByRootMethod(repo, null, methodSignature));
    }

    @PostMapping(value = "/analysis/create")
    public RemoteResponse<Boolean> create(@RequestParam("repo") String repo) {
        return RemoteResponse.success(topologyService.createAnalysisTask(repo));
    }

    @GetMapping(value = "/topology/query")
    public RemoteResponse<CallTopology> queryCallTopology(@RequestParam("repo") String repo, @RequestParam("method-signature") String methodSignature) {
        return RemoteResponse.success(topologyService.queryCallTopology(repo, methodSignature));
    }

    @GetMapping(value = "/topology/query/new")
    public RemoteResponse<Map<String, List<String>>> queryCallTopologyNew(@RequestParam("repo") String repo, @RequestParam("method-signature") String methodSignature) {
        return RemoteResponse.success(topologyService.queryCallTopologyNew(repo, methodSignature));
    }

    @GetMapping(value = "/topology/query/new/expression")
    public RemoteResponse<Map<String, List<String>>> queryCallTopologyNew(@RequestParam("repo") String repo, @RequestParam("method-signature") String methodSignature, @RequestParam("expression") String expression) {
        Map<String, List<String>> resp = topologyService.queryCallTopologyNew(repo, methodSignature);
        Map<String, List<String>> finalResp = new HashMap<>();
        resp.forEach((k, v) -> {
            if (MatcherUtils.matcher(k, expression)) {
                finalResp.put(k,v);
            }
        });
        return RemoteResponse.success(finalResp);
    }
    @GetMapping(value = "/topology/query/new/expression/local")
    public RemoteResponse<Map<String, List<String>>> queryCallTopologyNewLocal(@RequestParam("repo") String repo, @RequestParam("method-signature") String methodSignature, @RequestParam("expression") String expression) {
        Map<String, List<String>> resp = null;
        try {
            // 获取HTTP响应的JSON字符串
            String url = BASEURL + "/topology/query/new?repo=" + repo + "&method-signature=" + methodSignature;
            String jsonResponse = getHttpResponse(url);

            if (jsonResponse == null || jsonResponse.startsWith("HTTP请求失败")) {
                log.warn("HTTP请求失败");
                return new RemoteResponse<>();
            }
            JsonNode root = objectMapper.readTree(jsonResponse);
            JsonNode dataNode = root.get("data");
            resp = objectMapper.convertValue(dataNode,new TypeReference<Map<String, List<String>>>() {});

        } catch (Exception e) {
            log.error("解析HTTP响应JSON失败: {}", e.getMessage(), e);
            return new RemoteResponse<>();
        }
        Map<String, List<String>> finalResp = new HashMap<>();
        resp.forEach((k, v) -> {
            if (MatcherUtils.matcher(k, expression)) {
                finalResp.put(k, v);
            }
        });
        return RemoteResponse.success(finalResp);
    }

    @GetMapping(value = "/code-tools")
    public RemoteResponse<?> testCodeTools(@RequestParam(value = "user-message", defaultValue = "请找到仓库 ssh://*******************/vc/dzviewscene-dealshelf-home.git 的 com.sankuai.dzviewscene.product.shelf.ability.fetcher.productpactivity.ProductActivitiesFetcher.build(com.sankuai.athena.viewscene.framework.ActivityCxt,com.sankuai.dzviewscene.product.shelf.ability.fetcher.productpactivity.ProductActivitiesFetcher$Request,com.sankuai.dzviewscene.product.shelf.ability.fetcher.productpactivity.ProductActivitiesFetcher$Config) 下3层的调用链路") String userMessage) {
        ChatOptions chatOptions = ToolCallingChatOptions.builder()
                .toolCallbacks(ToolCallbacks.from(new CodeTools(topologyService, analysisProjectThriftService)))
//                .toolCallbacks(toolCallbackProvider.getToolCallbacks())
                .internalToolExecutionEnabled(false)
                .build();

        ToolCallingManager toolCallingManager = ToolCallingManager.builder().build();
//        Prompt prompt = new Prompt(new UserMessage("""
//                请找到仓库 ssh://*******************/vc/dzviewscene-dealshelf-home.git 的 com.sankuai.AppContextConfiguration 的代码
//                """), chatOptions);
        Prompt prompt = new Prompt(new UserMessage(userMessage), chatOptions);
//        Prompt prompt = new Prompt(new UserMessage("""
//                请找到仓库 ssh://*******************/vc/dzviewscene-dealshelf-home.git 的 com.dianping.mobile.mapi.dztgdetail.faulttolerance.deal.executor.DzDealBaseExecutor.execute(com.dianping.mobile.mapi.dztgdetail.faulttolerance.req.DealBaseContextRequest) 下排除{com.dianping.mobile.mapi.dztgdetail.util.dinner.DinnerDealUtils}的调用链路
//                """), chatOptions);
        ChatResponse response = chatClient
                .prompt(prompt)
                .call()
                .chatResponse();
        AssistantMessage assistantMessage = response.getResult().getOutput();
        // 判断是否触发工具调用
        if (!assistantMessage.getToolCalls().isEmpty()) {
            ToolExecutionResult toolExecutionResult = toolCallingManager.executeToolCalls(prompt, response);
            return RemoteResponse.success(ToolExecutionResult.buildGenerations(toolExecutionResult));
        } else {
            return RemoteResponse.fail(ResponseCodeEnum.SERVER_ERROR, "工具没有执行");
        }
    }

    /**
     * 查询指定深度的调用链路
     * @param repo
     * @param methodSignature
     * @param depth
     * @return  指定深度的调用链路（拓扑形式）
     */
    @GetMapping(value = "/topology/query/depth")
    public RemoteResponse<CallTopology> queryCallTopologyWithDepth(@RequestParam("repo") String repo, @RequestParam("method-signature") String methodSignature, @RequestParam("depth") Integer depth) {
        return RemoteResponse.success(topologyService.queryCallTopologyWithDepth(repo, methodSignature, depth));
    }

    /**
     * 查询指定深度的调用链路
     * @param repo
     * @param methodSignature
     * @param depth
     * @return  指定深度的调用链路中所有类名与其方法列表的映射
     */
    @GetMapping(value = "/topology/query/new/depth")
    public RemoteResponse<Map<String, List<String>>> queryCallTopologyNewWithDepth(@RequestParam("repo") String repo, @RequestParam("method-signature") String methodSignature, @RequestParam("depth") Integer depth) {
        return RemoteResponse.success(topologyService.queryCallTopologyNewWithDepth(repo, methodSignature, depth));
    }

    @GetMapping(value = "/topology/query/new/exclude")
    public RemoteResponse<Map<String, List<String>>> queryCallTopologyNewWithExcludeClass(@RequestParam("repo") String repo, @RequestParam("method-signature") String methodSignature, @RequestParam("excludeClasses") List<String> excludeClasses) {
        return RemoteResponse.success(topologyService.queryCallTopologyNewWithExcludeClass(repo, methodSignature, excludeClasses));
    }

    @GetMapping(value = "/search", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public Flux<ServerSentEvent<String>> start(@RequestParam(value = "requirement", defaultValue = "页面整体布局：整体白底设计，Tab栏和筛选栏橙色及灰色区分，商品卡片一行一个，顶部信息包含门店信息、距离、商场名称、神券信息等，底部信息有评价区块、写评价赢积分等。") String requirement, @RequestParam(value = "query", defaultValue = "查找在主标题构造（UnifiedShelfMainTitleBuilder）中，如何配置顶部信息区块（门店信息、距离、商场名称、神券信息等）的样式与数据组装，以及如何支持不同风格和布局的主标题渲染。") String query, @RequestParam(value = "key-code-entrance", defaultValue = "UnifiedShelfMainTitleBuilder") String keyCodeEntrance,
                                               @RequestParam(value = "thread-id", defaultValue = "default") String threadId) {
        RunnableConfig runnableConfig = RunnableConfig.builder().threadId(threadId).build();
        AsyncGenerator<NodeOutput<ResearchState>> ag;
        Optional<StateSnapshot<ResearchState>> state = codeSearchAgent.stateOf(runnableConfig);
        if (state.isPresent()) {
            // 存在之前的状态，从中断点继续执行
            log.info("找到已存在的会话：{}, 从断点处恢复", threadId);
            ag = codeSearchAgent.stream(GraphInput.resume(), runnableConfig);
        } else {
            // 没有之前的状态，开始新的执行
            log.info("没有存在的会话：{}, 开始执行", threadId);
            ag = codeSearchAgent.stream(Map.of(
                    Consts.ConstsState.REQUIREMENT, requirement,
                    Consts.ConstsState.QUERY, query,
                    Consts.ConstsState.KEY_CODE_ENTRANCE, keyCodeEntrance
            ), runnableConfig);
        }
        // 流式输出
        Sinks.Many<ServerSentEvent<String>> sink = Sinks.many().unicast().onBackpressureBuffer();
        processStream(ag, sink);
        return sink.asFlux().doOnCancel(() -> log.info("Client disconnected from stream")).doOnError(e -> log.error("Error occurred during streaming", e));
    }

    private void processStream(AsyncGenerator<NodeOutput<ResearchState>> generator, Sinks.Many<ServerSentEvent<String>> sink) {
        THREAD_POOL_EXECUTOR.submit(() -> {
            generator.forEachAsync(output -> {
                try {
                    String nodeName = output.node();
                    String content;
                    if (output instanceof StreamingOutput<ResearchState> streamingOutput) {
                        content = JsonUtils.toJson(Map.of(nodeName, streamingOutput.chunk()));
                        log.info("Streaming output from node {}: {}", nodeName, streamingOutput.chunk());
                    } else {
                        Map<String, Object> nodeOutput = Maps.newConcurrentMap();
                        nodeOutput.put("data", output.state().data());
                        nodeOutput.put("node", nodeName);
                        content = JsonUtils.toJson(nodeOutput);
                    }
                    sink.tryEmitNext(ServerSentEvent.builder(content).build());
                } catch (Exception e) {
                    log.error("Error processing output", e);
                    throw new CompletionException(e);
                }
            }).thenAccept(v -> {
                // 正常完成
                sink.tryEmitComplete();
            }).exceptionally(e -> {
                log.error("Error in stream processing", e);
                sink.tryEmitError(e);
                return null;
            });
        });
    }

    /**
     * 使用可配置的线程池替代单线程执行器，核心线程数基于CPU核心数
     */
    private final ThreadPoolExecutor THREAD_POOL_EXECUTOR = new ThreadPoolExecutor(
            // 核心线程数设置为CPU核心数
            Runtime.getRuntime().availableProcessors(),
            // 最大线程数设置为CPU核心数的2倍
            Runtime.getRuntime().availableProcessors() * 2 + 1,
            // 空闲线程存活时间
            60L,
            // 时间单位
            TimeUnit.SECONDS,
            // 工作队列
            new LinkedBlockingQueue<>(100), Executors.defaultThreadFactory(),
            // 拒绝策略：由调用线程处理
            new ThreadPoolExecutor.CallerRunsPolicy());

    @PreDestroy
    public void shutdown() {
        log.info("Shutting down executor service");
        THREAD_POOL_EXECUTOR.shutdown();
        try {
            int timeout = 60;
            if (!THREAD_POOL_EXECUTOR.awaitTermination(timeout, TimeUnit.SECONDS)) {
                THREAD_POOL_EXECUTOR.shutdownNow();
                if (!THREAD_POOL_EXECUTOR.awaitTermination(timeout, TimeUnit.SECONDS)) {
                    log.error("Executor did not terminate");
                }
            }
        } catch (InterruptedException e) {
            THREAD_POOL_EXECUTOR.shutdownNow();
            Thread.currentThread().interrupt();
        }
    }
    public String getHttpResponse(String url) {
        try {
            String response = webClient.get()
                    .uri(url)
                    .retrieve()
                    .bodyToMono(String.class)
                    .timeout(Duration.ofSeconds(30))
                    .block();
            return response;

        } catch (Exception e) {
            return "HTTP请求失败: " + e.getMessage();
        }
    }

}
package com.sankuai.qpro.ai.professor.controller;

import com.sankuai.qpro.ai.professor.application.service.operator.probe.chat.ConfigUnitManageService;
import com.sankuai.qpro.ai.professor.api.enums.ResponseCodeEnum;
import com.sankuai.qpro.ai.professor.api.response.ConfigUnitResponse;
import com.sankuai.qpro.ai.professor.api.response.RemoteResponse;
import com.sankuai.qpro.ai.professor.api.response.ConfigUnitModel;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * @author: wu<PERSON><PERSON><PERSON>
 * @create: 2024-12-31
 * @description:
 */
@RestController
@RequestMapping("/api/configunit")
@Slf4j
public class ConfigUnitController {

    @Autowired
    private ConfigUnitManageService configUnitManageService;

    @RequestMapping(value="/search", produces = "application/json", method = RequestMethod.GET)
    public RemoteResponse<ConfigUnitResponse> searchConfigUnit(@RequestParam(required = false) String scene) {
        List<ConfigUnitModel> configUnitModels = null;
        try {
            if (StringUtils.isNotBlank(scene)) {
                configUnitModels = configUnitManageService.queryConfigUnits(scene);
            } else {
                configUnitModels = configUnitManageService.queryConfigUnits();
            }
            return RemoteResponse.success(new ConfigUnitResponse(configUnitModels));
        } catch (Exception e) {
            log.error("[ConfigUnitController] searchConfigUnit error, scene={}", scene, e);
        }
        return RemoteResponse.fail(ResponseCodeEnum.SERVER_ERROR, ResponseCodeEnum.SERVER_ERROR.getName());
    }
}

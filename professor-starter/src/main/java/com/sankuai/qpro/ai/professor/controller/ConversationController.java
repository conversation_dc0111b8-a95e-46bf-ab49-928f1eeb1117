package com.sankuai.qpro.ai.professor.controller;

import com.google.common.base.Preconditions;
import com.google.gson.JsonObject;
import com.sankuai.qpro.ai.professor.application.model.operator.probe.chat.AgentChatModel;
import com.sankuai.qpro.ai.professor.application.service.operator.probe.chat.AgentChatManager;
import com.sankuai.qpro.ai.professor.application.service.operator.probe.chat.AgentChatService;
import com.sankuai.qpro.ai.professor.application.service.operator.probe.chat.ConversationStoreService;
import com.sankuai.qpro.ai.professor.application.utils.GsonUtils;
import com.sankuai.qpro.ai.professor.api.enums.MessageTypeEnum;
import com.sankuai.qpro.ai.professor.api.enums.ResponseCodeEnum;
import com.sankuai.qpro.ai.professor.api.enums.SseEventTypeEnum;
import com.sankuai.qpro.ai.professor.api.request.CreateConversationRequest;
import com.sankuai.qpro.ai.professor.api.request.SendMessageRequest;
import com.sankuai.qpro.ai.professor.api.response.CreateConversationResponse;
import com.sankuai.qpro.ai.professor.api.response.RemoteResponse;
import lombok.extern.slf4j.Slf4j;
import com.sankuai.qpro.ai.professor.api.request.MsgReviewRequest;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.codec.ServerSentEvent;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Flux;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;

/**
 * @author: wuwenqiang
 * @create: 2024-12-10
 * @description:
 */
@RestController
@RequestMapping("/api/conversation")
@Slf4j
public class ConversationController {

    @Autowired
    private AgentChatService agentChatService;
    @Autowired
    private ConversationStoreService conversationStoreService;

    @RequestMapping(value="/create", produces = "application/json", method = RequestMethod.POST)
    public RemoteResponse<CreateConversationResponse> createConversation(@RequestBody(required = false) CreateConversationRequest request, HttpServletRequest httpServletRequest) {
        if (Objects.isNull(request) || Objects.isNull(httpServletRequest)) {
            return RemoteResponse.invalidParam(ResponseCodeEnum.INVALID_REQUEST.getName());
        }
        try {
            validRequest(request, httpServletRequest);
        } catch (Exception e) {
            return RemoteResponse.invalidParam(e.getMessage());
        }
        try {
            CreateConversationResponse response = conversationStoreService.createConversation(request);
            return RemoteResponse.success(response);
        } catch (Exception e) {
            log.error("[ConversationController] createConversation error, request:{}", GsonUtils.toJsonString(request), e);
            return RemoteResponse.fail(ResponseCodeEnum.SERVER_ERROR, ResponseCodeEnum.SERVER_ERROR.getName());
        }
    }

    @RequestMapping(value="/sendMessage", produces = "application/json", method = RequestMethod.POST)
    public Flux<ServerSentEvent<String>> sendMessage(@RequestBody SendMessageRequest request, HttpServletRequest httpServletRequest, HttpServletResponse response) {
        return Flux.create(sink -> CompletableFuture.runAsync(() -> {
            try {
                // 开启响应
                AgentChatManager.init(sink);
                // 校验请求
                validRequest(request, httpServletRequest);
                Preconditions.checkArgument(request.getConversationId() != null && request.getConversationId() > 0, "当前会话ID无效") ;
                // 流开始标识
                AgentChatManager.send(AgentChatManager.generateContentMsg("start"), SseEventTypeEnum.START.getEventType());
                AgentChatModel outputModel = agentChatService.chat(request);
                if (Objects.isNull(outputModel) || StringUtils.isBlank(outputModel.getMsg())) {
                    throw new RuntimeException("aiMessage is null");
                }
                String content = outputModel.getMsg();
                // 模拟流式，正式文本
                AgentChatManager.simulateSendStream(content, SseEventTypeEnum.MESSAGE.getEventType(), MessageTypeEnum.TEXT.getType());
                // 流结束标识
                sendEndMsg(outputModel);
            } catch (Exception e) {
                log.error("[ConversationController] sendMessage error, request:{}", GsonUtils.toJsonString(request), e);
                AgentChatManager.send(AgentChatManager.generateContentMsg("系统出现异常，请重试"), SseEventTypeEnum.ERROR.getEventType());
                sink.error(e);
            } finally {
                // 结束响应（必须调用，否则会存在内存泄漏问题）
                AgentChatManager.remove();
                sink.complete();
            }
        }));
    }

    @RequestMapping(value="/msgReview", produces = "application/json", method = RequestMethod.POST)
    public RemoteResponse<Boolean> msgReview(@RequestBody MsgReviewRequest request, HttpServletRequest httpServletRequest) {
        if (Objects.isNull(request) || Objects.isNull(httpServletRequest)) {
            return RemoteResponse.invalidParam(ResponseCodeEnum.INVALID_REQUEST.getName());
        }
        try {
            conversationStoreService.updateReviewType(request.getMsgId(), request.getReviewType());
        } catch (Exception e) {
            log.error("[ConversationController] msgReview error, request:{}", GsonUtils.toJsonString(request), e);
            return RemoteResponse.fail(ResponseCodeEnum.SERVER_ERROR, ResponseCodeEnum.SERVER_ERROR.getName());
        }
        return RemoteResponse.success(true);
    }


    private <T> void validRequest(T request, HttpServletRequest httpServletRequest) {
        if (Objects.isNull(request) || Objects.isNull(httpServletRequest)) {
            throw new IllegalArgumentException("Invalid request");
        }
        if (request instanceof CreateConversationRequest) {
            CreateConversationRequest createConversationRequest = (CreateConversationRequest) request;
            if (StringUtils.isBlank((createConversationRequest).getUserMis())) {
                throw new IllegalArgumentException("用户Mis号不存在");
            }
            if (createConversationRequest.getConfigUnitId() == null || createConversationRequest.getConfigUnitId() <= 0) {
                throw new IllegalArgumentException("未输入configUnitId");
            }
        } else if(request instanceof SendMessageRequest) {
            if (StringUtils.isBlank(((SendMessageRequest) request).getUserMis())) {
                throw new IllegalArgumentException("用户Mis号不存在");
            }
        }
    }

    private void sendEndMsg(AgentChatModel outputModel) {
        if (Objects.isNull(outputModel)) {
            AgentChatManager.send(null, SseEventTypeEnum.END.getEventType());
        }
        JsonObject data = new JsonObject();
        data.addProperty("messageId", outputModel.getMessageId());
        data.addProperty("messageType", outputModel.getSegment().getDescription());
        AgentChatManager.send(AgentChatManager.generateEndMsg(data), SseEventTypeEnum.END.getEventType());
    }
}

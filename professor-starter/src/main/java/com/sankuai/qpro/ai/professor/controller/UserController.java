package com.sankuai.qpro.ai.professor.controller;

import com.google.common.collect.Lists;
import com.sankuai.meituan.auth.util.UserUtils;
import com.sankuai.meituan.auth.vo.User;
import com.sankuai.qpro.ai.professor.api.enums.ResponseCodeEnum;
import com.sankuai.qpro.ai.professor.api.response.RemoteResponse;
import com.sankuai.qpro.ai.professor.api.response.UserModel;
import com.sankuai.qpro.ai.professor.application.service.developer.trading.DxUserService;
import com.sankuai.xm.openplatform.api.entity.UserDetail;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.beanutils.BeanUtils;
import org.apache.commons.collections.MapUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Map;

/**
 * @author: wuwenqi<PERSON>
 * @create: 2025-06-05
 * @description:
 */
@RestController
@RequestMapping("/api/user")
@Slf4j
public class UserController {

    @Resource
    private DxUserService dxUserService;

    @RequestMapping(value="/query", produces = "application/json", method = RequestMethod.GET)
    public RemoteResponse<UserModel> getUserInfo() {
        try {
            User user = UserUtils.getUser();
            if (user == null) {
                return RemoteResponse.fail(ResponseCodeEnum.SERVER_ERROR,"用户未登录");
            }
            UserModel userModel = new UserModel();
            BeanUtils.copyProperties(userModel, user);
            UserDetail userDetail = dxUserService.getUserDetailByEmpId(Long.valueOf(user.getId()));
            userModel.setAvatarUrl(userDetail.getAvatarUrl());
            return RemoteResponse.success(userModel);
        } catch (Exception e) {
            log.error("获取用户信息失败", e);
            return RemoteResponse.fail(ResponseCodeEnum.SERVER_ERROR, "获取用户信息失败");
        }
    }

}

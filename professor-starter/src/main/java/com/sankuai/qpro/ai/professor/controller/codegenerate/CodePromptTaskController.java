package com.sankuai.qpro.ai.professor.controller.codegenerate;

import com.sankuai.meituan.auth.util.UserUtils;
import com.sankuai.meituan.auth.vo.User;
import com.sankuai.qpro.ai.professor.api.enums.ResponseCodeEnum;
import com.sankuai.qpro.ai.professor.api.request.CreateCodePromptTaskReq;
import com.sankuai.qpro.ai.professor.api.response.CodePromptTaskModel;
import com.sankuai.qpro.ai.professor.api.response.RemoteResponse;
import com.sankuai.qpro.ai.professor.application.service.codegenerate.core.CodePromptTaskManageService;
import com.sankuai.qpro.ai.professor.application.utils.SerializeUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * @author: wu<PERSON><PERSON><PERSON>
 * @create: 2025-05-29
 * @description:
 */
@RestController
@RequestMapping("/code/promptTask")
@Slf4j
public class CodePromptTaskController {

    @Resource
    private CodePromptTaskManageService codePromptTaskManageService;

    @RequestMapping(value="/create", produces = "application/json", method = RequestMethod.POST)
    public RemoteResponse<String> createCodePromptTask(@RequestBody CreateCodePromptTaskReq request) {
        if (!validateCreateReq(request)) {
            return RemoteResponse.fail(ResponseCodeEnum.INVALID_REQUEST, "参数不合法");
        }
        try {
            String userId = getMisId();
            Long taskId = codePromptTaskManageService.createCodePromptTask(userId, request);
            if (taskId == null) {
                return RemoteResponse.fail(ResponseCodeEnum.SERVER_ERROR, "创建任务失败");
            }
            return RemoteResponse.success(String.valueOf(taskId));
        } catch (Exception e) {
            log.error("创建任务失败, req={}", SerializeUtils.toJsonStr(request), e);
            return RemoteResponse.fail(ResponseCodeEnum.SERVER_ERROR, "创建任务失败");
        }
    }

    @RequestMapping(value="/detail", produces = "application/json", method = RequestMethod.GET)
    public RemoteResponse<CodePromptTaskModel> queryCodePromptTaskDetail(@RequestParam String taskId) {
        if (StringUtils.isBlank(taskId)) {
            return RemoteResponse.fail(ResponseCodeEnum.INVALID_REQUEST, "任务ID不能为空");
        }
        String userId = getMisId();
        CodePromptTaskModel codePromptTaskModel = codePromptTaskManageService.queryCodePromptTaskById(Long.valueOf(taskId), userId);
        return RemoteResponse.success(codePromptTaskModel);
    }

    @RequestMapping(value="/count", produces = "application/json", method = RequestMethod.GET)
    public RemoteResponse<Long> queryCodePromptTaskCount() {
        String userId = getMisId();
        Long count = codePromptTaskManageService.queryCodePromptTaskCountByUserId(userId);
        return RemoteResponse.success(count);
    }

    @RequestMapping(value="/list", produces = "application/json", method = RequestMethod.GET)
    public RemoteResponse<List<CodePromptTaskModel>> queryCodePromptTaskList(@RequestParam Integer page, @RequestParam Integer size) {
        if (!validatePageParam(page, size)) {
            return RemoteResponse.fail(ResponseCodeEnum.INVALID_REQUEST, "分页参数不合法");
        }
        String userId = getMisId();
        List<CodePromptTaskModel> codePromptTaskModels = codePromptTaskManageService.queryCodePromptTaskByUserId(userId, page, size);
        return RemoteResponse.success(codePromptTaskModels);
    }

    private boolean validateCreateReq(CreateCodePromptTaskReq request) {
        return request != null && StringUtils.isNotBlank(request.getTitle());
    }

    private boolean validatePageParam(Integer page, Integer size) {
        return page != null && size != null && page >= 0 && size > 0;
    }

    private String getMisId() {
        User user = UserUtils.getUser();
        if (user == null || StringUtils.isBlank(user.getLogin())) {
            return null;
        }
        return user.getLogin();
    }
}

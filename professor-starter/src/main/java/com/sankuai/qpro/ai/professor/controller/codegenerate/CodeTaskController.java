package com.sankuai.qpro.ai.professor.controller.codegenerate;

import com.google.common.collect.Maps;
import com.sankuai.meituan.auth.util.UserUtils;
import com.sankuai.meituan.auth.vo.User;
import com.sankuai.qpro.ai.professor.api.enums.CodePromptStageEnum;
import com.sankuai.qpro.ai.professor.api.enums.ResponseCodeEnum;
import com.sankuai.qpro.ai.professor.api.request.ExtractKmDocWithImageReq;
import com.sankuai.qpro.ai.professor.api.request.ExtractRequirementDocRequest;
import com.sankuai.qpro.ai.professor.api.request.SaveRequirementPromptReq;
import com.sankuai.qpro.ai.professor.api.response.CodePromptTaskModel;
import com.sankuai.qpro.ai.professor.api.response.RemoteResponse;
import com.sankuai.qpro.ai.professor.api.constants.PromptTaskAttrConstant;
import com.sankuai.qpro.ai.professor.application.constants.RequestContextConstant;
import com.sankuai.qpro.ai.professor.application.model.codegenerate.RequestContext;
import com.sankuai.qpro.ai.professor.application.service.codegenerate.core.CodePromptTaskManageService;
import com.sankuai.qpro.ai.professor.application.service.codegenerate.core.ExtractRequirementDocService;
import com.sankuai.qpro.ai.professor.application.utils.SerializeUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Map;

/**
 * @author: wuwenqiang
 * @create: 2025-05-29
 * @description:
 */
@RestController
@RequestMapping("/code/task")
@Slf4j
public class CodeTaskController {

    @Resource
    private ExtractRequirementDocService extractRequirementDocService;

    @Resource
    private CodePromptTaskManageService codePromptTaskManageService;

    /**
     * 解析需求文档 + 需求评审记录
     * @param req
     * @return
     */
    @RequestMapping(value="/parseRequirement", produces = "application/json", method = RequestMethod.POST)
    public RemoteResponse<String> parseRequirement(@RequestBody ExtractRequirementDocRequest req) {
        if (req == null) {
            return RemoteResponse.fail(ResponseCodeEnum.INVALID_REQUEST, "参数不能为空");
        }
        String userId = getMisId();
        if (!validateTaskAndUserId(req.getTaskId(), userId)) {
            return RemoteResponse.fail(ResponseCodeEnum.INVALID_REQUEST, "用户无权限");
        }
        try {
            // 1. 设置上下文
            RequestContext.setAttribute(RequestContextConstant.EXTRACT_REQUIREMENT_REQ, SerializeUtils.toJsonStr(req));
            RequestContext.setAttribute(RequestContextConstant.USER_MIS, userId);
            // 2. 提取需求文档
            return extractRequirementDocService.extractRequirementDoc(req);
        } catch (Exception e) {
            log.error("解析需求文档异常, req:{}", req, e);
        }
        return RemoteResponse.fail(ResponseCodeEnum.SERVER_ERROR, "解析需求文档异常");
    }

    @RequestMapping(value="/saveRequirementPrompt", produces = "application/json", method = RequestMethod.POST)
    public RemoteResponse<Boolean> saveRequirementPrompt(@RequestBody SaveRequirementPromptReq req) {
        if (!validateSaveRequirementPromptReq(req)) {
            return RemoteResponse.fail(ResponseCodeEnum.INVALID_REQUEST, "参数不能为空");
        }
        String userId = getMisId();
        if (!validateTaskAndUserId(req.getTaskId(), userId)) {
            return RemoteResponse.fail(ResponseCodeEnum.INVALID_REQUEST, "用户无权限");
        }
        try {
            Map<String, String> extraData = Maps.newHashMap();
            extraData.put(PromptTaskAttrConstant.REQUIREMENT_SUMMARY, req.getPrompt());
            boolean result = codePromptTaskManageService.updateCodePromptTask(req.getTaskId(), CodePromptStageEnum.EXTRACT_REQUIREMENT.getOrder(), extraData);
            if (result) {
                return RemoteResponse.success(true);
            }
            return RemoteResponse.fail(ResponseCodeEnum.SERVER_ERROR, "保存标准需求描述失败");
        } catch (Exception e) {
            log.error("保存标准需求描述失败, req={}", req, e);
        }
        return RemoteResponse.fail(ResponseCodeEnum.SERVER_ERROR, "保存标准需求描述失败");
    }

    private boolean validateSaveRequirementPromptReq(SaveRequirementPromptReq req) {
        return req != null && req.getTaskId() != null && req.getTaskId() > 0 && StringUtils.isNotBlank(req.getPrompt());
    }

    private boolean validateTaskAndUserId(Long taskId, String userId) {
        if (taskId == null || taskId <= 0 || StringUtils.isBlank(userId)) {
            return false;
        }
        CodePromptTaskModel taskDetail = codePromptTaskManageService.queryCodePromptTaskById(taskId, userId);
        if (taskDetail == null) {
            return false;
        }
        return true;
    }

    private String getMisId() {
        User user = UserUtils.getUser();
        if (user == null || StringUtils.isBlank(user.getLogin())) {
            return null;
        }
        return user.getLogin();
    }
}

package com.sankuai.qpro.ai.professor.controller.codegenerate;

import com.sankuai.qpro.ai.professor.api.enums.ResponseCodeEnum;
import com.sankuai.qpro.ai.professor.api.request.ExtractKmDocWithImageReq;
import com.sankuai.qpro.ai.professor.api.response.RemoteResponse;
import com.sankuai.qpro.ai.professor.application.service.codegenerate.core.ExtractRequirementDocService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * @author: wuwenqiang
 * @create: 2025-07-07
 * @description:
 */
@RestController
@RequestMapping("/doc")
@Slf4j
public class DocExtractController {

    @Resource
    private ExtractRequirementDocService extractRequirementDocService;

    @RequestMapping(value="/extractKmDocWithImage", produces = "application/json", method = RequestMethod.POST)
    public RemoteResponse<String> extractKmDocWithImage(@RequestBody ExtractKmDocWithImageReq req) {
        if (!validateExtractKmDocWithImageReq(req)) {
            return RemoteResponse.fail(ResponseCodeEnum.INVALID_REQUEST, "参数不能为空");
        }
        try {
            return extractRequirementDocService.extractKmDocWithImage(req);
        } catch (Exception e) {
            log.error("提取学城文档异常, req:{}", req, e);
        }
        return RemoteResponse.fail(ResponseCodeEnum.SERVER_ERROR, "提取学城文档异常");
    }

    private boolean validateExtractKmDocWithImageReq(ExtractKmDocWithImageReq req) {
        return req != null && StringUtils.isNotBlank(req.getUrl());
    }
}

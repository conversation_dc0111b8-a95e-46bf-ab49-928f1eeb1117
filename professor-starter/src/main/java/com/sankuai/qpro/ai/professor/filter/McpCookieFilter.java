package com.sankuai.qpro.ai.professor.filter;

import com.meituan.mdp.boot.starter.web.filter.MdpWebFilter;
import com.sankuai.qpro.ai.professor.application.constants.RequestContextConstant;
import com.sankuai.qpro.ai.professor.application.model.codegenerate.RequestContext;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import javax.servlet.*;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;

/**
 * @author: wuwenqiang
 * @create: 2025-07-03
 * @description:
 */
@MdpWebFilter(urlPatterns = {"/sse/*", "/sse", "/doc/*"})
@Component
public class McpCookieFilter implements Filter {

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain) throws IOException, ServletException {
        HttpServletRequest httpRequest = (HttpServletRequest) request;
        RequestContext.init();
        String cookieHeader = httpRequest.getHeader("Cookie");
        if (StringUtils.isNotBlank(cookieHeader)) {
            RequestContext.setAttribute(RequestContextConstant.COOKIE_HEADER, cookieHeader);
        }
        // 继续执行后续Filter链
        chain.doFilter(request, response);
    }
}

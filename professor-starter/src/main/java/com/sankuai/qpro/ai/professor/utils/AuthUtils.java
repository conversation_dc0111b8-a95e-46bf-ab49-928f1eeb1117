package com.sankuai.qpro.ai.professor.utils;

import org.apache.commons.lang.StringUtils;

import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import java.util.Objects;

/**
 * @author: w<PERSON><PERSON><PERSON><PERSON>
 * @create: 2024-12-10
 * @description:
 */
public class AuthUtils {
    private static final String TOKEN_NAME = "shop_guide_ai_token";
    // 暂时写成明文
    private static final String TOKEN_VALUE = "123456";
    public static boolean hasAuth(HttpServletRequest request) {
        if (request == null) {
            return false;
        }
        String token = parseTokenFormCookies(request.getCookies(), TOKEN_NAME);
        return StringUtils.equals(token, TOKEN_VALUE);
    }

    private static String parseTokenFormCookies(Cookie[] cookies, String tokenName) {
        if (Objects.isNull(cookies)) {
            return null;
        }
        for (Cookie cookie : cookies) {
            if (Objects.nonNull(cookie)
                    && StringUtils.equals(cookie.getName(), tokenName)) {
                return cookie.getValue();
            }
        }
        return null;
    }
}

mdp.langmodel.friday.appId=1864250816652337228
mdp.langmodel.meituan-chat-language-model.enable=true

mdp.ai.openai.chat.enable=true
mdp.ai.openai.chat[0].debug=false
mdp.ai.openai.chat[0].beanName=gpt4oMiniModel
mdp.ai.openai.chat[0].model=gpt-4o-mini
mdp.ai.openai.chat[0].temperature=0.2d
mdp.ai.openai.chat[0].topP=1d
mdp.ai.openai.chat[0].maxTokens=10000
mdp.ai.openai.chat[0].frequencyPenalty=0.1d
mdp.ai.openai.chat[0].presencePenalty=0.1d

mdp.ai.openai.chat[1].debug=false
mdp.ai.openai.chat[1].beanName=gpt4oModel
mdp.ai.openai.chat[1].model=gpt-4o-2024-11-20
mdp.ai.openai.chat[1].temperature=0.2d
mdp.ai.openai.chat[1].topP=0.9d
mdp.ai.openai.chat[1].maxTokens=4000
mdp.ai.openai.chat[1].frequencyPenalty=0.1d
mdp.ai.openai.chat[1].presencePenalty=0.1d

mdp.ai.openai.chat[2].debug=false
mdp.ai.openai.chat[2].beanName=gpt4PreviewModel
mdp.ai.openai.chat[2].model=gpt-4-1106-preview
mdp.ai.openai.chat[2].temperature=0.2d
mdp.ai.openai.chat[2].topP=0.9d
mdp.ai.openai.chat[2].maxTokens=4000
mdp.ai.openai.chat[2].frequencyPenalty=0.1d
mdp.ai.openai.chat[2].presencePenalty=0.1d

mdp.ai.openai.chat[3].debug=false
mdp.ai.openai.chat[3].beanName=gpt4oSummaryModel
mdp.ai.openai.chat[3].model=gpt-4o-2024-11-20
mdp.ai.openai.chat[3].temperature=0.2d
mdp.ai.openai.chat[3].topP=0.9d
mdp.ai.openai.chat[3].maxTokens=6000
mdp.ai.openai.chat[3].frequencyPenalty=0.1d
mdp.ai.openai.chat[3].presencePenalty=0.1d

mdp.ai.openai.chat[4].debug=false
mdp.ai.openai.chat[4].beanName=gpt4TurboModel
mdp.ai.openai.chat[4].model=gpt-4-turbo-2024-04-09
mdp.ai.openai.chat[4].temperature=0.8d
mdp.ai.openai.chat[4].topP=0.9d
mdp.ai.openai.chat[4].maxTokens=2000
mdp.ai.openai.chat[4].frequencyPenalty=0.1d
mdp.ai.openai.chat[4].presencePenalty=0.1d

mdp.ai.openai.chat[5].debug=false
mdp.ai.openai.chat[5].beanName=gpt4oStructModel
mdp.ai.openai.chat[5].model=gpt-4o-2024-11-20
mdp.ai.openai.chat[5].temperature=0.6d
mdp.ai.openai.chat[5].topP=0.9d
mdp.ai.openai.chat[5].maxTokens=6000
mdp.ai.openai.chat[5].frequencyPenalty=0.1d
mdp.ai.openai.chat[5].presencePenalty=0.1d

mdp.ai.openai.chat[6].debug=false
mdp.ai.openai.chat[6].beanName=gpt4oCodeModel
mdp.ai.openai.chat[6].model=gpt-4o-2024-11-20
mdp.ai.openai.chat[6].temperature=0.6d
mdp.ai.openai.chat[6].topP=0.9d
mdp.ai.openai.chat[6].maxTokens=16000
mdp.ai.openai.chat[6].frequencyPenalty=0.1d
mdp.ai.openai.chat[6].presencePenalty=0.1d

mdp.ai.friday.chat.tool.enable=true
mdp.ai.friday.chat.tool.[0].debug=false
mdp.ai.friday.chat.tool.[0].beanName=claudeModel
mdp.ai.friday.chat.tool.[0].model=anthropic.claude-3.5-sonnet
mdp.ai.friday.chat.tool.[0].temperature=0.2d
mdp.ai.friday.chat.tool.[0].topP=0.9d
mdp.ai.friday.chat.tool.[0].maxTokens=4000
mdp.ai.friday.chat.tool.[0].frequencyPenalty=0.1d
mdp.ai.friday.chat.tool.[0].presencePenalty=0.1d

mdp.ai.friday.chat.tool.[1].debug=false
mdp.ai.friday.chat.tool.[1].beanName=deepSeekV3Model
mdp.ai.friday.chat.tool.[1].model=deepseek-v3-cloud
mdp.ai.friday.chat.tool.[1].temperature=0.8d
mdp.ai.friday.chat.tool.[1].topP=0.9d
mdp.ai.friday.chat.tool.[1].maxTokens=8000
mdp.ai.friday.chat.tool.[1].frequencyPenalty=0.1d
mdp.ai.friday.chat.tool.[1].presencePenalty=0.1d

mdp.ai.friday.chat.tool.[2].debug=false
mdp.ai.friday.chat.tool.[2].beanName=deepSeekR1Model
mdp.ai.friday.chat.tool.[2].model=deepseek-r1-cloud
mdp.ai.friday.chat.tool.[2].temperature=0.8d
mdp.ai.friday.chat.tool.[2].topP=0.9d
mdp.ai.friday.chat.tool.[2].maxTokens=8000
mdp.ai.friday.chat.tool.[2].frequencyPenalty=0.1d
mdp.ai.friday.chat.tool.[2].presencePenalty=0.1d
management.server.port=8080
management.endpoints.web.base-path=/monitor
management.endpoints.web.path-mapping.health=/alive
server.port=8080
server.servlet.context-path=/ai/probe/chat

dx.appId=021P50T155414220
dx.appSecret=360a6569bc821debcf170f6bb8975089

spring.ai.openai.api-key=1864250816652337228
spring.ai.openai.base-url=https://aigc.sankuai.com/v1/openai/native
spring.ai.openai.chat.completions-path=/chat/completions
spring.ai.openai.chat.options.model=gpt-4.1

ssoSecret=c192b9009c0b481aaf66d741b6b8385f
ssoClientId=41be7fc50a

# Local MCP Server Config, when run mvn package, this config will be used
#spring.main.web-application-type=none
## disable banner
#spring.main.banner-mode=off
#
#spring.ai.mcp.server.stdio=true
#spring.ai.mcp.server.name=professor-mcp-local-server

# disable console log
#logging.pattern.console=

# Remote MCP Server Config
#spring.ai.mcp.server.sse-message-endpoint=/mcp/message
#spring.ai.mcp.server.sse-endpoint=/sse

spring.ai.mcp.client.name=professor
spring.ai.mcp.client.version=1.0.0
spring.ai.mcp.client.request-timeout=20s
spring.ai.mcp.client.type=SYNC
spring.ai.mcp.client.root-change-notification=true
spring.ai.mcp.client.toolcallback.enabled=true

#spring.ai.mcp.client.stdio.connections.claude-context.command=npx
#spring.ai.mcp.client.stdio.connections.claude-context.args=@zilliz/claude-context-mcp@latest
#spring.ai.mcp.client.stdio.connections.claude-context.env.EMBEDDING_PROVIDER=Ollama
#spring.ai.mcp.client.stdio.connections.claude-context.env.OLLAMA_HOST=http://127.0.0.1:11434
#spring.ai.mcp.client.stdio.connections.claude-context.env.OLLAMA_MODEL=granite-embedding:278m
#spring.ai.mcp.client.stdio.connections.claude-context.env.MILVUS_ADDRESS=http://127.0.0.1:19530
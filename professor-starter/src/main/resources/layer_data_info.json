{"ShelfDouHuFetcher": {"关联数据": "斗斛AB测试实验数据、实验配置数据、实验结果数据", "相关外部接口": ["com.sankuai.dzviewscene.productshelf.nr.atom.AtomFacadeService#getPoiABTest", "com.sankuai.dzviewscene.productshelf.nr.atom.impl.AtomFacadeServiceImpl#getPoiABTest"]}, "MatrixExperimentFetcher": {"关联数据": "Matrix实验数据、实验场景配置数据、实验组配置数据、AB测试结果数据", "相关外部接口": ["com.sankuai.meituan.waimai.dataops.exp.service.DataOpsExperimentManagerV2#getExperiments"]}, "ArenaExperimentFetcher": {"关联数据": "无（实现不存在）", "相关外部接口": []}, "PreHandlerContextAbility": {"关联数据": "医美意图识别数据、留资权益数据、直播状态数据、跨品类推荐数据、美团基础参数数据、门店信息数据、城市信息数据、后台类目数据、用户上下文数据、环境上下文数据", "相关外部接口": ["com.sankuai.beautycontent.intention.api.IntentionService#recognizeIntention", "com.sankuai.clr.content.process.thrift.api.LeadsQueryService#batchQueryLeadsInfo", "com.sankuai.mpcontent.feeds.HorizonVideoLiveService#queryIsLiving", "com.sankuai.fbi.faas.wed.api.CrossCatRecommendService#queryCrossTitleByBackCat", "com.sankuai.sinai.data.query.MtPoiService#batchGetPoiByMtShopIds", "com.sankuai.dzviewscene.nr.atom.CompositeAtomService#batchGetMtByDpIds", "com.sankuai.dzviewscene.nr.atom.CompositeAtomService#batchGetPoiByMtShopIds"]}, "FilterFirstFetcher": {"关联数据": "商品平台筛选配置数据、筛选导航数据、筛选项配置数据", "相关外部接口": ["com.dianping.product.shelf.query.api.GeneralShelfQueryService#queryShelfNav", "com.dianping.product.shelf.query.api.GeneralShelfQueryService#queryShelfNavV2"]}, "DealQueryFetcher": {"关联数据": "商品平台团单商品数据、商品列表数据、团单基础信息数据", "相关外部接口": ["com.dianping.product.shelf.query.api.GeneralShelfQueryService#queryShelfNavTabProductList", "com.dianping.product.shelf.query.api.GeneralShelfQueryService#queryShelfNavTabProductListV2"]}, "ProductPaddingFetcher": {"关联数据": "商品主题数据、标品主题数据、团单主题数据、泛商品主题数据、组合商品数据、次卡数据、商品详细信息数据、商品属性数据、商品活动数据、商品价格数据、商品库存数据、商品图片数据、商品标签数据", "相关外部接口": ["com.sankuai.dztheme.product.ProductQueryService#query", "com.sankuai.dztheme.spuproduct.SpuThemeQueryService#query", "com.sankuai.dztheme.dealproduct.DealProductService#query", "com.sankuai.dztheme.generalproduct.GeneralProductService#query", "com.sankuai.pack.api.CombinationProductQueryService", "com.dianping.TimesCardQueryService.TimesCardQueryService"]}, "ContextHandlerAbility": {"关联数据": "金融服务数据、保障服务数据、留资服务数据、推荐服务数据、意图识别数据、数据同步服务数据、预订服务数据、销量服务数据、用户信用支付数据、门店保障标签数据、门店预约数量数据、门店客户推荐词数据、搜索词分析数据、生活洗护时效性数据、KTV预订规则数据、门店销量展示数据", "相关外部接口": ["com.sankuai.fincreditpay.bnpl.client.access.thrift.BNPLAccessThriftService#exposure", "com.sankuai.priceoperation.service.GuaranteeQueryService", "com.sankuai.leads.count.NewLeadsCountService", "com.sankuai.beautycontent.intention.api.IntentionService#recognize", "com.sankuai.feitianplus.data.onedata.api.thrift.service.QueryDataSyncService", "com.sankuai.spt.statequery.ShopBookInfoQueryService", "com.sankuai.mpmctmember.process.SalesDisplayQueryService"]}, "ProductParallelQueryFetcher": {"关联数据": "商品平台团单商品数据（并行查询优化）、商品列表数据、团单基础信息数据", "相关外部接口": ["com.dianping.product.shelf.query.api.GeneralShelfQueryService#queryShelfNavTabProductList", "com.dianping.product.shelf.query.api.GeneralShelfQueryService#queryShelfNavTabProductListV2"]}, "ProductParallelQueryPostHandler": {"关联数据": "商品匹配结果数据、筛选匹配状态数据、门店在线团单数据、降级查询结果数据", "相关外部接口": ["com.dianping.product.shelf.query.api.GeneralShelfQueryService#queryShelfNavTabProductList", "com.dianping.deal.shop.ShopOnlineDealGroupService#queryShopOnlineDealGroups", "com.dianping.deal.shop.ShopOnlineDealGroupService#queryLongShopOnlineDealGroups"]}, "ShopProductPaddingFetcher": {"关联数据": "门店商品数据、商品分组数据、商品类型数据、门店在线团单数据、门店推荐商品数据、商品主题填充数据", "相关外部接口": ["com.dianping.deal.shop.ShopOnlineDealGroupService#queryShopOnlineDealGroups", "com.sankuai.dztheme.product.ProductQueryService#query", "com.sankuai.dztheme.spuproduct.SpuThemeQueryService#query", "com.sankuai.dztheme.dealproduct.DealProductService#query"]}, "CardFetcher": {"关联数据": "门店卡券类型数据、用户持卡状态数据、卡券有效性数据、平台卡券映射数据", "相关外部接口": ["com.sankuai.dzcard.joycard.navigation.api.JoyCardProductShelfService#getShopAndUserCardHoldStatus"]}, "ProductActivitiesFetcher": {"关联数据": "门店营销活动数据、活动类型数据、活动图片数据、活动状态数据、活动有效期数据、活动规则数据", "相关外部接口": ["com.sankuai.gmkt.activity.service.DealActivityQueryService#batchQueryShopActivity"]}, "ShelfMainDataAssembler": {"关联数据": "货架组装数据、筛选组装数据、商品分组数据、实验标记数据、商品总数统计数据、优惠减负标记数据", "相关外部接口": []}, "UnifiedShelfFilterBuilder": {"关联数据": "筛选器UI数据、筛选节点数据、筛选按钮数据、筛选配置数据、筛选显示规则数据", "相关外部接口": []}, "UnifiedProductAreaBuilder": {"关联数据": "商品基础信息数据（商品ID、商品类型、有效状态）、商品跳转链接数据、商品埋点数据、商品扩展信息数据、商品标题数据、商品图片数据（头图、角标、浮动标签）、商品价格数据（售价、原价、划线价、价格前后缀）、商品标签数据（特色标签、副标题标签、优惠标签、价格下标签）、商品活动数据、商品按钮数据、商品轮播信息数据、商品预热信息数据、商品子项数据、商品优惠明细数据、神券优惠数据、富文本标签数据、图标标签数据", "相关外部接口": []}, "UnifiedShelfOceanBuilder": {"关联数据": "埋点配置数据、AB测试数据、埋点标识数据、用户行为追踪数据", "相关外部接口": []}, "UnifiedShelfMainTitleBuilder": {"关联数据": "标题配置数据、标题图标数据、商品总数统计数据、平台样式数据", "相关外部接口": []}, "UnifiedShelfResponseAssembler": {"关联数据": "货架响应数据、展示样式数据、扩展属性数据、运营配置数据", "相关外部接口": ["com.sankuai.dzviewscene.nr.atom.CompositeAtomService#asyncFlashOperatorShelfConfig"]}}
# UnifiedShelfResponse 货架展示信息字段梳理（含VP标识）

## 概述
本文档详细梳理了美团点评货架展示系统中 `UnifiedShelfResponse` 类的所有字段结构，按照"模块-展示信息-具体字段"的层级关系进行整理，并为与VP直接关联的子模块标注了相应的VP信息，便于理解货架展示的完整数据结构和扩展点机制。

## 一、主体结构模块

| 模块 | 展示信息 | 字段层级名 | 字段描述 | VP标识 | VP所在类 | VP描述 |
|------|----------|------------|----------|--------|----------|--------|
| 货架主体 | 筛选商品区 | filterIdAndProductAreas | 筛选项对应的商品区域列表 | - | - | - |
| 货架主体 | 货架导航 | filter | 筛选导航组件 | - | - | - |
| 货架主体 | 主标题 | mainTitle | 货架主标题信息 | UnifiedShelfMainTitleVP | UnifiedShelfMainTitleBuilder | 主标题变化点 |
| 货架主体 | 场景标识 | sceneCode | 货架场景代码 | - | - | - |
| 货架主体 | 展示样式 | showType | 货架展示样式类型 | - | - | - |
| 货架主体 | 埋点信息 | ocean | 上报埋点数据 | - | - | - |
| 货架主体 | 追踪标识 | traceId | 请求追踪ID（保留字段） | - | - | - |
| 货架主体 | 保留信息 | retainMsg | 保留消息（保留字段） | - | - | - |


## 二、筛选商品区模块 (filterIdAndProductAreas)

| 模块 | 展示信息 | 字段层级名 | 字段描述 | VP标识 | VP所在类 | VP描述 |
|------|----------|------------|----------|--------|----------|--------|
| 筛选商品区 | 商品区域 | filterIdAndProductAreas.productAreas | 商品展示区域列表 | - | - | - |
| 筛选商品区 | 筛选标识 | filterIdAndProductAreas.filterId | 筛选项ID | - | - | - |
| 筛选商品区 | 追踪标识 | filterIdAndProductAreas.traceId | 追踪ID（保留字段） | - | - | - |
| 筛选商品区 | 保留信息 | filterIdAndProductAreas.retainMsg | 保留消息（保留字段） | - | - | - |

## 三、商品区域模块 (productAreas)

| 模块 | 展示信息 | 字段层级名 | 字段描述 | VP标识 | VP所在类 | VP描述 |
|------|----------|------------|----------|--------|----------|--------|
| 商品区域 | 商品列表 | filterIdAndProductAreas.productAreas.items | 商品项列表 | - | - | - |
| 商品区域 | 更多跳转 | filterIdAndProductAreas.productAreas.moreJumpUrl | 查看更多跳转链接 | ProductAreaMoreJumpUrlVP | UnifiedProductAreaBuilder | 商品区-查看更多-跳转链接 |
| 商品区域 | 更多文案 | filterIdAndProductAreas.productAreas.moreText | 查看更多按钮文案 | ProductAreaMoreTextVP | UnifiedProductAreaBuilder | 商品区-查看更多-展示文案 |
| 商品区域 | 默认展示数 | filterIdAndProductAreas.productAreas.defaultShowNum | 默认显示商品数量 | ProductAreaDefaultShowNumVP | UnifiedProductAreaBuilder | 商品区-默认展示数量 |
| 商品区域 | 分页标识 | filterIdAndProductAreas.productAreas.hasNext | 是否有下一页 | - | - | - |
| 商品区域 | 展示样式 | filterIdAndProductAreas.productAreas.showType | 商品区展示样式 | ProductAreaShowTypeVP | UnifiedProductAreaBuilder | 商品区-展示样式 |

## 四、商品项模块 (items)

### 4.1 基础信息

| 模块 | 展示信息 | 字段层级名 | 字段描述 | VP标识 | VP所在类 | VP描述 |
|------|----------|------------|----------|--------|----------|--------|
| 商品项 | 商品ID | filterIdAndProductAreas.productAreas.items.itemId | 商品唯一标识 | - | - | - |
| 商品项 | 商品类型 | filterIdAndProductAreas.productAreas.items.itemType | 商品类型标识 | - | - | - |
| 商品项 | 有效状态 | filterIdAndProductAreas.productAreas.items.available | 商品是否有效 | UnifiedShelfItemAvailableVP | UnifiedProductAreaBuilder | 是否有效 |
| 商品项 | 跳转链接 | filterIdAndProductAreas.productAreas.items.jumpUrl | 商品详情跳转链接 | UnifiedShelfItemJumpUrlVP | UnifiedProductAreaBuilder | 商品跳转链接 |
| 商品项 | 埋点信息 | filterIdAndProductAreas.productAreas.items.labs | 埋点数据（JSON格式） | UnifiedShelfItemOceanLabsVP | UnifiedProductAreaBuilder | 商品-Ocean 打点 |
| 商品项 | 扩展信息 | filterIdAndProductAreas.productAreas.items.extra | 扩展信息（JSON格式） | UnifiedShelfItemExtraVP | UnifiedProductAreaBuilder | 团单扩展数据 |
| 商品项 | 展示样式 | filterIdAndProductAreas.items.showType | 商品卡片样式 | - | - | - |

### 4.2 标题信息

| 模块 | 展示信息 | 字段层级名 | 字段描述 | VP标识 | VP所在类 | VP描述 |
|------|----------|------------|----------|--------|----------|--------|
| 商品标题 | 主标题 | filterIdAndProductAreas.productAreas.items.title | 商品标题列表 | UnifiedShelfItemTitleVP | UnifiedProductAreaBuilder | 商品标题 |
| 商品标题 | 标题文案 | filterIdAndProductAreas.productAreas.items.title.text | 标题文本内容 | - | - | - |
| 商品标题 | 标题样式 | filterIdAndProductAreas.productAreas.items.title.style | 标题文本样式 | - | - | - |
| 商品标题 | 标题标签 | filterIdAndProductAreas.productAreas.items.titleTag | 标题旁标签 | - | - | - |
| 商品标题 | 标签文本 | filterIdAndProductAreas.productAreas.items.titleTag.text | 标签文本信息 | - | - | - |
| 商品标题 | 标签图标 | filterIdAndProductAreas.productAreas.items.titleTag.icon | 标签图标信息 | - | - | - |
| 商品标题 | 标签类型 | filterIdAndProductAreas.productAreas.items.titleTag.type | 标签类型（文本/图片/图文） | - | - | - |

### 4.3 图片信息

| 模块 | 展示信息 | 字段层级名 | 字段描述 | VP标识 | VP所在类 | VP描述 |
|------|----------|------------|----------|--------|----------|--------|
| 商品图片 | 头图区域 | filterIdAndProductAreas.productAreas.items.headPic | 商品头图区域 | UnifiedShelfItemPicVP | UnifiedProductAreaBuilder | 商品图片 |
| 商品图片 | 默认图片 | filterIdAndProductAreas.productAreas.items.headPic.defaultPicUrl | 16:9默认头图URL | - | - | - |
| 商品图片 | 主图信息 | filterIdAndProductAreas.productAreas.items.headPic.pic | 主图详细信息 | - | - | - |
| 商品图片 | 图片URL | filterIdAndProductAreas.productAreas.items.headPic.pic.picUrl | 图片链接地址 | - | - | - |
| 商品图片 | 图片高度 | filterIdAndProductAreas.productAreas.items.headPic.pic.picHeight | 图片高度像素 | - | - | - |
| 商品图片 | 宽高比 | filterIdAndProductAreas.productAreas.items.headPic.pic.aspectRadio | 图片宽高比例 | - | - | - |
| 商品图片 | 浮动标签 | filterIdAndProductAreas.productAreas.items.headPic.floatTags | 头图角标列表 | UnifiedShelfItemPicFloatTagVP | UnifiedProductAreaBuilder | 商品图片角标 |
| 商品图片 | 角标内容 | filterIdAndProductAreas.productAreas.items.headPic.floatTags.tag | 角标标签内容 | - | - | - |
| 商品图片 | 角标位置 | filterIdAndProductAreas.productAreas.items.headPic.floatTags.position | 角标位置（1左上2右上3右下4左下） | - | - | - |

### 4.4 价格信息

| 模块 | 展示信息 | 字段层级名 | 字段描述 | VP标识 | VP所在类 | VP描述 |
|------|----------|------------|----------|--------|----------|--------|
| 商品价格 | 售价 | filterIdAndProductAreas.productAreas.items.salePrice | 商品售价 | UnifiedShelfItemSalePriceVP | UnifiedProductAreaBuilder | 商品售卖价格 |
| 商品价格 | 展示售价 | filterIdAndProductAreas.productAreas.items.displaySalePrice | 商品展示售价 | UnifiedShelfItemDisplaySalePriceVP | UnifiedProductAreaBuilder | 商品展示售价 |
| 商品价格 | 原价 | filterIdAndProductAreas.productAreas.items.basePrice | 原始价格 | - | - | - |
| 商品价格 | 划线价 | filterIdAndProductAreas.productAreas.items.marketPrice | 市场价（划线价） | UnifiedShelfItemMarketPriceVP | UnifiedProductAreaBuilder | 市场价 |
| 商品价格 | 售价前缀 | filterIdAndProductAreas.productAreas.items.salePricePrefix | 售价前缀文案 | UnifiedShelfSalePricePrefixVP | UnifiedProductAreaBuilder | 价格前缀 |
| 商品价格 | 售价后缀 | filterIdAndProductAreas.productAreas.items.salePriceSuffix | 售价后缀文案 | UnifiedShelfSalePriceSuffixVP | UnifiedProductAreaBuilder | 价格后缀 |
| 商品价格 | 后缀对象 | filterIdAndProductAreas.productAreas.items.salePriceSuffixObject | 售价后缀详细信息 | UnifiedShelfSalePriceSuffixObjectVP | UnifiedProductAreaBuilder | 价格后缀信息对象 |
| 商品价格 | 后缀文案 | filterIdAndProductAreas.productAreas.items.salePriceSuffixObject.salePriceSuffix | 后缀文本内容 | - | - | - |
| 商品价格 | 后缀字号 | filterIdAndProductAreas.productAreas.items.salePriceSuffixObject.textSize | 后缀字体大小 | - | - | - |

### 4.5 标签信息

| 模块 | 展示信息 | 字段层级名 | 字段描述 | VP标识 | VP所在类 | VP描述 |
|------|----------|------------|----------|--------|----------|--------|
| 商品标签 | 特色标签 | filterIdAndProductAreas.productAreas.items.specialTags | 特色标签组 | UnifiedShelfItemSpecialTagVP | UnifiedProductAreaBuilder | 第三行标题-特色标签 |
| 商品标签 | 特色标签列表 | filterIdAndProductAreas.productAreas.items.specialTags.tags | 特色标签列表 | - | - | - |
| 商品标签 | 副标题标签 | filterIdAndProductAreas.productAreas.items.productTags | 副标题标签组 | UnifiedShelfItemSubTitleVP | UnifiedProductAreaBuilder | 商品-副标题 |
| 商品标签 | 副标题列表 | filterIdAndProductAreas.productAreas.items.productTags.tags | 副标题标签列表 | - | - | - |
| 商品标签 | 连接方式 | filterIdAndProductAreas.productAreas.items.productTags.joinType | 标签连接方式（竖线/圆点） | - | - | - |
| 商品标签 | 前置图标 | filterIdAndProductAreas.productAreas.items.productTags.iconTag | 副标题前置图标 | - | - | - |
| 商品标签 | 优惠标签 | filterIdAndProductAreas.productAreas.items.promoTags | 优惠力度标签列表 | UnifiedShelfItemPromoTagVP | UnifiedProductAreaBuilder | 商品优惠标签 |
| 商品标签 | 价格下标签 | filterIdAndProductAreas.productAreas.items.priceBottomTags | 价格下方标签列表 | UnifiedShelfItemPriceBottomTagVP | UnifiedProductAreaBuilder | 商品价格下方标签 |

### 4.6 标签详细结构 (ShelfTagVO)

| 模块 | 展示信息 | 字段层级名 | 字段描述 | VP标识 | VP所在类 | VP描述 |
|------|----------|------------|----------|--------|----------|--------|
| 标签详情 | 标签文案 | *.tags.text | 标签主文案 | - | - | - |
| 标签详情 | 前置文案 | *.tags.preText | 标签前置文案 | - | - | - |
| 标签详情 | 标签名称 | *.tags.name | 标签名称标识 | - | - | - |
| 标签详情 | 前缀图片 | *.tags.prePic | 标签前缀图片 | - | - | - |
| 标签详情 | 后缀图片 | *.tags.afterPic | 标签后缀图片 | - | - | - |
| 标签详情 | 埋点信息 | *.tags.labs | 标签埋点数据 | - | - | - |
| 标签详情 | 优惠明细 | *.tags.promoDetail | 优惠详细信息 | - | - | - |

## 五、筛选导航模块 (filter)

| 模块 | 展示信息 | 字段层级名 | 字段描述 | VP标识 | VP所在类 | VP描述 |
|------|----------|------------|----------|--------|----------|--------|
| 筛选导航 | 根节点 | filter.filterRoot | 筛选项根节点 | - | - | - |
| 筛选节点 | 节点标题 | filter.filterRoot.title | 筛选项标题 | UnifiedShelfFilterTitleVP | UnifiedShelfFilterBuilder | 筛选-按钮名字 |
| 筛选节点 | 子节点 | filter.filterRoot.children | 子筛选项列表 | - | - | - |
| 筛选节点 | 筛选ID | filter.filterRoot.filterId | 筛选项标识 | - | - | - |
| 筛选节点 | 选中状态 | filter.filterRoot.selected | 是否选中 | - | - | - |
| 筛选节点 | 多选支持 | filter.filterRoot.multiSelect | 是否支持多选 | - | - | - |
| 筛选节点 | 最少展示 | filter.filterRoot.minShowNum | 最少展示个数 | - | - | - |
| 筛选节点 | 展示样式 | filter.filterRoot.showType | 筛选展示样式 | UnifiedShelfFilterShowTypeVP | UnifiedShelfFilterBuilder | 筛选-展示类型 |
| 筛选节点 | 回显选中 | filter.filterRoot.echoSelectedLeafNode | 是否回显选中叶节点 | - | - | - |
| 筛选节点 | 埋点信息 | filter.filterRoot.labs | 筛选埋点数据 | UnifiedShelfFilterBtnLabsVP | UnifiedShelfFilterBuilder | 筛选-Ocean labs |
| 筛选节点 | 扩展信息 | filter.filterRoot.extra | 筛选扩展信息 | UnifiedShelfExtraVP | UnifiedShelfFilterBuilder | 筛选-extra |

## 六、主标题模块 (mainTitle)

| 模块 | 展示信息 | 字段层级名 | 字段描述 | VP标识 | VP所在类 | VP描述 |
|------|----------|------------|----------|--------|----------|--------|
| 主标题 | 标题文案 | mainTitle.title | 主标题文本 | - | - | - |
| 主标题 | 标题图标 | mainTitle.icon | 主标题图标 | - | - | - |
| 主标题 | 商品总数 | mainTitle.totalProductQty | 总商品数量（展示用） | - | - | - |
| 主标题 | 标题标签 | mainTitle.tags | 主标题标签列表 | - | - | - |
| 主标题 | 浮层信息 | mainTitle.supernatantVO | 主标题浮层信息 | - | - | - |
| 主标题浮层 | 浮层标题 | mainTitle.supernatantVO.title | 浮层标题 | - | - | - |
| 主标题浮层 | 浮层内容 | mainTitle.supernatantVO.contentList | 浮层内容区域列表 | - | - | - |
| 浮层区域 | 区域标题 | mainTitle.supernatantVO.contentList.headLabelModel | 浮层区域标题标签 | - | - | - |
| 浮层区域 | 区域文案 | mainTitle.supernatantVO.contentList.areaTextList | 浮层区域文案列表 | - | - | - |

## 七、埋点模块 (ocean)

| 模块 | 展示信息 | 字段层级名 | 字段描述 | VP标识 | VP所在类 | VP描述 |
|------|----------|------------|----------|--------|----------|--------|
| 埋点信息 | 商品标签打点 | ocean.productItemTag | 商品标签点击埋点 | - | - | - |
| 埋点信息 | 页面浏览打点 | ocean.pageView | 页面浏览埋点 | - | - | - |
| 埋点信息 | 商品打点 | ocean.productItem | 商品点击埋点 | - | - | - |
| 埋点信息 | 购买按钮打点 | ocean.buyBtn | 购买按钮埋点 | - | - | - |
| 埋点信息 | 一级筛选打点 | ocean.filterBar | 一级筛选埋点 | - | - | - |
| 埋点信息 | 二级筛选打点 | ocean.childrenFilterBar | 二级筛选埋点 | - | - | - |
| 埋点信息 | 更多数据打点 | ocean.more | 更多按钮埋点 | - | - | - |
| 埋点信息 | 整体货架打点 | ocean.wholeShelf | 整个货架埋点 | - | - | - |
| 埋点信息 | 展开收起打点 | ocean.childrenCollapse | 子项展开收起埋点 | - | - | - |
| 埋点信息 | SPU卡片打点 | ocean.spuItem | SPU卡片埋点 | - | - | - |
| 埋点信息 | SKU卡片打点 | ocean.skuItem | SKU卡片埋点 | - | - | - |
| 埋点信息 | 子项更多打点 | ocean.childrenMore | 子项更多埋点 | - | - | - |
| 埋点信息 | 优惠弹窗打点 | ocean.promoPopView | 优惠弹窗埋点 | - | - | - |
| 埋点信息 | 活动弹窗打点 | ocean.activityPromoPopView | 活动优惠弹窗埋点 | - | - | - |
| 埋点信息 | 弹窗按钮打点 | ocean.activityPromoPopViewButton | 活动弹窗按钮埋点 | - | - | - |
| 埋点信息 | 弹窗关闭打点 | ocean.activityPromoPopViewClose | 活动弹窗关闭埋点 | - | - | - |
| 埋点信息 | 预订心智条打点 | ocean.reserveMindBar | 预订心智条埋点 | - | - | - |
| 埋点信息 | 预订浮层打点 | ocean.reserveMindSupernatant | 预订心智浮层埋点 | - | - | - |

### 7.1 埋点条目结构 (ShelfOceanEntryVO)

| 模块 | 展示信息 | 字段层级名 | 字段描述 | VP标识 | VP所在类 | VP描述 |
|------|----------|------------|----------|--------|----------|--------|
| 埋点条目 | 表名 | ocean.*.category | 打点上报表名 | - | - | - |
| 埋点条目 | 额外数据 | ocean.*.labs | 额外打点数据（JSON） | - | - | - |
| 埋点条目 | 实验信息 | ocean.*.abtest | AB测试信息 | - | - | - |
| 埋点条目 | 点击点 | ocean.*.bidClick | 模块点击埋点 | - | - | - |
| 埋点条目 | 曝光点 | ocean.*.bidView | 模块曝光埋点 | - | - | - |

## 八、商品活动模块 (activity)

| 模块 | 展示信息 | 字段层级名 | 字段描述 | VP标识 | VP所在类 | VP描述 |
|------|----------|------------|----------|--------|----------|--------|
| 商品活动 | 活动信息 | filterIdAndProductAreas.productAreas.items.activity | 商品活动信息 | UnifiedShelfItemActivityVP | UnifiedProductAreaBuilder | 团单活动结束信息 |
| 商品活动 | 前缀文案 | filterIdAndProductAreas.productAreas.items.activity.preText | 倒计时前缀文案 | - | - | - |
| 商品活动 | 结束时间 | filterIdAndProductAreas.productAreas.items.activity.activityEndTime | 活动结束时间戳 | - | - | - |
| 商品活动 | 后缀文案 | filterIdAndProductAreas.productAreas.items.activity.suffix | 倒计时后缀文案 | - | - | - |

## 九、商品按钮模块 (button)

| 模块 | 展示信息 | 字段层级名 | 字段描述 | VP标识 | VP所在类 | VP描述 |
|------|----------|------------|----------|--------|----------|--------|
| 商品按钮 | 按钮信息 | filterIdAndProductAreas.productAreas.items.button | 商品操作按钮 | UnifiedShelfItemButtonVP | UnifiedProductAreaBuilder | 商品按钮 |
| 商品按钮 | 按钮名称 | filterIdAndProductAreas.productAreas.items.button.name | 按钮显示文案 | - | - | - |
| 商品按钮 | 按钮类型 | filterIdAndProductAreas.productAreas.items.button.type | 按钮类型（0普通1秒杀） | - | - | - |
| 商品按钮 | 跳转链接 | filterIdAndProductAreas.productAreas.items.button.jumpUrl | 按钮跳转链接 | - | - | - |
| 商品按钮 | 禁用状态 | filterIdAndProductAreas.productAreas.items.button.disable | 按钮是否置灰 | - | - | - |

## 十、轮播信息模块 (buttonCarouselMsg)

| 模块 | 展示信息 | 字段层级名 | 字段描述 | VP标识 | VP所在类 | VP描述 |
|------|----------|------------|----------|--------|----------|--------|
| 轮播信息 | 轮播列表 | filterIdAndProductAreas.productAreas.items.buttonCarouselMsg | 按钮上方轮播信息 | UnifiedShelfItemCarouselMsgVP | UnifiedProductAreaBuilder | 商品-轮播信息 |
| 轮播信息 | 轮播文案 | filterIdAndProductAreas.productAreas.items.buttonCarouselMsg.text | 轮播文案内容 | - | - | - |
| 轮播信息 | 轮播类型 | filterIdAndProductAreas.productAreas.items.buttonCarouselMsg.type | 轮播类型（1销量2最近购买3倒计时） | - | - | - |
| 轮播文案 | 文案内容 | filterIdAndProductAreas.productAreas.items.buttonCarouselMsg.text.text | 轮播文本内容 | - | - | - |
| 轮播文案 | 文案样式 | filterIdAndProductAreas.productAreas.items.buttonCarouselMsg.text.style | 轮播文本样式 | - | - | - |

## 十一、预热信息模块 (warmUp)

| 模块 | 展示信息 | 字段层级名 | 字段描述 | VP标识 | VP所在类 | VP描述 |
|------|----------|------------|----------|--------|----------|--------|
| 预热信息 | 预热列表 | filterIdAndProductAreas.productAreas.items.warmUp | 货架预热信息列表 | UnifiedShelfItemWarmUpVP | UnifiedProductAreaBuilder | 预热信息 |
| 预热信息 | 预热活动 | filterIdAndProductAreas.productAreas.items.warmUp.activity | 预热活动信息 | - | - | - |
| 预热信息 | 预热按钮 | filterIdAndProductAreas.productAreas.items.warmUp.button | 预热操作按钮 | - | - | - |

## 十二、子项商品模块 (subItems)

| 模块 | 展示信息 | 字段层级名 | 字段描述 | VP标识 | VP所在类 | VP描述 |
|------|----------|------------|----------|--------|----------|--------|
| 子项商品 | 子项列表 | filterIdAndProductAreas.productAreas.items.subItems | 子项商品列表 | - | - | - |
| 子项商品 | 默认展示数 | filterIdAndProductAreas.productAreas.items.defaultShowNum | 子项默认展示数量 | - | - | - |
| 子项商品 | 更多文案 | filterIdAndProductAreas.productAreas.items.moreText | 子项更多按钮文案 | - | - | - |

## 十三、优惠明细模块 (promoDetail)

| 模块 | 展示信息 | 字段层级名 | 字段描述 | VP标识 | VP所在类 | VP描述 |
|------|----------|------------|----------|--------|----------|--------|
| 优惠明细 | 优惠条目 | *.promoDetail.promoItems | 优惠条目列表 | - | - | - |
| 优惠明细 | 弹窗标题 | *.promoDetail.title | 优惠弹窗标题 | - | - | - |
| 优惠明细 | 市场价 | *.promoDetail.marketPrice | 优惠前市场价 | - | - | - |
| 优惠明细 | 到手价 | *.promoDetail.salePrice | 优惠后到手价 | - | - | - |
| 优惠明细 | 总优惠金额 | *.promoDetail.totalPromoPrice | 总优惠金额 | - | - | - |
| 优惠明细 | 优惠标签 | *.promoDetail.promoTag | 总价旁优惠标签 | - | - | - |

### 13.1 优惠条目详情 (promoItems)

| 模块 | 展示信息 | 字段层级名 | 字段描述 | VP标识 | VP所在类 | VP描述 |
|------|----------|------------|----------|--------|----------|--------|
| 优惠条目 | 优惠ID | *.promoDetail.promoItems.promoId | 优惠唯一标识 | - | - | - |
| 优惠条目 | 优惠类型 | *.promoDetail.promoItems.promoType | 优惠类型标识 | - | - | - |
| 优惠条目 | 优惠标题 | *.promoDetail.promoItems.title | 优惠标题文案 | - | - | - |
| 优惠条目 | 优惠描述 | *.promoDetail.promoItems.desc | 优惠详细描述 | - | - | - |
| 优惠条目 | 优惠金额 | *.promoDetail.promoItems.promoPrice | 优惠金额文案 | - | - | - |
| 优惠条目 | 条目标签 | *.promoDetail.promoItems.tag | 优惠条目标签 | - | - | - |
| 优惠条目 | 神券信息 | *.promoDetail.promoItems.couponPromoItem | 神券优惠详情 | - | - | - |

### 13.2 神券优惠详情 (couponPromoItem)

| 模块 | 展示信息 | 字段层级名 | 字段描述 | VP标识 | VP所在类 | VP描述 |
|------|----------|------------|----------|--------|----------|--------|
| 神券优惠 | 商品ID | *.couponPromoItem.productId | 关联商品ID | - | - | - |
| 神券优惠 | 券类型 | *.couponPromoItem.couponType | 券类型（1神会员券2免费神券） | - | - | - |
| 神券优惠 | 券标签 | *.couponPromoItem.couponTag | 券标签文案 | - | - | - |
| 神券优惠 | 按钮文案 | *.couponPromoItem.couponButtonText | 券按钮文案 | - | - | - |
| 神券优惠 | 会员点位 | *.couponPromoItem.position | 神会员点位 | - | - | - |
| 神券优惠 | 页面来源 | *.couponPromoItem.pageSource | 页面来源标识 | - | - | - |
| 神券优惠 | 膨胀参数 | *.couponPromoItem.bizToken | 膨胀参数透传 | - | - | - |
| 神券优惠 | 门槛金额 | *.couponPromoItem.requiredAmount | 膨胀前门槛（分） | - | - | - |
| 神券优惠 | 优惠金额 | *.couponPromoItem.reduceAmount | 膨胀前金额（分） | - | - | - |
| 神券优惠 | 资产类型 | *.couponPromoItem.assetType | 资产类型标识 | - | - | - |

## 十四、富文本标签通用结构 (RichLabelModel)

| 模块 | 展示信息 | 字段层级名 | 字段描述 | VP标识 | VP所在类 | VP描述 |
|------|----------|------------|----------|--------|----------|--------|
| 富文本标签 | 文本内容 | *.text.text | 标签文本内容 | - | - | - |
| 富文本标签 | 文本颜色 | *.text.textColor | 文本颜色（16进制） | - | - | - |
| 富文本标签 | 背景色 | *.text.backgroundColor | 背景颜色 | - | - | - |
| 富文本标签 | 样式类型 | *.text.style | 样式（0默认1圆角2气泡） | - | - | - |
| 富文本标签 | 跳转链接 | *.text.jumpUrl | 标签跳转链接 | - | - | - |
| 富文本标签 | 多文本 | *.text.multiText | 多文本列表 | - | - | - |
| 富文本标签 | 扩展配置 | *.text.extra | 扩展样式配置 | - | - | - |

## 十五、图标富文本标签结构 (IconRichLabelModel)

| 模块 | 展示信息 | 字段层级名 | 字段描述 | VP标识 | VP所在类 | VP描述 |
|------|----------|------------|----------|--------|----------|--------|
| 图标标签 | 富文本 | *.text | 富文本标签内容 | - | - | - |
| 图标标签 | 图标信息 | *.icon | 图标图片信息 | - | - | - |
| 图标标签 | 标签类型 | *.type | 类型（0文本1图片2图文） | - | - | - |

## 十六、商品区构造处理模块

| 模块 | 展示信息 | 字段层级名 | 字段描述 | VP标识 | VP所在类 | VP描述 |
|------|----------|------------|----------|--------|----------|--------|
| 构造处理 | 前置处理 | - | 商品区构造前置处理逻辑 | ProductAreaPreBuildHandlerVP | UnifiedProductAreaBuilder | 商品区-构造前置处理 |
| 构造处理 | 后置处理 | - | 商品区构造后置处理逻辑 | ProductAreaPostBuildHandlerVP | UnifiedProductAreaBuilder | 商品区-商品列表构造后置处理 |
| 构造处理 | 商品后置处理 | - | 单个商品构造后置处理逻辑 | UnifiedShelfItemBuildPostHandlerVP | UnifiedProductAreaBuilder | 商卡构造后置处理变化点 |

---

## VP标识说明

### 主标题模块VP
- **UnifiedShelfMainTitleVP**: 主标题变化点，控制货架主标题的整体展示逻辑

### 筛选导航模块VP
- **UnifiedShelfFilterTitleVP**: 筛选按钮名字，控制筛选项的标题展示
- **UnifiedShelfFilterShowTypeVP**: 筛选展示类型，控制筛选项的展示样式
- **UnifiedShelfFilterBtnLabsVP**: 筛选Ocean labs，控制筛选项的埋点数据
- **UnifiedShelfExtraVP**: 筛选扩展信息，控制筛选项的扩展数据

### 商品区模块VP
- **ProductAreaMoreJumpUrlVP**: 商品区查看更多跳转链接
- **ProductAreaMoreTextVP**: 商品区查看更多展示文案
- **ProductAreaDefaultShowNumVP**: 商品区默认展示数量
- **ProductAreaShowTypeVP**: 商品区展示样式
- **ProductAreaPreBuildHandlerVP**: 商品区构造前置处理
- **ProductAreaPostBuildHandlerVP**: 商品区构造后置处理

### 商品项模块VP
- **UnifiedShelfItemTitleVP**: 商品标题
- **UnifiedShelfItemSubTitleVP**: 商品副标题
- **UnifiedShelfItemPicVP**: 商品图片
- **UnifiedShelfItemPicFloatTagVP**: 商品图片角标
- **UnifiedShelfItemSalePriceVP**: 商品售卖价格
- **UnifiedShelfItemMarketPriceVP**: 市场价
- **UnifiedShelfSalePricePrefixVP**: 价格前缀
- **UnifiedShelfSalePriceSuffixVP**: 价格后缀
- **UnifiedShelfSalePriceSuffixObjectVP**: 价格后缀信息对象
- **UnifiedShelfItemSpecialTagVP**: 第三行标题-特色标签
- **UnifiedShelfItemPromoTagVP**: 商品优惠标签
- **UnifiedShelfItemPriceBottomTagVP**: 商品价格下方标签
- **UnifiedShelfItemAvailableVP**: 是否有效
- **UnifiedShelfItemJumpUrlVP**: 商品跳转链接
- **UnifiedShelfItemOceanLabsVP**: 商品Ocean打点
- **UnifiedShelfItemExtraVP**: 团单扩展数据
- **UnifiedShelfItemActivityVP**: 团单活动结束信息
- **UnifiedShelfItemButtonVP**: 商品按钮
- **UnifiedShelfItemCarouselMsgVP**: 商品轮播信息
- **UnifiedShelfItemWarmUpVP**: 预热信息
- **UnifiedShelfItemBuildPostHandlerVP**: 商卡构造后置处理变化点
- **UnifiedShelfItemDisplaySalePriceVP**: 商品展示售价

**说明：**
1. 字段层级名中的 `*` 表示可变的中间路径
2. 所有字段均支持空值，具体展示以实际业务需求为准
3. 埋点相关字段主要用于数据分析和用户行为追踪
4. 保留字段（traceId、retainMsg）仅供系统内部使用，业务方请勿依赖
5. 神券相关字段为特殊优惠类型，支持神会员权益展示
6. 子项商品支持嵌套结构，可递归包含完整的商品信息
7. 富文本标签支持多种样式和跳转功能，适用于各种展示场景
8. **VP标识仅标注与VP直接关联的子节点**，未标注VP的字段表示无直接VP关联或由父级VP统一处理
9. VP扩展点机制允许业务方通过配置或代码实现自定义逻辑，实现货架展示的灵活定制

package com.sankuai.qpro.ai.professor;

import com.meituan.mdp.langmodel.api.message.AssistantMessage;
import com.meituan.mdp.langmodel.api.message.SystemMessage;
import com.meituan.mdp.langmodel.api.message.UserMessage;
import com.meituan.mdp.langmodel.component.model.chat.OpenAIChatModel;
import com.sankuai.qpro.ai.professor.application.utils.SerializeUtils;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

/**
 * <AUTHOR>
 * @date 2024/12/4
 */
@Slf4j
@SpringBootTest
public class AgentServiceTest {

    @Autowired
    private OpenAIChatModel openAIChatModel;

    @Test
    public void toBeHumorTest() {
        SystemMessage systemMessage = SystemMessage.from("You are an assistant.");
        UserMessage userMessage = UserMessage.from("How to be a super humor boy？");
        AssistantMessage assistantMessage = openAIChatModel.sendMessages(systemMessage, userMessage);
        log.info("assistantMessage:{}", SerializeUtils.toJsonStr(assistantMessage));
    }

}

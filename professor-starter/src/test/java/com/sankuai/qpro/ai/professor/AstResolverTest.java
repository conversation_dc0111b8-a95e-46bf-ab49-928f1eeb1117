package com.sankuai.qpro.ai.professor;

import com.sankuai.qpro.ai.professor.application.code.ast.node.MethodNode;
import com.sankuai.qpro.ai.professor.application.code.ast.resolver.AstResolver;
import com.sankuai.qpro.ai.professor.application.utils.CodeBlockUtils;
import org.junit.jupiter.api.Test;

import java.io.File;

/**
 * AstResolverTest
 *
 * <AUTHOR>
 * @since 2025/8/4
 */
public class AstResolverTest {

    @Test
    public void testOriginalAst() {
        String path = "/Users/<USER>/IdeaProjects/professor";
        File pathFile = new File(path);
        if (!pathFile.exists()) {
            System.out.println("路径不存在: " + path);
            return;
        }
        if (!pathFile.isDirectory()) {
            System.out.println("路径不是目录: " + path);
            return;
        }
        AstResolver resolver = AstResolver.getInstance();
        resolver.parseProjectAst(path);
        String methodKey = "com.sankuai.dzviewscene.product.shelf.ability.fetcher.productpactivity.ProductActivitiesFetcher.build(com.sankuai.athena.viewscene.framework.ActivityCxt,com.sankuai.dzviewscene.product.shelf.ability.fetcher.productpactivity.ProductActivitiesFetcher$Request,com.sankuai.dzviewscene.product.shelf.ability.fetcher.productpactivity.ProductActivitiesFetcher$Config)";
        MethodNode method = resolver.getCache().get(path).getMethodNodeMap().get(methodKey);

        if (method != null) {
            System.out.println("\n=== ProductActivitiesFetcher.build Method ===");
            System.out.println("Method found: " + methodKey);
            System.out.println("Invocations (" + method.getInvocation().size() + "):");
            method.getInvocation().forEach(inv -> System.out.println("  " + inv));

            // 检查是否包含 convertActivityMsFromResponse
            boolean hasConvertMethod = method.getInvocation().stream().anyMatch(inv -> inv.contains("convertActivityMsFromResponse"));

            if (!hasConvertMethod) {
                System.out.println("WARNING: convertActivityMsFromResponse() call in lambda NOT detected!");
            } else {
                System.out.println("SUCCESS: convertActivityMsFromResponse() call in lambda detected!");
            }
        } else {
            System.out.println("Method not found: " + methodKey);
        }
    }

    @Test
    public void testParseProjectAst() {
        String path = "/Users/<USER>/IdeaProjects/professor";
        AstResolver resolver = AstResolver.getInstance();
        resolver.parseProjectAst(path);
        String classKey = "com.sankuai.qpro.ai.professor.application.code.agent.core.node.CodeSearch";
        System.out.println(CodeBlockUtils.getCodeBlock(resolver.getCache().get(path).getClassNodeMap().get(classKey).getSourceCode(), 1, 10));
    }

}
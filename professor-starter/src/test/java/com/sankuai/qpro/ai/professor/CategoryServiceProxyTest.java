package com.sankuai.qpro.ai.professor;

import com.google.common.collect.Lists;
import com.sankuai.qpro.ai.professor.application.model.operator.probe.agent.category.UnifiedCategoryModel;
import com.sankuai.qpro.ai.professor.application.service.operator.probe.proxy.CategoryServiceProxy;
import com.sankuai.qpro.ai.professor.application.utils.SerializeUtils;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.List;

import static org.junit.jupiter.api.Assertions.assertTrue;


/**
 * @author: wuwen<PERSON>ang
 * @create: 2025-01-02
 * @description:
 */
@SpringBootTest
public class CategoryServiceProxyTest {
    @Autowired
    private CategoryServiceProxy categoryServiceProxy;

    @Test
    public void testGetProductCategoryListByPlatformIds() {
        List<Long> categoryIds = Lists.newArrayList(80301L, 80310L);
        List<UnifiedCategoryModel> models =  categoryServiceProxy.getProductCategoryListByPlatformIds(categoryIds);
        assertTrue(models.size() == 2);
        assertTrue(models.get(0).getPlatformCategoryId() == 80301L);
        assertTrue(models.get(1).getPlatformCategoryId() == 80310L);
    }

    @Test
    public void testGetProductCategoryListByBpIds() {
        List<Long> categoryIds = Lists.newArrayList(301L, 310L);
        List<UnifiedCategoryModel> models =  categoryServiceProxy.getProductCategoryListByBpIds(categoryIds);
        assertTrue(models.size() == 2);
        assertTrue(models.get(0).getPlatformCategoryId() == 80301L);
        assertTrue(models.get(1).getPlatformCategoryId() == 80310L);
    }

    @Test
    public void testQueryPOICategoryInfo() {
        List<Long> categoryIds = Lists.newArrayList(12L, 13L);
        List<UnifiedCategoryModel> models =  categoryServiceProxy.getPoiCategoryListByIds(categoryIds);
        System.out.println(SerializeUtils.toJsonStr(models));
        assertTrue(models.size() == 2);
        assertTrue(models.get(0).getCategoryId() == 12L);
        assertTrue(models.get(1).getCategoryId() == 13L);
    }
}

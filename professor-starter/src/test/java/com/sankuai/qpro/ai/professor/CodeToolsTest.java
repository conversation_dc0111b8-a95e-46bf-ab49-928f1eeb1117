package com.sankuai.qpro.ai.professor;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.sankuai.ee.mcode.analyze.api.thrift.dto.ClassInfoDTO;
import com.sankuai.ee.mcode.analyze.api.thrift.dto.MethodDefWithoutInvocationsDTO;
import com.sankuai.ee.mcode.analyze.api.thrift.response.ProjectClassesResponse;
import com.sankuai.ee.mcode.analyze.api.thrift.service.AnalysisProjectThriftService;
import com.sankuai.qpro.ai.professor.application.code.TopologyService;
import com.sankuai.qpro.ai.professor.application.code.tools.CodeTools;
import com.sankuai.qpro.ai.professor.application.utils.ExpressionMatcherUtils;
import lombok.SneakyThrows;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.web.reactive.function.client.WebClient;

import javax.annotation.Resource;
import java.time.Duration;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 描述：
 *
 * <AUTHOR>
 * @since 2025/8/10
 */

public class CodeToolsTest {
    private final WebClient webClient = WebClient.builder()
            .codecs(configurer -> configurer.defaultCodecs().maxInMemorySize(20 * 1024 * 1024))
            .build();
    private final ObjectMapper objectMapper = new ObjectMapper();

    @SneakyThrows
    @Test
    public void findClassesTest() {
        String BASEURL = "http://10.98.14.201:8080/ai/probe/chat/api/code";
        String url = BASEURL + "/project/classes?repo=ssh://*******************/vc/dzviewscene-dealshelf-home.git";
        String jsonResponse = getHttpResponse(url);
        JsonNode root = objectMapper.readTree(jsonResponse);
        JsonNode dataNode = root.get("data");
        ProjectClassesResponse projectClassesResponse = objectMapper.convertValue(dataNode,new TypeReference<ProjectClassesResponse>() {});
        Map<String, ClassInfoDTO> filteredClasses = new HashMap<>();
        String expression = "%Code%";
        int limit = 10;
        projectClassesResponse.getClasses().forEach((k, v)-> {
            if(ExpressionMatcherUtils.matcher(k, expression)){
                filteredClasses.put(k, v);
            }
        });
        List<String> list = filteredClasses.keySet().stream().limit(limit).toList();
    }
    @SneakyThrows
    @Test
    public void findMethodTest() {
        String BASEURL = "http://10.98.14.201:8080/ai/probe/chat/api/code";
        String url = BASEURL + "/project/methods?repo=ssh://*******************/vc/dzviewscene-dealshelf-home.git";
        String jsonResponse = getHttpResponse(url);
        JsonNode root = objectMapper.readTree(jsonResponse);
        JsonNode dataNode = root.get("data");
        Map<String, MethodDefWithoutInvocationsDTO> stringMethodDef = objectMapper.convertValue(dataNode,new TypeReference<Map<String, MethodDefWithoutInvocationsDTO>>() {});
        Map<String, MethodDefWithoutInvocationsDTO> filteredMethods = new HashMap<>();
        int limit = 10;
        String expression = "%CodeShelf%";
        stringMethodDef.forEach((k, v) -> {
            if (ExpressionMatcherUtils.matcher(k, expression)) {
                filteredMethods.put(k, v);
            }
        });
        List<String> list = filteredMethods.keySet().stream().limit(limit).toList();


    }

    public String getHttpResponse(String url) {
        try {
            String response = webClient.get()
                    .uri(url)
                    .retrieve()
                    .bodyToMono(String.class)
                    .timeout(Duration.ofSeconds(30))
                    .block();
            return response;

        } catch (Exception e) {
            return "HTTP请求失败: " + e.getMessage();
        }
    }
}

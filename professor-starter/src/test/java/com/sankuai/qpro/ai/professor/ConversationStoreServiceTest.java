package com.sankuai.qpro.ai.professor;

import com.meituan.mdp.langmodel.api.message.Message;
import com.sankuai.qpro.ai.professor.application.service.operator.probe.chat.ConversationStoreService;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.List;

/**
 * @author: wuwen<PERSON><PERSON>
 * @create: 2025-01-14
 * @description:
 */
@Slf4j
@SpringBootTest
public class ConversationStoreServiceTest {

    @Autowired
    private ConversationStoreService conversationStoreService;

    @Test
    public void testQueryHistory() {
        long conversationId = 2346L;
        List<Message> historyMsg = conversationStoreService.queryHistoryMessagesForConfigSegment(conversationId);

        log.info("historyMsg = {}", historyMsg);
    }
}

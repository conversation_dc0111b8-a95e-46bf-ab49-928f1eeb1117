package com.sankuai.qpro.ai.professor;

import com.sankuai.qpro.ai.professor.application.service.developer.trading.DxSendService;
import org.apache.thrift.TException;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.Arrays;
@SpringBootTest
public class DxSentServiceTest {

    @Autowired
    private DxSendService dxSentService;

    @Test
    public void testSendChatMsg() throws TException {
        dxSentService.sendChatMsg("我是ai回复的", Arrays.asList(7026373L));
    }

}

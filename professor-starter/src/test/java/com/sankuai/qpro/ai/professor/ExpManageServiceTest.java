package com.sankuai.qpro.ai.professor;

import com.google.common.collect.Lists;
import com.sankuai.qpro.ai.professor.application.model.operator.probe.agent.exp.ExpCheckModel;
import com.sankuai.qpro.ai.professor.application.model.operator.probe.agent.exp.param.ProbeExpParam;
import com.sankuai.qpro.ai.professor.application.service.operator.probe.proxy.ExpManageProxy;
import com.sankuai.qpro.ai.professor.application.utils.SerializeUtils;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

/**
 * @author: w<PERSON><PERSON><PERSON><PERSON>
 * @create: 2024-12-14
 * @description:
 */
@Slf4j
@SpringBootTest
public class ExpManageServiceTest {

    @Autowired
    private ExpManageProxy expManageService;

    @Test
    public void testPass() throws Exception {
        // 除了平台外都相同
        // 平台=点评/美团，cid=shopinfo/c_oast293，流量20-60-20，时间1.20~1.26
        ProbeExpParam param = new ProbeExpParam();
        param.setExpIdList(Lists.newArrayList("EXP2025010300001", "EXP2025010300003"));

        ExpCheckModel expCheckModel = expManageService.checkExp(param);
        log.info(SerializeUtils.toJsonStr(expCheckModel));

        assertTrue(expCheckModel.isCheckResult());
    }

    @Test
    public void testSamePlatform() throws Exception {
        // 完全相同
        // 平台=点评，cid=shopinfo，流量20-60-20，时间1.20~1.26
        ProbeExpParam param = new ProbeExpParam();
        param.setExpIdList(Lists.newArrayList("EXP2025010300001", "EXP2025010300002"));

        ExpCheckModel expCheckModel = expManageService.checkExp(param);
        log.info(SerializeUtils.toJsonStr(expCheckModel));

        assertFalse(expCheckModel.isCheckResult());
        assertFalse(expCheckModel.isCanSkip());
        log.info(SerializeUtils.toJsonStr(expCheckModel.getCheckErrorMsg()));
    }

    @Test
    public void testDiffRatio() throws Exception {
        // 除了平台、流量以外相同
        // 平台=点评/美团，cid=shopinfo/c_oast293，流量20-60-20/20-70-10，时间1.20~1.26
        ProbeExpParam param = new ProbeExpParam();
        param.setExpIdList(Lists.newArrayList("EXP2025010300001", "EXP2025010300004"));

        ExpCheckModel expCheckModel = expManageService.checkExp(param);
        log.info(SerializeUtils.toJsonStr(expCheckModel));

        assertFalse(expCheckModel.isCheckResult());
        assertFalse(expCheckModel.isCanSkip());
        log.info(SerializeUtils.toJsonStr(expCheckModel.getCheckErrorMsg()));
    }

    @Test
    public void testNotExistExpId1() throws Exception {
        // 不存在的实验号
        ProbeExpParam param = new ProbeExpParam();
        param.setExpIdList(Lists.newArrayList("EXP2025010300001111", "EXP2025010300004"));

        ExpCheckModel expCheckModel = expManageService.checkExp(param);
        log.info(SerializeUtils.toJsonStr(expCheckModel));

        assertFalse(expCheckModel.isCheckResult());
        assertFalse(expCheckModel.isCanSkip());
        log.info(SerializeUtils.toJsonStr(expCheckModel.getCheckErrorMsg()));
    }

    @Test
    public void testNotExistExpId2() throws Exception {
        // 不存在的实验号
        ProbeExpParam param = new ProbeExpParam();
        param.setExpIdList(Lists.newArrayList("EXP2025010300001", "EXP2025010300004444"));

        ExpCheckModel expCheckModel = expManageService.checkExp(param);
        log.info(SerializeUtils.toJsonStr(expCheckModel));

        assertFalse(expCheckModel.isCheckResult());
        assertFalse(expCheckModel.isCanSkip());
        log.info(SerializeUtils.toJsonStr(expCheckModel.getCheckErrorMsg()));
    }

    @Test
    public void testNotExistExpId3() throws Exception {
        // 不存在的实验号
        ProbeExpParam param = new ProbeExpParam();
        param.setExpIdList(Lists.newArrayList("EXP2025010300001111", "EXP2025010300004444"));

        ExpCheckModel expCheckModel = expManageService.checkExp(param);
        log.info(SerializeUtils.toJsonStr(expCheckModel));

        assertFalse(expCheckModel.isCheckResult());
        assertFalse(expCheckModel.isCanSkip());
        log.info(SerializeUtils.toJsonStr(expCheckModel.getCheckErrorMsg()));
    }

}

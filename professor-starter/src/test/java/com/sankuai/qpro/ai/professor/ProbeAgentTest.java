package com.sankuai.qpro.ai.professor;

import com.google.common.collect.Lists;
import com.meituan.mdp.langmodel.api.message.AssistantMessage;
import com.meituan.mdp.langmodel.api.message.Message;
import com.meituan.mdp.langmodel.api.message.UserMessage;
import com.sankuai.qpro.ai.professor.api.enums.ConfigUnitEnum;
import com.sankuai.qpro.ai.professor.application.model.operator.probe.agent.plan.AgentPlanModel;
import com.sankuai.qpro.ai.professor.application.service.operator.probe.chat.ConversationStoreService;
import com.sankuai.qpro.ai.professor.application.service.operator.probe.ProbeAgent;
import com.sankuai.qpro.ai.professor.application.service.operator.probe.agent.ProbeConfAgent;
import com.sankuai.qpro.ai.professor.application.utils.SerializeUtils;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.List;

import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

/**
 * <AUTHOR>
 * @date 2025/1/2
 */
@Slf4j
@SpringBootTest
public class ProbeAgentTest {

    @Autowired
    private ProbeAgent probeAgent;

    @Autowired
    private ProbeConfAgent probeConfAgent;

    @Autowired
    private ConversationStoreService conversationStoreService;


    @Test
    public void executeCategoryReasoningTest() {
        Long conversionId = 1L;
        String configUnit = ConfigUnitEnum.SHELF_SUBTITLE.getCode();
        // 1. 调整分类（单消息）
        List<Message> msgList = Lists.newArrayList();
        msgList.add(UserMessage.from("我要重新调整分类"));
        AssistantMessage aiMsg1 = probeAgent.chooseTaskAgentReasoning(msgList, conversionId, configUnit);
        log.info("aiMsg1:{}", aiMsg1);
        AgentPlanModel result = SerializeUtils.toObj(aiMsg1.getContent(), AgentPlanModel.class);
        assertNotNull(result);
        assertTrue(result.getAgent().equals("generateCategoryTips"));

        // 2. 调整分类（多消息）
        List<Message> msgList2 = Lists.newArrayList();
        msgList2.add(UserMessage.from("我需要在美团点评双平台上实验，美团平台是EXP2025020800001，点评平台是EXP2025020800002"));
        msgList2.add(AssistantMessage.from("本次实验在两个平台同时开展并复用同一套实验方案，咱们只需要沟通清楚一套方案，小舟会保证它在双平台生效~  \n" +
                "\n" +
                "实验号**EXP2025020800001、EXP2025020800002**，生效时间为**2025-02-09 00:00:00~2025-02-28 00:00:00**。\n" +
                "\n" +
                "该实验包含4个分组，其中 **a** (EXP2025020800001_a、EXP2025020800002_a) 是空白对照组、 **b** (EXP2025020800001_b、EXP2025020800002_b) 是对照组，只能对 **c** (EXP2025020800001_c、EXP2025020800002_c)、**d** (EXP2025020800001_d、EXP2025020800002_d) 2 个实验分组进行配置。请根据我的指引一步步来，首先，请问你想如何配置实验分组 **c** (EXP2025020800001_c、EXP2025020800002_c) 的**副标题点位**?   你可以这样回答：\n" +
                "\n" +
                ">eg.我想基于线上逻辑进行修改，把排烟设备点位修改成预约点位，其他点位保持不变  \n" +
                "\n" +
                ">eg.我想覆盖线上逻辑，重新配置服务部位范围、艾灸手法两个点位\n" +
                "\n" +
                "[查看休闲娱乐/按摩/足疗/艾灸上单页已有属性](https://qing.sankuai)"));
        msgList2.add(UserMessage.from("我要重新调整分类"));
        AssistantMessage aiMsg2 = probeAgent.chooseTaskAgentReasoning(msgList2, conversionId, configUnit);
        log.info("aiMsg2:{}", aiMsg2);
        result = SerializeUtils.toObj(aiMsg2.getContent(), AgentPlanModel.class);
        assertNotNull(result);
        assertTrue(result.getAgent().equals("generateCategoryTips"));
    }

    @Test
    public void executeCategoryConfigTest() {
        Long conversionId = 1L;
        String configUnit = ConfigUnitEnum.SHELF_SUBTITLE.getCode();
        List<Message> msgList = Lists.newArrayList();
        // 1. 测试
        msgList.add(UserMessage.from("我要调整货架商品卡片副标题展示"));
        msgList.add(AssistantMessage.from("<lui-form submitTemplate=\"POI分类是{{poiCategory}}，团购分类是{{category}}\" submitAction=\"userMessage\" submitText=\"确认\">\n" +
                "       <lui-poi-category label=\"POI分类\" name=\"poiCategory\"></lui-poi-category>\n" +
                "<lui-tuan-category label=\"团购分类\" name=\"category\"></lui-tuan-category>\n" +
                "</lui-form>\n" +
                "\n" +
                "[查看POI分类树](https://pdc.sankuai.com/category/categorytree/back)\n" +
                "\n" +
                "[查看团购分类树](https://qing.sankuai.com/url:JTIwJTJGJTJGcWluZy5zYW5rdWFpLmNvbSUyRnNraWZmLXNjcC1tYW5hZ2Utc3lzdGVtJTJGY2F0ZWdvcnktcGFnZSUyMyUyRm1haW4lMkZjYXRlZ29yeS1saXN0/pageId:categoryPage#/main/category-list)"));
        msgList.add(UserMessage.from("POI分类是 休闲娱乐/按摩/足疗(48)，团购分类是 休闲娱乐/按摩/足疗/艾灸(84101019)"));
        AssistantMessage aiMsg1 = probeAgent.chooseTaskAgentReasoning(msgList, conversionId, configUnit);
        log.info("aiMsg1:{}", aiMsg1);
        AgentPlanModel result = SerializeUtils.toObj(aiMsg1.getContent(), AgentPlanModel.class);
        assertNotNull(result);
        assertTrue(result.getAgent().equals("generateCategoryTips"));
    }

    @Test
    public void executeExpTest() {
        Long conversionId = 1L;
        String configUnit = ConfigUnitEnum.SHELF_SUBTITLE.getCode();
        List<Message> msgList = Lists.newArrayList();
        // 1. 正常流程
        msgList.add(UserMessage.from("POI分类是休闲娱乐/按摩/足疗(48)，团购分类是休闲娱乐/按摩/足疗/艾灸(84101019)"));
        msgList.add(AssistantMessage.from("POI分类=按摩/足疗（48），团购分类=艾灸（84101019）\n" +
                "线上逻辑：副标题：由 团购次卡标签 | 过夜标签 | 服务时长 | 服务部位 | 特色灸法 | 排烟设备 | 艾灸工具 | 艾灸材料 | 其他工具 9个点位构成\n" +
                "\n" +
                "[团购次卡标签] 点位  \n" +
                "如果存在属性countCardTag，则直接使用该值。\n"));
        msgList.add(UserMessage.from("我需要在美团点评双平台上实验，美团平台是EXP2025020800001，点评平台是EXP2025020800002"));
        AssistantMessage aiMsg1 = probeAgent.chooseTaskAgentReasoning(msgList, conversionId, configUnit);
        log.info("aiMsg1:{}", aiMsg1);
        AgentPlanModel result = SerializeUtils.toObj(aiMsg1.getContent(), AgentPlanModel.class);
        assertNotNull(result);
        assertTrue(result.getAgent().equals("generateExperimentTips"));

        // 2. 用户在属性配置后需要重新更换实验
        List<Message> msgList2 = Lists.newArrayList();
        msgList2.add(UserMessage.from("[是否含餐]点位，属性免费餐食freeFood，value1=小吃简餐畅吃，展示1=小吃；value2=茶点水果，展示2=水果；value3=自助餐畅吃，展示3=自助；value4=无，不展示\n"));
        msgList2.add(AssistantMessage.from("那么[功效]点位希望怎么展示？一定要说明属性英文描述，否则小舟可能认错属性哦T_T。你可以这样对我说：\n" +
                "\n" +
                ">eg.[功效]点位，属性功效effect，value1=缓解疲劳，展示1=缓解疲劳；value2=改善睡眠，展示2=改善睡眠；value3=促进血液循环，展示3=促进循环；value4=无，不展示"));
        msgList2.add(UserMessage.from("我需要在美团点评双平台上实验，美团平台是EXP2025020800001，点评平台是EXP2025020800002"));
        AssistantMessage aiMsg2 = probeAgent.chooseTaskAgentReasoning(msgList, conversionId, configUnit);
        log.info("aiMsg2:{}", aiMsg2);
        result = SerializeUtils.toObj(aiMsg2.getContent(), AgentPlanModel.class);
        assertNotNull(result);
        assertTrue(result.getAgent().equals("generateExperimentTips"));


        // 3.用户在生成小结后需要重新更换实验
        List<Message> msgList3 = Lists.newArrayList();
        msgList3.add(UserMessage.from("确认生成策略小结"));
        msgList3.add(AssistantMessage.from("以下是基于您的需求整理的详细策略配置总结：\n" +
                "\n" +
                "---\n" +
                "\n" +
                "### 斗斛实验号\n" +
                "- 美团平台：EXP2025020800001\n" +
                "- 点评平台：EXP2025020800002\n" +
                "\n" +
                "---\n" +
                "\n" +
                "### 实验组策略详细总结\n" +
                "\n" +
                "#### 分组 c (EXP2025020800001_c、"));
        msgList3.add(UserMessage.from("我需要在美团点评双平台上实验，美团平台是EXP2025020800001，点评平台是EXP2025020800002"));
        AssistantMessage aiMsg3 = probeAgent.chooseTaskAgentReasoning(msgList, conversionId, configUnit);
        log.info("aiMsg3:{}", aiMsg3);
        result = SerializeUtils.toObj(aiMsg3.getContent(), AgentPlanModel.class);
        assertNotNull(result);
        assertTrue(result.getAgent().equals("generateExperimentTips"));
    }

    @Test
    public void executeAttrConfigTest() {
        Long conversionId = 1L;
        String configUnit = ConfigUnitEnum.SHELF_SUBTITLE.getCode();
        List<Message> msgList = Lists.newArrayList();
        // 1.正常流程1
        msgList.add(UserMessage.from("我需要在美团点评双平台上实验，美团平台是EXP2025020800001，点评平台是EXP2025020800002"));
        msgList.add(AssistantMessage.from("本次实验在两个平台同时开展并复用同一套实验方案，咱们只需要沟通清楚一套方案，小舟会保证它在双平台生效~  \n" +
                "\n" +
                "实验号**EXP2025020800001、EXP2025020800002**，生效时间为**2025-02-09 00:00:00~2025-02-28 00:00:00**。\n" +
                "\n" +
                "该实验包含4个分组，其中 **a** (EXP2025020800001_a、EXP2025020800002_a) 是空白对照组、 **b** (EXP2025020800001_b、EXP2025020800002_b) 是对照组，只能对 **c** (EXP2025020800001_c、EXP2025020800002_c)、**d** (EXP2025020800001_d、EXP2025020800002_d) 2 个实验分组进行配置。请根据我的指引一步步来，首先，请问你想如何配置实验分组 **c** (EXP2025020800001_c、EXP2025020800002_c) 的**副标题点位**?   你可以这样回答：\n" +
                "\n" +
                ">eg.我想基于线上逻辑进行修改，把排烟设备点位修改成预约点位，其他点位保持不变  \n" +
                "\n" +
                ">eg.我想覆盖线上逻辑，重新配置服务部位范围、艾灸手法两个点位\n" +
                "\n" +
                "[查看休闲娱乐/按摩/足疗/艾灸上单页已有属性](https://qing.sankuai"));
        msgList.add(UserMessage.from("我想基于线上逻辑进行修改，把排烟设备点位修改成名称前缀点位，其他点位保持不变\n"));
        AssistantMessage aiMsg = probeAgent.chooseTaskAgentReasoning(msgList, conversionId, configUnit);
        log.info("aiMsg:{}", aiMsg);
        AgentPlanModel result = SerializeUtils.toObj(aiMsg.getContent(), AgentPlanModel.class);
        assertNotNull(result);
        assertTrue(result.getAgent().equals("generateConfigTips"));

        // 2. 正常流程2
        List<Message> msgList2 = Lists.newArrayList();
        msgList2.add(UserMessage.from("我想基于线上逻辑进行修改，把排烟设备点位修改成名称前缀点位，其他点位保持不变"));
        msgList2.add(AssistantMessage.from("请告诉我[名称前缀]点位需要使用的属性KEY，你可以这样对我说：\n" +
                "\n" +
                ">eg.[名称前缀]点位，取属性[请输入属性名][请输入属性KEY]，但我不知道属性值是什么，请帮我查询属性的枚举值\n" +
                "\n" +
                ">eg.[名称前缀]点位，取属性[请输入属性名][请输入属性KEY]，当属性值为以下一个或多个时：xxx、xxx、xxx。请按照以下优先顺序展示：xxx > xxx > xxx，也就是说，优先展示列表中最前面的那个。当属性值是 xxx、xxx、xxx 时不展示"));
        msgList2.add(UserMessage.from("名称前缀点位，取属性title_prefix，当属性值为以下一个或多个时：白天专享、新客专享、新店特 惠、节日特惠。请按照以下优先顺序展示：新客专享>白天专享>节日特惠>新店特惠>节日特惠，也就是说，优先展示的是列表中最前面的那个。当属性值是主打、爆款、新品时不展示\n"));
        AssistantMessage aiMsg2 = probeAgent.chooseTaskAgentReasoning(msgList2, conversionId, configUnit);
        log.info("aiMsg2:{}", aiMsg2);
        result = SerializeUtils.toObj(aiMsg2.getContent(), AgentPlanModel.class);
        assertNotNull(result);
        assertTrue(result.getAgent().equals("generateConfigTips"));

        // 3. 正常流程3
        List<Message> msgList3 = Lists.newArrayList();
        msgList3.add(UserMessage.from("名称前缀点位，取属性title_prefix，当属性值为以下一个或多个时：白天专享、新客专享、新店特 惠、节日特惠。请按照以下优先顺序展示：新客专享>白天专享>节日特惠>新店特惠>节日特惠，也就是说，优先展示的是列表中最前面的那个。当属性值是主打、爆款、新品时不展示"));
        msgList3.add(AssistantMessage.from("咱们继续进行分组 **d** 的配置，请问你想如何配置实验分组 **d** (EXP2025020800001_d、EXP2025020800002_d) 的**副标题点位**？  \n" +
                "\n" +
                "你可以这样回答：  \n" +
                "\n" +
                ">eg. 我想基于线上逻辑进行修改，新增服务时长点位，其他点位保持不变  \n" +
                "\n" +
                ">eg. 我想覆盖线上逻辑，重新配置服务部位范围、艾灸手法两个点位"));
        msgList3.add(UserMessage.from("我想覆盖线上逻辑，配置3个点位，名称前缀、是否含餐、功效点位\n"));
        AssistantMessage aiMsg3 = probeAgent.chooseTaskAgentReasoning(msgList3, conversionId, configUnit);
        log.info("aiMsg3:{}", aiMsg3);
        result = SerializeUtils.toObj(aiMsg3.getContent(), AgentPlanModel.class);
        assertNotNull(result);
        assertTrue(result.getAgent().equals("generateConfigTips"));
    }

    @Test
    public void executeGenerateTest() {
        Long conversionId = 1L;
        String configUnit = ConfigUnitEnum.SHELF_SUBTITLE.getCode();
        List<Message> msgList = Lists.newArrayList();
        // 1. 正常流程(生成小结)
        msgList.add(UserMessage.from("[功效]点位，属性功效massage_efficacy，当属性值为以下一个或多个时，请按照以下优先顺序展示： 健脾 > 祛湿 > 护肝 > 养气血，也就是说，优先展示的症状是列表中最前面的那个。当属性值是养肤排毒、身体放松、温经通络、暖宫调经、眼部放松、头部\n" +
                "放松、助睡眠时不展示"));
        msgList.add(AssistantMessage.from("实验所需的配置信息已全部收集完毕，请确认是否要生成策略小结，以便检查实验方案是否有错漏。你可以这样对我说：\n" +
                "\n" +
                ">eg. 确认生成策略小结  \n" +
                "\n" +
                ">eg. 我想修改实验组c的方案，将[服务时长]点位替换成[服务部位]点位"));
        msgList.add(UserMessage.from("确认生成策略小结"));
        AssistantMessage aiMsg = probeAgent.chooseTaskAgentReasoning(msgList, conversionId, configUnit);
        log.info("aiMsg:{}", aiMsg);
        AgentPlanModel result = SerializeUtils.toObj(aiMsg.getContent(), AgentPlanModel.class);
        assertNotNull(result);
        assertTrue(result.getAgent().equals("generateSummaryOrCode"));

        // 2. 正常流程（生成代码）
        msgList.add(UserMessage.from("确认生成策略小结"));
        msgList.add(AssistantMessage.from("以下是基于您的需求整理的详细策略配置总结：\n" +
                "\n" +
                "---"));
        msgList.add(UserMessage.from("确认生成代码"));
        AssistantMessage aiMsg2 = probeAgent.chooseTaskAgentReasoning(msgList, conversionId, configUnit);
        log.info("aiMsg2:{}", aiMsg2);
        result = SerializeUtils.toObj(aiMsg2.getContent(), AgentPlanModel.class);
        assertNotNull(result);
        assertTrue(result.getAgent().equals("generateSummaryOrCode"));

        // 3. 正常流程（重新生成小结）
        msgList.add(UserMessage.from("确认生成代码"));
        msgList.add(AssistantMessage.from("以下是基于您的需求整理的详细策略配置代码：\n" +
                "\n" +
                "---"));
        msgList.add(UserMessage.from("我想重新生成策略小结"));
        AssistantMessage aiMsg3 = probeAgent.chooseTaskAgentReasoning(msgList, conversionId, configUnit);
        log.info("aiMsg3:{}", aiMsg3);
        result = SerializeUtils.toObj(aiMsg3.getContent(), AgentPlanModel.class);
        assertNotNull(result);
        assertTrue(result.getAgent().equals("generateSummaryOrCode"));

        // 4. 正常流程 (重新生成代码)
        msgList.add(UserMessage.from("确认生成代码"));
        msgList.add(AssistantMessage.from("以下是基于您的需求整理的详细策略配置代码：\n" +
                "\n" +
                "---"));
        msgList.add(UserMessage.from("生成代码存在问题，我想重新生成代码"));
        AssistantMessage aiMsg4 = probeAgent.chooseTaskAgentReasoning(msgList, conversionId, configUnit);
        log.info("aiMsg4:{}", aiMsg4);
        result = SerializeUtils.toObj(aiMsg4.getContent(), AgentPlanModel.class);
        assertNotNull(result);
        assertTrue(result.getAgent().equals("generateSummaryOrCode"));
    }

    @Test
    public void executeInValidQuestionTest() {
        Long conversionId = 1L;
        String configUnit = ConfigUnitEnum.SHELF_SUBTITLE.getCode();
        List<Message> msgList = Lists.newArrayList();
        msgList.add(UserMessage.from("能帮我查查当前天气是什么嘛"));
        AssistantMessage aiMsg = probeAgent.chooseTaskAgentReasoning(msgList, conversionId, configUnit);
        log.info("aiMsg:{}", aiMsg);
        AgentPlanModel result = SerializeUtils.toObj(aiMsg.getContent(), AgentPlanModel.class);
        assertNotNull(result);
        assertTrue(!result.getHitAgent());
    }
}

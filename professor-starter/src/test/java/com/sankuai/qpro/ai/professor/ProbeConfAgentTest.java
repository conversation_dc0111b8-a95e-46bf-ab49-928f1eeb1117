package com.sankuai.qpro.ai.professor;

import com.google.common.collect.Lists;
import com.meituan.mdp.langmodel.api.message.AssistantMessage;
import com.meituan.mdp.langmodel.api.message.Message;
import com.meituan.mdp.langmodel.api.message.SystemMessage;
import com.meituan.mdp.langmodel.api.message.UserMessage;
import com.meituan.mdp.langmodel.component.function.MdpFunctionRegistry;
import com.meituan.mdp.langmodel.component.model.chat.OpenAIChatModel;
import com.sankuai.qpro.ai.professor.application.constants.ToolTypeConstant;
import com.sankuai.qpro.ai.professor.api.enums.ConfigUnitEnum;
import com.sankuai.qpro.ai.professor.application.model.operator.probe.agent.conf.ProbeConfParam;
import com.sankuai.qpro.ai.professor.application.model.operator.probe.agent.conf.<PERSON><PERSON><PERSON>ck<PERSON><PERSON>ult;
import com.sankuai.qpro.ai.professor.application.model.operator.probe.agent.conf.param.CheckPVValidParam;
import com.sankuai.qpro.ai.professor.application.service.operator.probe.agent.ProbeConfAgent;
import com.sankuai.qpro.ai.professor.application.utils.SerializeUtils;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.util.Assert;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/12/31
 */
@Slf4j
@SpringBootTest
public class ProbeConfAgentTest {

    @Autowired
    private ProbeConfAgent probeConfAgent;

    @Autowired
    @Qualifier("gpt4oModel")
    private OpenAIChatModel gpt4oModel;

    @Test
    public void generateTaskListOfConfigTipsTest() {
        ProbeConfParam param = new ProbeConfParam();
        param.setConfigUnit(ConfigUnitEnum.SHELF_SUBTITLE.name());

        List<Message> list = generateTaskListOfConfigTips();
//        param.setMsgList(getStrList(list));

        while (true) {
            String result = probeConfAgent.generateTaskListOfConfigTips(param);
            log.info("result:{}", result);
            list.add(AssistantMessage.from(result));

            String userInput = "";
            log.info("请输入用户消息：{}", userInput);
            list.add(UserMessage.from(userInput));
        }

    }

    private List<String> getStrList(List<Message> msgList) {
        List<String> list = Lists.newArrayList();
        for (Message message : msgList) {
            list.add((String)message.getContent());
        }
        return list;
    }

    private List<Message> generateTaskListOfConfigTips() {
        List<Message > list = Lists.newArrayList();
        list.add(AssistantMessage.from("欢迎来到【轻舟-\"探针\"实验平台】，我是智能助手阿舟。\"探针\"是支持运营敏捷迭代属性策略的实验平台，请问今天有什么可以帮你"));
        list.add(UserMessage.from("我要调整货架商品卡片副标题展示？"));
        list.add(AssistantMessage.from("请问您要修改的POI分类和团购分类是什么？"));
        list.add(UserMessage.from("POI分类是休闲娱乐-按摩/足疗，团购分类是休闲娱乐-按摩/足疗-拔罐"));
        list.add(AssistantMessage.from("货架副标题当POI分类为休闲娱乐-按摩/足疗且商品分类为拔罐时的展示规则如下：依次拼接买赠标签、团购次卡标签、过夜标签、服务时长、拔罐服务部位和拔罐罐体这六个点位，显示前三个有值的点位。当前仅支持配置实验，请问斗斛实验号是多少？"));
        list.add(UserMessage.from("斗斛实验号是EXP2024121600005"));
        list.add(AssistantMessage.from("实验号EXP2024121600005下有4个实验分组，其中EXP2024121600006_a和EXP2024121600006_b是对照组，EXP2024121600006_c和EXP2024121600006_d是实验组"));
        return list;
    }


    @Test
    public void exceptionTest() {
        ProbeConfParam param = new ProbeConfParam();
        param.setConfigUnit(ConfigUnitEnum.SHELF_SUBTITLE.getCode());

        List<Message> list = generateTaskListOfConfigTips();
//        param.setMsgList(getStrList(list));
        list.add(AssistantMessage.from(probeConfAgent.generateTaskListOfConfigTips(param)));
        list.add(UserMessage.from("展示 服务时长 | 部位 | 是否含餐 三个点位"));

        list.add(AssistantMessage.from(probeConfAgent.generateTaskListOfConfigTips(param)));
        list.add(UserMessage.from("[服务时长] 点位，取属性服务时长，后拼接分钟"));

        while (true) {
            String result = probeConfAgent.generateTaskListOfConfigTips(param);
            log.info("result:{}", result);
            list.add(AssistantMessage.from(result));

            String userInput = "";
            log.info("请输入用户消息：{}", userInput);
            list.add(UserMessage.from(userInput));
        }

    }

    @Test
    public void testParam() {
        String systemMsg = "你是个属性配置检查专家，帮我检查下输入的属性配置是否合法。POI分类是休闲娱乐/按摩/足疗(48)，团购分类是休闲娱乐/按摩/足疗/艾灸(84101019)。";

        String usrMsg1 = "我想配置两个点位";
        String usrMsg2 = "我想基于线上逻辑进行修改，把排烟设备点位修改成预约点位，其他点位保持不变";
        String usrMsg3 = "[预约]点位，取属性工作日提前预约时间单位weekday_advance_reservation_time_unit，请告知我该属性枚举值";
        String usrMsg4 = "[功效]点位，属性功效massage_efficacy，按下列值的顺序，祛湿>健脾>护肝，展示优先级最高的值，当值是养肤排毒、身体放松、眼部放松、头部放松、助睡眠时不展示";


        // 测试1
        List<Message> msgList1 = Lists.newArrayList(SystemMessage.from(systemMsg), UserMessage.from(usrMsg1));
        AssistantMessage assistantMessage1 = gpt4oModel.sendMessagesUseFuncs(msgList1, MdpFunctionRegistry.getFunctionsByType(ToolTypeConstant.PROBE_CONF));
        log.info("assistantMessage1:{}", SerializeUtils.toJsonStr(assistantMessage1.getContent()));

        // 测试2
        List<Message> msgList2 = Lists.newArrayList(SystemMessage.from(systemMsg), UserMessage.from(usrMsg2));
        AssistantMessage assistantMessage2 = gpt4oModel.sendMessagesUseFuncs(msgList2, MdpFunctionRegistry.getFunctionsByType(ToolTypeConstant.PROBE_CONF));
        log.info("assistantMessage2:{}", SerializeUtils.toJsonStr(assistantMessage2.getContent()));


        // 测试3
        List<Message> msgList3 = Lists.newArrayList(SystemMessage.from(systemMsg), UserMessage.from(usrMsg3));
        AssistantMessage assistantMessage3 = gpt4oModel.sendMessagesUseFuncs(msgList3, MdpFunctionRegistry.getFunctionsByType(ToolTypeConstant.PROBE_CONF));
        log.info("assistantMessage3:{}", SerializeUtils.toJsonStr(assistantMessage3.getContent()));


        // 测试4
        List<Message> msgList4 = Lists.newArrayList(SystemMessage.from(systemMsg), UserMessage.from(usrMsg4));
        AssistantMessage assistantMessage4 = gpt4oModel.sendMessagesUseFuncs(msgList4, MdpFunctionRegistry.getFunctionsByType(ToolTypeConstant.PROBE_CONF));
        log.info("assistantMessage4:{}", SerializeUtils.toJsonStr(assistantMessage4.getContent()));


        String usrMsg = "[特色艾灸] 点位，属性艾灸手法serviceTechnique，value1=盒灸，展示1=用盒灸；value2=仪器灸，展示2=用仪器灸；value3=铺灸，展示3=铺灸；value4=手工悬灸，展示4=手工灸；" +
                "[通用服务部位]点位，属性服务部位范围bodyRegion，value1=全身，展示1=含全身；value2=局部部位，展示2=局部。" +
                "[服务时长]点位，属性服务时长serviceDurationInt，后拼接分钟。" +
                "[功效]点位，属性功效massage_efficacy，按下列值的顺序，祛湿>健脾>护肝>养气血，展示优先级最高的值，当值是养肤排毒、身体放松、温经通络、暖宫调经、眼部放松、头部放松、助睡眠时不展示"
                ;
        List<Message> msgList = Lists.newArrayList(SystemMessage.from(systemMsg), UserMessage.from(usrMsg));

        AssistantMessage assistantMessage = gpt4oModel.sendMessagesUseFuncs(msgList, MdpFunctionRegistry.getFunctionsByType(ToolTypeConstant.PROBE_CONF));
        Assert.notNull(assistantMessage);
        log.info("assistantMessage:{}", assistantMessage.getContent());
    }


    @Test
    public void testParam1() {
        Long productCategoryId = 84101019L;
        String systemMsg = "你是个属性配置检查专家，帮我检查下输入的属性配置是否合法。POI分类是休闲娱乐/按摩/足疗(48)，团购分类是休闲娱乐/按摩/足疗/艾灸(84101019)。";

        String usrMsg1 = "我想配置两个点位";
        String usrMsg2 = "我想基于线上逻辑进行修改，把排烟设备点位修改成预约点位，其他点位保持不变";
        String usrMsg3 = "[预约]点位，取属性工作日提前预约时间单位weekday_advance_reservation_time_unit，但我不知道属性值是什么，请帮我查询属性的枚举值";
        String usrMsg4 = "[预约]点位，取属性工作日提前预约时间单位weekday_advance_reservation_time_unit，value1=hour，展示1=小时；value2=day，展示2=天";
        String usrMsg5 = "[功效]点位，属性功效massage_efficacy，按下列值的顺序，祛湿>健脾>护肝，展示优先级最高的值，当值是养肤排毒、身体放松、眼部放松、头部放松、助睡眠时不展示";

        String usrMsg6 = "[功效]点位，属性功效massage_efficacy，当属性值是祛湿、养气血、温经通络、暖宫调经、助睡眠、养肤排毒、健脾、护肝、身体放松、头部放松、眼部放松中的一个或多个时，按下列顺序，祛湿>养气血>温经通络>暖宫调经>助睡眠>养肤排毒>健脾>护肝>身体放松>头部放松>眼部放松，展示优先级最高的值";

        // 测试1
        CheckPVValidParam param1 = CheckPVValidParam.of(productCategoryId, usrMsg1);
        PVCheckResult checkPVValid1 = probeConfAgent.checkPVValid(param1);
        log.info("checkPVValid1:{}", SerializeUtils.toJsonStr(checkPVValid1));

        // 测试2
        CheckPVValidParam param2 = CheckPVValidParam.of(productCategoryId, usrMsg2);
        PVCheckResult checkPVValid2 = probeConfAgent.checkPVValid(param2);
        log.info("checkPVValid3:{}", SerializeUtils.toJsonStr(checkPVValid2));


        // 测试3 有问题
        CheckPVValidParam param3 = CheckPVValidParam.of(productCategoryId, usrMsg3);
        PVCheckResult checkPVValid3 = probeConfAgent.checkPVValid(param3);
        log.info("checkPVValid3:{}", SerializeUtils.toJsonStr(checkPVValid3));


        // 测试4
        CheckPVValidParam param4 = CheckPVValidParam.of(productCategoryId, usrMsg4);
        PVCheckResult checkPVValid4 = probeConfAgent.checkPVValid(param4);
        log.info("checkPVValid4:{}", SerializeUtils.toJsonStr(checkPVValid4));

        // 测试5
        CheckPVValidParam param5 = CheckPVValidParam.of(productCategoryId, usrMsg5);
        PVCheckResult checkPVValid5 = probeConfAgent.checkPVValid(param5);
        log.info("checkPVValid5:{}", SerializeUtils.toJsonStr(checkPVValid5));

        // 测试6
        CheckPVValidParam param6 = CheckPVValidParam.of(productCategoryId, usrMsg6);
        PVCheckResult checkPVValid6 = probeConfAgent.checkPVValid(param6);
        log.info("checkPVValid6:{}", SerializeUtils.toJsonStr(checkPVValid6));


        String usrMsg = "[特色艾灸] 点位，属性艾灸手法serviceTechnique，value1=盒灸，展示1=用盒灸；value2=仪器灸，展示2=用仪器灸；value3=铺灸，展示3=铺灸；value4=手工悬灸，展示4=手工灸；" +
                "[通用服务部位]点位，属性服务部位范围bodyRegion，value1=全身，展示1=含全身；value2=局部部位，展示2=局部。" +
                "[服务时长]点位，属性服务时长serviceDurationInt，后拼接分钟。" +
                "[功效]点位，属性功效massage_efficacy，按下列值的顺序，祛湿>健脾>护肝>养气血，展示优先级最高的值，当值是养肤排毒、身体放松、温经通络、暖宫调经、眼部放松、头部放松、助睡眠时不展示"
                ;
        List<Message> msgList = Lists.newArrayList(SystemMessage.from(systemMsg), UserMessage.from(usrMsg));

        AssistantMessage assistantMessage = gpt4oModel.sendMessagesUseFuncs(msgList, MdpFunctionRegistry.getFunctionsByType(ToolTypeConstant.PROBE_CONF));
        Assert.notNull(assistantMessage);
        log.info("assistantMessage:{}", assistantMessage.getContent());
    }
}

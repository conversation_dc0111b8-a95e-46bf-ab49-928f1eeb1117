package com.sankuai.qpro.ai.professor;

import com.google.common.collect.Lists;
import com.meituan.mdp.langmodel.api.message.Message;
import com.meituan.mdp.langmodel.api.message.UserMessage;
import com.sankuai.qpro.ai.professor.api.enums.ConfigUnitEnum;
import com.sankuai.qpro.ai.professor.application.model.operator.probe.agent.gene.GeneStatisticModel;
import com.sankuai.qpro.ai.professor.application.model.operator.probe.chat.AgentOutputModel;
import com.sankuai.qpro.ai.professor.application.model.operator.probe.agent.gene.param.ProbeGeneParam;
import com.sankuai.qpro.ai.professor.application.model.operator.probe.agent.plan.ValidateModel;
import com.sankuai.qpro.ai.professor.application.model.operator.probe.agent.gene.StructureInfoModel;
import com.sankuai.qpro.ai.professor.application.service.operator.probe.agent.ProbeGeneAgent;
import com.sankuai.qpro.ai.professor.application.utils.SerializeUtils;
import com.sankuai.qpro.ai.professor.entity.ConfigUnitEntity;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.mockito.invocation.InvocationOnMock;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.io.BufferedReader;
import java.io.FileReader;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.doAnswer;

/**
 * @author: wuwenqiang
 * @create: 2025-01-04
 * @description:
 */
@Slf4j
@SpringBootTest
public class ProbeGeneAgentTest {

    @Autowired
    private ProbeGeneAgent probeGeneAgent;


    @Test
    public void testGenerateCode() {
        ProbeGeneParam param = new ProbeGeneParam();
        param.setConversationId(2435L);
        param.setConfigUnit(ConfigUnitEnum.SHELF_SUBTITLE.getCode());

        AgentOutputModel result = probeGeneAgent.generateSummaryOrCode(param);
        log.info("result:{}", result);

    }

    @Test
    public void testGenerateSummary() {
        ProbeGeneParam param = new ProbeGeneParam();
        param.setConversationId(2384L);
        param.setConfigUnit(ConfigUnitEnum.SHELF_SUBTITLE.getCode());

        List<Message> historyMsg = probeGeneAgent.queryHistoryMsg(param.getConversationId());

        historyMsg.remove(historyMsg.size() - 1);

        String summary = probeGeneAgent.generateSummary(param, historyMsg);
        log.info("summary = {}", summary);

        historyMsg.add(UserMessage.from("实验分组c需要基于线上逻辑修改，而不是覆盖线上逻辑，能帮我重新生成策略小结嘛？"));

        String summary2 = probeGeneAgent.generateSummary(param, historyMsg);
        log.info("summary2 = {}", summary2);
    }

    @Test
    public void testExtractStruct() throws Exception {
        ProbeGeneParam param = new ProbeGeneParam();
        param.setConversationId(2384L);
        param.setConfigUnit(ConfigUnitEnum.SHELF_SUBTITLE.getCode());
        param.setPoiCategoryList(Lists.newArrayList(48));
        param.setProductCategoryId(84101019L);

        List<Message> historyMsg = probeGeneAgent.queryHistoryMsg(param.getConversationId());

        historyMsg.remove(historyMsg.size() - 1);

        String summary = probeGeneAgent.generateSummary(param, historyMsg);

        StructureInfoModel result = probeGeneAgent.extractStructure(param, summary);
        log.info("result = {}", result);
    }

    @Test
    public void testGenerateInitDSL() {
        ProbeGeneParam param = new ProbeGeneParam();
        param.setConversationId(2451L);
        param.setConfigUnit(ConfigUnitEnum.SHELF_SUBTITLE.getCode());
        param.setPoiCategoryList(Lists.newArrayList(48));
        param.setProductCategoryId(84101019L);

        List<Message> historyMsg = probeGeneAgent.queryHistoryMsg(param.getConversationId());

        historyMsg.remove(historyMsg.size() - 1);

        String summary = probeGeneAgent.generateSummary(param, historyMsg);

        String onlineConfig = probeGeneAgent.getOnlineConfig(param, new ConfigUnitEntity());
        String initDSL = probeGeneAgent.generateInitDSL(param, historyMsg, summary, onlineConfig);
        log.info("dsl = {}", initDSL);

        ValidateModel validateModel = probeGeneAgent.validateDSL(param, summary, initDSL);
        log.info("validate model = {}", validateModel);
    }

    @Test
    public void testGenerateInitDSL2() {
        ProbeGeneParam param = new ProbeGeneParam();
        param.setConversationId(2384L);
        param.setConfigUnit(ConfigUnitEnum.SHELF_SUBTITLE.getCode());
        param.setPoiCategoryList(Lists.newArrayList(48));
        param.setProductCategoryId(84101019L);

        List<Message> historyMsg = probeGeneAgent.queryHistoryMsg(param.getConversationId());

        historyMsg.remove(historyMsg.size() - 1);

        String summary = probeGeneAgent.generateSummary(param, historyMsg);

        String onlineConfig = probeGeneAgent.getOnlineConfig(param, new ConfigUnitEntity());
        String initDSL = probeGeneAgent.generateInitDSL(param, historyMsg, summary, onlineConfig);
        log.info("dsl = {}", initDSL);

        ValidateModel validateModel = probeGeneAgent.validateDSL(param, summary, initDSL);
        log.info("validate model = {}", validateModel);
    }

    @Test
    public void testGenerateInitDSLWithoutHistoryMsg() {
        ProbeGeneParam param = new ProbeGeneParam();
        param.setConversationId(2398L);
        param.setConfigUnit(ConfigUnitEnum.SHELF_SUBTITLE.getCode());
        param.setPoiCategoryList(Lists.newArrayList(48));
        param.setProductCategoryId(84101019L);

        List<Message> historyMsg = probeGeneAgent.queryHistoryMsg(param.getConversationId());

        historyMsg.remove(historyMsg.size() - 1);

        String summary = probeGeneAgent.generateSummary(param, historyMsg);

        String onlineConfig = probeGeneAgent.getOnlineConfig(param, new ConfigUnitEntity());
        String initDSL = probeGeneAgent.generateInitDSL(param, historyMsg, summary, onlineConfig);
        log.info("dsl = {}", initDSL);

        ValidateModel validateModel = probeGeneAgent.validateDSL(param, summary, initDSL);
        log.info("validate model = {}", validateModel);
    }



    @Test
    public void testRegenerateCode() {
        ProbeGeneParam param = new ProbeGeneParam();
        param.setConversationId(2398L);
        param.setConfigUnit(ConfigUnitEnum.SHELF_SUBTITLE.getCode());
        param.setPoiCategoryList(Lists.newArrayList(48));
        param.setProductCategoryId(84101019L);

        List<Message> historyMsg = probeGeneAgent.queryHistoryMsg(param.getConversationId());

        historyMsg.remove(historyMsg.size() - 1);

        String summary = probeGeneAgent.generateSummary(param, historyMsg);

        String initDSL = "def result = new ItemSubTitleVO()\n" +
                "result.setJoinType(0) // 使用竖线分隔\n" +
                "def tags = new ArrayList<StyleTextModel>()\n" +
                "def product = param.productM\n" +
                "\n" +
                "// 获取必要的属性值\n" +
                "def countCardTag = product.getAttrValueFromAllAttrs(\"attr_count_card_tag\")\n" +
                "def foodName = product.getAttrValueFromAllAttrs(\"foodName\")\n" +
                "def serviceDurationTag = product.getAttrValueFromAllAttrs(\"attr_joy_service_duration_tag\")\n" +
                "def bodyRegion = product.getAttrValueFromAllAttrs(\"bodyRegion\")\n" +
                "def serviceBodyRange = product.getAttrValueFromAllAttrs(\"serviceBodyRange\")\n" +
                "def moxibustionMethod = product.getAttrValueFromAllAttrs(\"moxibustionMethod\")\n" +
                "def unclassifiedTools = product.getAttrValueFromAllAttrs(\"unclassifiedTools\")\n" +
                "def moxibustionTool = product.getAttrValueFromAllAttrs(\"moxibustionTool\")\n" +
                "def moxibustionMaterial = product.getAttrValueFromAllAttrs(\"moxibustionMaterial\")\n" +
                "def disposableMaterial = product.getAttrValueFromAllAttrs(\"disposableMaterial\")\n" +
                "\n" +
                "// 检查斗斛实验分组\n" +
                "def douhuList = param.douHuList\n" +
                "def strategyMT = douhuList?.find { it.expId == \"EXP2025010700001\" }?.sk\n" +
                "def strategyDP = douhuList?.find { it.expId == \"EXP2025010700002\" }?.sk\n" +
                "\n" +
                "// 构建副标题内容\n" +
                "def subtitles = []\n" +
                "\n" +
                "if (strategyMT == \"EXP2025010700001_c\" || strategyDP == \"EXP2025010700002_c\") {\n" +
                "    // 实验分组 c 逻辑\n" +
                "    if (countCardTag) {\n" +
                "        subtitles << countCardTag\n" +
                "    }\n" +
                "    if (foodName) {\n" +
                "        subtitles << foodName\n" +
                "    }\n" +
                "    if (serviceDurationTag) {\n" +
                "        subtitles << serviceDurationTag\n" +
                "    }\n" +
                "    if (bodyRegion == \"全身\") {\n" +
                "        subtitles << \"全身\"\n" +
                "    } else if (serviceBodyRange) {\n" +
                "        def bodyParts = [\"肩颈\", \"腰背\", \"头部\", \"四肢\", \"足部\", \"耳部\", \"颈椎\", \"肩部\", \"腰部\", \"背部\", \"腹部\", \"脊柱\", \"腿部\", \"手臂\", \"面部\", \"眼部\"]\n" +
                "        def serviceParts = serviceBodyRange.split(\"、\")\n" +
                "        def part = bodyParts.find { it in serviceParts }\n" +
                "        if (part) {\n" +
                "            subtitles << part\n" +
                "        }\n" +
                "    }\n" +
                "    if (moxibustionMethod) {\n" +
                "        subtitles << moxibustionMethod\n" +
                "    }\n" +
                "    if (unclassifiedTools) {\n" +
                "        subtitles << unclassifiedTools\n" +
                "    }\n" +
                "    if (moxibustionTool) {\n" +
                "        subtitles << moxibustionTool\n" +
                "    }\n" +
                "    if (moxibustionMaterial) {\n" +
                "        subtitles << moxibustionMaterial\n" +
                "    }\n" +
                "    def disposableList = disposableMaterial ? disposableMaterial.split(\"、\").findAll { it != \"一次性工具\" } : []\n" +
                "    def unclassifiedList = unclassifiedTools ? unclassifiedTools.split(\"、\") : []\n" +
                "    def otherTools = (disposableList + unclassifiedList).unique()\n" +
                "    def toolOrder = [\"耳内镜\", \"一次性床单\", \"一次性短裤\", \"香薰\", \"眼罩\", \"电动按摩床\", \"一次性拖鞋\", \"一次性按摩巾\", \"消毒按摩服\"]\n" +
                "    def tool = toolOrder.find { it in otherTools }\n" +
                "    if (tool) {\n" +
                "        subtitles << tool\n" +
                "    }\n" +
                "    subtitles = subtitles.take(3)\n" +
                "} else if (strategyMT == \"EXP2025010700001_d\" || strategyDP == \"EXP2025010700002_d\") {\n" +
                "    // 实验分组 d 逻辑\n" +
                "    if (foodName) {\n" +
                "        subtitles << foodName\n" +
                "    }\n" +
                "    if (bodyRegion == \"全身\") {\n" +
                "        subtitles << \"全身\"\n" +
                "    } else if (bodyRegion == \"局部部位\") {\n" +
                "        subtitles << \"局部部位\"\n" +
                "    }\n" +
                "} else {\n" +
                "    // 对照组逻辑（复用线上逻辑）\n" +
                "    if (countCardTag) {\n" +
                "        subtitles << countCardTag\n" +
                "    }\n" +
                "    def overNightTag = product.getAttrValueFromAllAttrs(\"attr_over_night_tag\")\n" +
                "    if (overNightTag) {\n" +
                "        subtitles << overNightTag\n" +
                "    }\n" +
                "    if (serviceDurationTag) {\n" +
                "        subtitles << serviceDurationTag\n" +
                "    }\n" +
                "    if (bodyRegion == \"全身\") {\n" +
                "        subtitles << \"全身\"\n" +
                "    } else if (serviceBodyRange) {\n" +
                "        def bodyParts = [\"肩颈\", \"腰背\", \"头部\", \"四肢\", \"足部\", \"耳部\", \"颈椎\", \"肩部\", \"腰部\", \"背部\", \"腹部\", \"脊柱\", \"腿部\", \"手臂\", \"面部\", \"眼部\"]\n" +
                "        def serviceParts = serviceBodyRange.split(\"、\")\n" +
                "        def part = bodyParts.find { it in serviceParts }\n" +
                "        if (part) {\n" +
                "            subtitles << part\n" +
                "        }\n" +
                "    }\n" +
                "    if (moxibustionMethod) {\n" +
                "        subtitles << moxibustionMethod\n" +
                "    }\n" +
                "    if (unclassifiedTools) {\n" +
                "        subtitles << unclassifiedTools\n" +
                "    }\n" +
                "    if (moxibustionTool) {\n" +
                "        subtitles << moxibustionTool\n" +
                "    }\n" +
                "    if (moxibustionMaterial) {\n" +
                "        subtitles << moxibustionMaterial\n" +
                "    }\n" +
                "    def disposableList = disposableMaterial ? disposableMaterial.split(\"、\").findAll { it != \"一次性工具\" } : []\n" +
                "    def unclassifiedList = unclassifiedTools ? unclassifiedTools.split(\"、\") : []\n" +
                "    def otherTools = (disposableList + unclassifiedList).unique()\n" +
                "    def toolOrder = [\"耳内镜\", \"一次性床单\", \"一次性短裤\", \"香薰\", \"眼罩\", \"电动按摩床\", \"一次性拖鞋\", \"一次性按摩巾\", \"消毒按摩服\"]\n" +
                "    def tool = toolOrder.find { it in otherTools }\n" +
                "    if (tool) {\n" +
                "        subtitles << tool\n" +
                "    }\n" +
                "    subtitles = subtitles.take(3)\n" +
                "}\n" +
                "\n" +
                "// 转换为StyleTextModel列表\n" +
                "subtitles.each { text ->\n" +
                "    def model = new StyleTextModel()\n" +
                "    model.setText(text)\n" +
                "    model.setStyle(TextStyleEnum.TEXT_GRAY.getType())\n" +
                "    tags.add(model)\n" +
                "}\n" +
                "result.setTags(tags)\n" +
                "return result\n";
        String onlineConfig = probeGeneAgent.getOnlineConfig(param, new ConfigUnitEntity());
        String result = probeGeneAgent.retryGenerateDSL(param, historyMsg, summary, onlineConfig, initDSL, "groovy的非实验组应该直接返回空");
        log.info("result = {}", result);
    }

}

package com.sankuai.qpro.ai.professor;

import com.sankuai.qpro.ai.professor.api.enums.ConfigUnitEnum;
import com.sankuai.qpro.ai.professor.application.model.operator.probe.agent.category.param.ProbeCategoryParam;
import com.sankuai.qpro.ai.professor.application.service.operator.probe.agent.ProbeCategoryAgent;
import lombok.extern.slf4j.Slf4j;
import org.assertj.core.util.Lists;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

/**
 * <AUTHOR>
 * @date 2025/1/2
 */
@Slf4j
@SpringBootTest
public class ProbeParamAgentTest {

    @Autowired
    private ProbeCategoryAgent probeParamAgent;

    @Test
    public void shelfSubTitleConfigTest() {
        ProbeCategoryParam param = new ProbeCategoryParam();
        param.setConfigUnit(ConfigUnitEnum.SHELF_SUBTITLE.getCode());
        param.setPoiCategoryIdList(Lists.newArrayList(48)); // 休闲娱乐-按摩/足疗 https://pdc.sankuai.com/category/categorytree/back
        param.setProductCategoryId(101019L); //  休闲娱乐-按摩/足疗-艾灸 https://qing.sankuai.com/url:JTIwJTJGJTJGcWluZy5zYW5rdWFpLmNvbSUyRnNraWZmLXNjcC1tYW5hZ2Utc3lzdGVtJTJGY2F0ZWdvcnktcGFnZSUyMyUyRm1haW4lMkZjYXRlZ29yeS1saXN0/pageId:categoryPage#/main/category-list
        String result = probeParamAgent.shelfSubTitleConfig(param);
        log.info("result:{}", result);
    }
}

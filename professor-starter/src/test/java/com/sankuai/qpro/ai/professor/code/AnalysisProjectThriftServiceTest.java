package com.sankuai.qpro.ai.professor.code;

import com.sankuai.ee.mcode.analyze.api.thrift.response.ProjectCallGraphByRootMethodResponse;
import com.sankuai.ee.mcode.analyze.api.thrift.service.AnalysisProjectThriftService;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

/**
 * AnalysisProjectThriftServiceTest
 *
 * <AUTHOR>
 * @since 2025/5/27
 */
@Slf4j
@SpringBootTest
public class AnalysisProjectThriftServiceTest {

    @Autowired
    private AnalysisProjectThriftService analysisProjectThriftService;

    @SneakyThrows
    @Test
    public void test() {
        ProjectCallGraphByRootMethodResponse resp = analysisProjectThriftService.getJavaProjectCallGraphByRootMethod("ssh://**********************/vc/dzviewscene-dealshelf-home.git",
                null,
                "com.sankuai.dzviewscene.product.unifiedshelf.UnifiedShelfServiceImpl.queryShelfNavAndProduct(com.sankuai.dzviewscene.dealshelf.req.UnifiedShelfRequest)");
        log.info("{}", resp);
    }

}
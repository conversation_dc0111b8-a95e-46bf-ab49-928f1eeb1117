package com.sankuai.qpro.ai.professor.code;

import com.google.common.collect.Lists;
import com.sankuai.ee.mcode.analyze.api.thrift.exception.ThriftApiException;
import com.sankuai.ee.mcode.analyze.api.thrift.response.ProjectCallGraphByRootMethodResponse;
import com.sankuai.qpro.ai.professor.application.code.TopologyService;
import com.sankuai.qpro.ai.professor.application.code.pojo.CallTopology;
import com.sankuai.qpro.ai.professor.application.utils.SerializeUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.thrift.TException;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;
import org.springframework.util.FileCopyUtils;

import java.io.*;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import static org.junit.jupiter.api.Assertions.assertNotNull;

/**
 * TopologyServiceTest
 *
 * <AUTHOR>
 * @since 2025/5/29
 */
@Slf4j
@SpringBootTest
public class TopologyServiceTest {


    @Autowired
    private TopologyService topologyService;

    @Test
    public void test01() {
        String repo = "ssh://**********************/tuangou/mapi-dztgdetail-web.git";
        String methodSignature = "com.dianping.mobile.mapi.dztgdetail.faulttolerance.deal.executor.DzDealBaseExecutor.execute(com.dianping.mobile.mapi.dztgdetail.faulttolerance.req.DealBaseContextRequest)";
        CallTopology callTopology = topologyService.queryCallTopology(repo, methodSignature);
        log.info("callTopology={}", callTopology);
    }


    @Test
    public void testTopology() throws IOException {
        Resource resource = new ClassPathResource("topology.json");
        ProjectCallGraphByRootMethodResponse res = null;
        try (Reader reader = new InputStreamReader(resource.getInputStream(), StandardCharsets.UTF_8)) {
            String content = FileCopyUtils.copyToString(reader);
            res = SerializeUtils.toObj(content, ProjectCallGraphByRootMethodResponse.class);
        }
        if (Objects.nonNull(res)) {
            log.info("res={}", res);
            CallTopology callTopology = topologyService.buildCallTopology(res.getRootMethod(), res);
            log.info("callTopology={}", callTopology);
        }
    }

    @Test
    public void testTopologyNew() throws IOException {
        Resource resource = new ClassPathResource("topology.json");
        ProjectCallGraphByRootMethodResponse res = null;
        try (Reader reader = new InputStreamReader(resource.getInputStream(), StandardCharsets.UTF_8)) {
            String content = FileCopyUtils.copyToString(reader);
            res = SerializeUtils.toObj(content, ProjectCallGraphByRootMethodResponse.class);
        }
        if (Objects.nonNull(res)) {
            log.info("res={}", res);
            Map<String, List<String>> resp = topologyService.buildCallTopologyNew(res.getRootMethod(), res);
            log.info("callTopology={}", resp);
        }
    }
    @Test
    public void testTopologyNewDepth() throws IOException, TException, ThriftApiException {
        Resource resource = new ClassPathResource("topology.json");
        ProjectCallGraphByRootMethodResponse res = null;
        try (Reader reader = new InputStreamReader(resource.getInputStream(), StandardCharsets.UTF_8)) {
            String content = FileCopyUtils.copyToString(reader);
            res = SerializeUtils.toObj(content, ProjectCallGraphByRootMethodResponse.class);
        }
        if (Objects.nonNull(res)) {
            log.info("res={}", res);
            CallTopology resp = topologyService.buildCallTopologyDepth(res.getRootMethod(), res, "ssh://**********************/tuangou/mapi-dztgdetail-web.git",4);
            saveCallTopologyToJson(resp,"callTopology_new_depth.json");
            log.info("callTopology={}", resp);
        }
    }

    @Test
    public void testTopologyNewExcludeClass() throws IOException {
        Resource resource = new ClassPathResource("topology.json");
        ProjectCallGraphByRootMethodResponse res = null;
        try (Reader reader = new InputStreamReader(resource.getInputStream(), StandardCharsets.UTF_8)) {
            String content = FileCopyUtils.copyToString(reader);
            res = SerializeUtils.toObj(content, ProjectCallGraphByRootMethodResponse.class);
        }
        if (Objects.nonNull(res)) {
            log.info("res={}", res);
            List<String> excludeClass = Lists.newArrayList("com.dianping.mobile.mapi.dztgdetail.util.dinner.DinnerDealUtils");
            var resp = topologyService.buildCallTopologyNewWithExcludeClass(res.getRootMethod(), res, excludeClass);
            saveTopologyMapToJson(resp, "callTopology_new_exclude_class.json");
            log.info("callTopology={}", resp);
        }
    }

    /**
     * 验证剪枝效果
     * 通过比较过滤前后的节点数量，验证是否简化了调用链路
     */
    @Test
    public void testPojoFiltering() throws IOException {
        Resource resource = new ClassPathResource("topology.json");
        ProjectCallGraphByRootMethodResponse res = null;
        try (Reader reader = new InputStreamReader(resource.getInputStream(), StandardCharsets.UTF_8)) {
            String content = FileCopyUtils.copyToString(reader);
            res = SerializeUtils.toObj(content, ProjectCallGraphByRootMethodResponse.class);
        }
        if (Objects.nonNull(res)) {
            // 记录过滤前的节点总数
            int totalNodes = countTotalNodes(res);
            log.info("过滤前总节点数: {}", totalNodes);

            // 构建调用拓扑（裁剪后）
            CallTopology callTopology = topologyService.buildCallTopology(res.getRootMethod(), res);

            // 计算过滤后的节点总数
            int filteredNodes = countNodes(callTopology);
            log.info("过滤后总节点数: {}", filteredNodes);

            // 计算过滤比例
            double filterRatio = (totalNodes - filteredNodes) * 100.0 / totalNodes;
            log.info("节点过滤比例: {}%", String.format("%.2f", filterRatio));

            // 将调用拓扑输出为JSON文件
            saveCallTopologyToJson(callTopology, "callTopology_filtered.json");
        }
    }

    /**
     * 计算调用图中的总节点数
     */
    private int countTotalNodes(ProjectCallGraphByRootMethodResponse res) {
        // 根节点 + 所有直接调用
        int count = 1 + res.getInvocationSignatures().size();

        // 加上所有间接调用
        for (String methodSignature : res.getMethodDefMap().keySet()) {
            if (!methodSignature.equals(res.getRootMethod())) {
                count += res.getMethodDefMap().get(methodSignature).getInvocationSignatures().size();
            }
        }
        return count;
    }

    /**
     * 递归计算调用拓扑中的节点数
     */
    private int countNodes(CallTopology topology) {
        if (topology == null) {
            return 0;
        }

        int count = 1; // 当前节点

        if (topology.getChildren() != null) {
            for (CallTopology child : topology.getChildren()) {
                count += countNodes(child);
            }
        }

        return count;
    }

    /**
     * 将调用拓扑输出为JSON文件
     *
     * @param callTopology 调用拓扑
     * @param fileName 文件名
     */
    private void saveCallTopologyToJson(CallTopology callTopology, String fileName) {
        try {
            // 创建输出目录
            File outputDir = new File("target/topology");
            if (!outputDir.exists()) {
                outputDir.mkdirs();
            }

            // 创建输出文件
            File outputFile = new File(outputDir, fileName);

            // 将调用拓扑转换为JSON字符串
            String json = SerializeUtils.toJsonStr(callTopology);

            // 写入文件
            try (FileWriter writer = new FileWriter(outputFile)) {
                writer.write(json);
            }

            log.info("调用拓扑已保存到文件: {}", outputFile.getAbsolutePath());
        } catch (Exception e) {
            log.error("保存调用拓扑到JSON文件失败", e);
        }
    }

    /**
     * 将Map<String, List<String>>类型的调用拓扑数据保存为JSON文件
     *
     * @param topologyMap 调用拓扑Map数据，key为类名，value为方法列表
     * @param fileName 文件名
     */
    private void saveTopologyMapToJson(Map<String, List<String>> topologyMap, String fileName) {
        try {
            // 创建输出目录
            File outputDir = new File("target/topology");
            if (!outputDir.exists()) {
                outputDir.mkdirs();
            }

            // 创建输出文件
            File outputFile = new File(outputDir, fileName);

            // 将Map转换为JSON字符串
            String json = SerializeUtils.toJsonStr(topologyMap);

            // 写入文件
            try (FileWriter writer = new FileWriter(outputFile, StandardCharsets.UTF_8)) {
                writer.write(json);
            }

            log.info("调用拓扑Map已保存到文件: {}", outputFile.getAbsolutePath());
            log.info("保存的类数量: {}, 总方法数量: {}",
                topologyMap.size(),
                topologyMap.values().stream().mapToInt(List::size).sum());
        } catch (Exception e) {
            log.error("保存调用拓扑Map到JSON文件失败", e);
        }
    }
}
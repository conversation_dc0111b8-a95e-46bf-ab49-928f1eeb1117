package com.sankuai.qpro.ai.professor.code.agent;

import com.sankuai.qpro.ai.professor.application.code.agent.consts.Consts;
import com.sankuai.qpro.ai.professor.application.code.agent.core.node.PreRetrieval;
import com.sankuai.qpro.ai.professor.application.code.agent.state.ResearchState;
import com.sankuai.qpro.ai.professor.application.utils.SerializeUtils;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.core.io.ClassPathResource;
import org.springframework.util.StreamUtils;

import javax.annotation.Resource;
import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.util.Map;

/**
 * @author: wuwenqiang
 * @create: 2025-08-07
 * @description:
 */
@Slf4j
@SpringBootTest
public class PreRetrievalTest {

    @Resource
    private PreRetrieval preRetrieval;

    @Test
    public void testPreRetrieval() {
        String requirement = readRequirementFromFile();
        Map<String, Object> data = Map.of(Consts.ConstsState.QUERY, requirement);
        ResearchState state = new ResearchState(data);
        Map<String, Object> result = preRetrieval.apply(state);
        log.info("result={}", SerializeUtils.toJsonStr(result));
    }

    private String readRequirementFromFile() {
        try {
            ClassPathResource resource = new ClassPathResource("requirement_init.md");
            InputStream inputStream = resource.getInputStream();
            return StreamUtils.copyToString(inputStream, StandardCharsets.UTF_8);
        } catch (IOException e) {
            log.error("读取requirement_init.md文件失败", e);
            return "";
        }
    }
}

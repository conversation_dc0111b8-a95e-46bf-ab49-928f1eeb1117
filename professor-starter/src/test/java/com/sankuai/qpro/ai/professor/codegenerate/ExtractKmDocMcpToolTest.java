package com.sankuai.qpro.ai.professor.codegenerate;

import com.sankuai.qpro.ai.professor.api.response.RemoteResponse;
import com.sankuai.qpro.ai.professor.application.constants.RequestContextConstant;
import com.sankuai.qpro.ai.professor.application.model.codegenerate.RequestContext;
import com.sankuai.qpro.ai.professor.application.service.codegenerate.tools.ExtractKmDocMcpTool;

import com.sankuai.qpro.ai.professor.application.utils.SerializeUtils;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;

/**
 * @author: wuwenqiang
 * @create: 2025-07-03
 * @description:
 */
@Slf4j
@SpringBootTest
public class ExtractKmDocMcpToolTest {

    @Resource
    private ExtractKmDocMcpTool extractKmDocMcpTool;

    @Test
    public void testExtractDocMcp() {
        String docUrl = "https://km.sankuai.com/collabpage/2718774394";
        String userInput = "货架";
        RequestContext.init();
        String cookie = "com.sankuai.it.ead.citadel_ssoid=eAGFj61LQ2EchblaBhrEaLrBoAuX9_u9P5O7zik48Qsdrsj7OS66q-LGEFZMCgvC4oKIIoLBJtgNNoPB6v4Dg9XgFsxy0inPeU4umPi8-xgNO_3LhwtKpsxhPTpR2X5TpVHaiJyykUkbyrqDudA5AM-NB4sokxzHRvm81VzyQQXjk7dgcrbi9JZxmWuXaJIkJVmMY-C8lKBkoVgkCBYpyAIjRRSevd5_dejMCPkXHA8V58eWb59eHs_pev_n6v2bdIPc31YviBjGBHsrnEaaDYK8AOQNE-AVICz2sGSACDCBOIObYLrldBs7YEQYiqn0TBMSY8cpeKKNlRIor4ZKSy8Nx4rnY6a8UFZLJCjj0lihpekGYVku1QsJrW1uxyuVXb5zVK2erpbtWgGXSWYrG71gvNVsuew4VVkNieug8zx88QucwHbz**eAEFwQkBgAAIA8BKDCZah_H0j-DdjbCUM5yEUjHSqct2vJAjmmVtGdRPfHxjzwm7skT0D2DPEjI**SDCrWzwOr-zAQM-BdybNybHW-1cRcrv8CqVVnxXeBLFPg3mqkwdYe7oudn7ikZjMVDK2P2mGJjAKODTmDB1LRw**NzcwNTMxMSx3dXdlbnFpYW5nMDYs5LyN5rip5by6LHd1d2VucWlhbmcwNkBtZWl0dWFuLmNvbSwxLDM0MDAxNzA0LDE3NTQ1ODI0Nzg4NTk";
        RequestContext.setAttribute(RequestContextConstant.COOKIE_HEADER, cookie);
        RemoteResponse<String> result = extractKmDocMcpTool.extractDocTool(docUrl, userInput);
        log.info("result:{}", SerializeUtils.toJsonStr(result));
    }
}

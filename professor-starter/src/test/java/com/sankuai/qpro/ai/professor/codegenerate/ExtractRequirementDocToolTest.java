package com.sankuai.qpro.ai.professor.codegenerate;

import com.sankuai.qpro.ai.professor.application.constants.KmDocConstant;
import com.sankuai.qpro.ai.professor.application.constants.RequestContextConstant;
import com.sankuai.qpro.ai.professor.application.model.codegenerate.RequestContext;
import com.sankuai.qpro.ai.professor.application.model.codegenerate.RequirementReviewInfo;
import com.sankuai.qpro.ai.professor.application.service.codegenerate.proxy.CookieManager;
import com.sankuai.qpro.ai.professor.application.service.codegenerate.tools.ExtractRequirementDocTool;
import com.sankuai.qpro.ai.professor.application.utils.SerializeUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

/**
 * @author: wuwen<PERSON><PERSON>
 * @create: 2025-05-27
 * @description:
 */
@Slf4j
@SpringBootTest
public class ExtractRequirementDocToolTest {

    @Autowired
    private ExtractRequirementDocTool extractRequirementDocTool;

    @Autowired
    private CookieManager cookieManager;

    @Test
    public void testExtractRequirementDoc() {
        RequestContext.init();
        RequestContext.setAttribute(RequestContextConstant.TASK_ID, "123");
        String cookieValue = "eAGFjj9LAzEcQI1dCi7F0SmjdDiSX5JL4mTjeTgKioJb_vxOjurdcOcX6GbRRRdnoeAurgW_hKufQMGhu_YTOL3l8XhDMvpcfAzo8n15_wOwE9vrrPPN9MbXWd1n6FMW694nvNqjTFjPVAqqCknmlnvhw1gqnXwUUerKPZJtWgrnXKkLY6xSpWPuoCiA2UNh9URCwejL0-1iBbub8G_PrM_2t45Wb3df33A8f11jRjYeyPAcw0nEBp8J5Wgl5FFwoSsZAAxHJWwFISatrVAXlFUCVco9t-M_1UjDQHIrvNY6KEA7I6NL7CcxYtedtlNszmBOBl3X_gIrNllM**eAEFwQkRADAIAzBLsA445Kw8_iUsucbnENfXjqKCJbeWkPHxzDpDjAmRut3zciN6GHtMzOoDTcUSaA**qDkyqOZtPh4sDYFU3dH0xJpmr7JXPTPbbg1bUKaqGgEclREJsvF1Aue1duyNHgJh8UuT6IfkJZjZmOTzaVotFg**NzcwNTMxMSx3dXdlbnFpYW5nMDYs5LyN5rip5by6LHd1d2VucWlhbmcwNkBtZWl0dWFuLmNvbSwxLDM0MDAxNzA0LDE3NDgzNDg0MTIwOTQ";
        cookieManager.setCookie("123", KmDocConstant.KM_SSO_COOKIE_NAME, cookieValue);
        String scene = "货架";
        String url = "https://km.sankuai.com/collabpage/2539447766#b-ed93fa57000d4e56a7412801c3aa1fdf";
        String result = extractRequirementDocTool.extractPRDSpecialSceneDoc(url, scene, null);
        log.info("result:{}", result);
    }

    @Test
    public void testExtractReviewDoc() {
        RequestContext.init();
        RequestContext.setAttribute(RequestContextConstant.TASK_ID, "123");
        String cookieValue = "eAGFjr1KBDEYAI3XHNgcllYp5Yol-ZJsEisvrouloCjY5edbWU53i11f4MrDSt9AEOxFS0sray19AgWRK-z0nsBqmmGYIRm9374O6NPP2-MXwEZsz7PON9MLX2d1n6FPWax7n_BsizJhPVMpqCokmVvuhQ9jqXTyUUSpK3dN1mkpnHOlLoyxSpWOuZ2iAGZ3hdUTCQWjd9_PLwvYXIV_e2Z5tr22t3i4_PiE_fn9EjOyckWGxxgOIjZ4QyhHKyGPggtdyQBgOCphKwgxaW2FOqGsEqhS7rkd_6lGGgaSW-G11kEB2hkZnWI_iRG77rCdYnMEczLouvYXLK1ahQ**eAENysEBwCAIA8CVIAIx40Cr-4_Q3vuU8GTLNNWH2EE_q57FmkO-5ml_QSyMxTYW5EpAmfK-H_lhD0w**UnFaqZFicNc_rYzgPsaXEi8Ya-U1fKUBRVhebY2zppqIy5Auno8KpA6cd_pLiqDRrXi_cf-pg7vmLx2OarPS7A**NzcwNTMxMSx3dXdlbnFpYW5nMDYs5LyN5rip5by6LHd1d2VucWlhbmcwNkBtZWl0dWFuLmNvbSwxLDM0MDAxNzA0LDE3NDg0MzQ4MTIwOTQ";
        cookieManager.setCookie("123", KmDocConstant.KM_SSO_COOKIE_NAME, cookieValue);
        String url = "https://km.sankuai.com/collabpage/2711896309";
//        String userInput = "extendDisplayInfo的key只支持couponWalletInfo，应该从couponWalletInfo对应的value中查找最高膨胀金额文案";
        String userInput = null;
        Pair<String, RequirementReviewInfo> result = extractRequirementDocTool.extractReviewDocAndInfo(url, userInput);
        if (result == null) {
            log.info("result is null");
            return;
        }
        log.info("review string:{}\n", result.getLeft());
        log.info("review info:{}", SerializeUtils.toJsonStr(result.getRight()));
    }

    @Test
    public void testGenerateRequirementPrompt() {
        RequestContext.init();
        RequestContext.setAttribute(RequestContextConstant.TASK_ID, "123");
        String cookieValue = "eAGFj7FKgzEURgkuVRzEJ_gHB-kgyU3-e1PH4lCKg1BK0UVyk5sqYlFQ6tCn6KQdRAQ3X8BRJ91F7ORaXByq4KgdnN2_cw5fRS293bzMFZP3--dPgAVnWCjHUof1gjUha4gc2TvtvUfnqiZn4CBsyNRv1fJ8R7gVpSeDYvo0vviGVQX_gn6WbHzcfb1OYetyND5_hKGq_JlGak2jNQliiN6TqSWdyzJliw7ZUkDhXUOWLKJGTY6u1UpfeAAgSDGEEkN2WaNnoOSgVrM-JRHZKbI1uQwBSFfJWTS_C5RYuoyiTSI3VEXm5kaKjeROugfbxrdNbh9BvXO2GQ5Ta881R2qxf9qX3vF-6HU1XqnJw-zFDyAca5s**eAEFwQkBwDAIA0BLgYZPTunAv4TdycFoYyzcfLBkQ1CopI6Rvj5lca5BPC2XGd_NB22-JX4E_xA3**BM-AuqBkoRp12lqIsafCi69QthDfQRmu_wwsbM8l-AQ8G_u1U0xmRwqTRg3kSEz5IFy1RjSwv0bMO8MRH2wa0g**NzcwNTMxMSx3dXdlbnFpYW5nMDYs5LyN5rip5by6LHd1d2VucWlhbmcwNkB0ZXN0LmNvbSwxLDM0MDAxNzA0LDE3NDkzMDI2NzA2OTY";
        cookieManager.setCookie("123", KmDocConstant.KM_SSO_COOKIE_NAME, cookieValue);
        String scene = "货架";
        String prdUrl = "https://km.sankuai.com/collabpage/2539447766#b-ed93fa57000d4e56a7412801c3aa1fdf";
        String reviewUrl = "https://km.sankuai.com/collabpage/2711896309";
        String userInput = "膨胀神券商家场景的可再膨胀，也是区分单列和双列货架的，单列货架需要实验分组，双列货架方案和膨胀已完成一致。";
        String result = extractRequirementDocTool.generateRequirementPrompt(prdUrl, reviewUrl, scene, null);
        log.info("result:{}", result);
    }

    @Test
    public void testAll() {
        RequestContext.init();
        RequestContext.setAttribute(RequestContextConstant.TASK_ID, "123");
        String cookieValue = "eAGFjzFLAzEYhg1dCi7F0ekGB-1wJF--XBIne56Ho6AouEiS-yKl2g5XcemgdRNcnMRRcBcU3P0HOrj6AwRBoeBoHZx9l3d73udtstbb7WsjuXg_uf8CmA-Dw7R2_d6R66bdYUquSkN36Co6WE6IexOj08ijxyqgkcK3pQKDlVKZw_yZzS3tkN8M1KdRKfM8L3VhjFWqzHm-WhTA7Zq0uoNQ8OTs5ep7AosM_gWbX8WV2fXTp4fHT9j4mNxNa8xmLlnzb-6apRhFgCzz3CniMRNWA3GujTdcV4JgT2g0KDNAiwJv2MIx-ZEgi5AFKaSO6AGMICVtBB8qra1Uu0mkmBmO0Zq2QDF9QKinEUIHZ4j4mLX2adgJgep6a9Cj_jacs0ZdD34AiBBrNA**eAEFwYkBwCAIA8CVjJGn42CQ_UfoXUVWGGNGF_0c1omZ3iJT9KY5GcUHLcfZpuOfVJHguvwBRLERTg**wpzRc4A90z2e9h4XbtGR3MNfxcipQ8NnSLQQkCmdwJyk-nIar3A_zN68Iaf7uNIZ472AC5bPZs0oEf_wlp3xmQ**NzcwNTMxMSx3dXdlbnFpYW5nMDYs5LyN5rip5by6LHd1d2VucWlhbmcwNkBtZWl0dWFuLmNvbSwxLDM0MDAxNzA0LDE3NDg2OTU1Mjg4NDU";
        cookieManager.setCookie("123", KmDocConstant.KM_SSO_COOKIE_NAME, cookieValue);
        String scene = "货架";
        String prdUrl = "https://km.sankuai.com/collabpage/2539447766#b-ed93fa57000d4e56a7412801c3aa1fdf";
        String reviewUrl = "https://km.sankuai.com/collabpage/2711896309";
        String result = extractRequirementDocTool.testAll(prdUrl, reviewUrl, scene, null);
        log.info("result:{}", result);
    }
}

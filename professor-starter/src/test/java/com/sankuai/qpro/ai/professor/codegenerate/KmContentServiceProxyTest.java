package com.sankuai.qpro.ai.professor.codegenerate;

import com.sankuai.qpro.ai.professor.api.response.RemoteResponse;
import com.sankuai.qpro.ai.professor.application.model.codegenerate.ImageContent;
import com.sankuai.qpro.ai.professor.application.service.codegenerate.proxy.KmImageServiceProxy;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

/**
 * @author: wuwenqiang
 * @create: 2025-07-01
 * @description:
 */
@Slf4j
@SpringBootTest
public class KmContentServiceProxyTest {

    @Autowired
    private KmImageServiceProxy kmImageServiceProxy;

    @Test
    public void testImageContent() {
        String url = "https://km.sankuai.com/api/file/cdn/2707346824/157189098810?contentType=1&isNewContent=false";
        String cookieValue = "com.sankuai.it.ead.citadel_ssoid=eAGFjz1LA0EYhFltAlqIpdUVFpri2O-9tdIziYegBBTUNHK7t284Es-EGKKQwtI6ZQpRIT9BrKzTWViInf4CEbURU5gU1jLVFPPMTA7NvQyepr3h9eB9ROmCPT7yW3FWa8epn574Lk58m57EiauveM5pDcKCTjDjSpDAxpBPjFBibLWF8AHNL-85s2Nd5rolFoZhSRWCQAtRCnG4XihQrItMqzVOC9i7u7i_PWdLU_RfcDCZuDoTvX08f_3Q8uvo8vGT9lDur6uPfE4IJZBIZ7DhY2GQGoPlUkOsMZGHRHGNqeYSC65v0GLHmS5xmlNpGWEKuKE0IE4wDdTYRCnNRMWzFACU5IzkDZcCm3EcAAfUWmGAkR7y2OY2Pys3Noqn-3UWRa3G7paJRKVpodE6YNVaH8122h2XNdM4q2J5hYbfkxe_nmZ6rw**eAEFwYEBwEAEA8CVlBfWIdh_hN7JTYuG0o9AXZ49_3SU6F3BPCvLQEwWQ4S9LpbpT8Gb_QFHhxHh**jv2nOoWfHISflmJ8S3P0b47L1SHRpx4LDeEhVeXUKCiqPWEGyCeA5T6N36nRJ1_VBxUVd-_U0ARfwNfR0C3QmA**NzcwNTMxMSx3dXdlbnFpYW5nMDYs5LyN5rip5by6LHd1d2VucWlhbmcwNkBtZWl0dWFuLmNvbSwxLDM0MDAxNzA0LDE3NTE1MDg0MDY0NzE";
        RemoteResponse<ImageContent> contentResp = kmImageServiceProxy.readImageContentByCookie(url, cookieValue);
        log.info("contentResp={}", contentResp);
    }
}

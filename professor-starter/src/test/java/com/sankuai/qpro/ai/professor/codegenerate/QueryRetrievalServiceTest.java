package com.sankuai.qpro.ai.professor.codegenerate;

import com.sankuai.qpro.ai.professor.api.response.RemoteResponse;
import com.sankuai.qpro.ai.professor.application.constants.RequestContextConstant;
import com.sankuai.qpro.ai.professor.application.model.codegenerate.PreQueryResult;
import com.sankuai.qpro.ai.professor.application.model.codegenerate.RequestContext;
import com.sankuai.qpro.ai.professor.application.service.codegenerate.retrieval.QueryRetrievalService;
import com.sankuai.qpro.ai.professor.application.service.codegenerate.tools.ExtractKmDocMcpTool;
import com.sankuai.qpro.ai.professor.application.utils.SerializeUtils;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.core.io.ClassPathResource;
import org.springframework.util.StreamUtils;

import javax.annotation.Resource;
import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;

/**
 * @author: wuwenqiang
 * @create: 2025-08-05
 * @description:
 */
@Slf4j
@SpringBootTest
public class QueryRetrievalServiceTest {
    @Resource
    private QueryRetrievalService queryRetrievalService;

    @Resource
    private ExtractKmDocMcpTool extractKmDocMcpTool;

    @Test
    public void testPreRetrieval() {
        String requirement = readRequirementFromFile();
        PreQueryResult retrievalResult = queryRetrievalService.queryPreRetrieval(requirement);
        log.info("retrievalResult:{}", SerializeUtils.toJsonStr(retrievalResult));
    }

    private String readRequirementFromFile() {
        try {
            ClassPathResource resource = new ClassPathResource("requirement_init.md");
            InputStream inputStream = resource.getInputStream();
            return StreamUtils.copyToString(inputStream, StandardCharsets.UTF_8);
        } catch (IOException e) {
            log.error("读取requirement_init.md文件失败", e);
            return "";
        }
    }
}

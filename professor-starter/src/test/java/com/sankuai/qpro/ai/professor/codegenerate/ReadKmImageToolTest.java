package com.sankuai.qpro.ai.professor.codegenerate;

import com.sankuai.qpro.ai.professor.api.response.RemoteResponse;
import com.sankuai.qpro.ai.professor.application.model.codegenerate.ImageContent;
import com.sankuai.qpro.ai.professor.application.service.codegenerate.tools.ReadKmImageTool;
import com.sankuai.qpro.ai.professor.application.utils.SerializeUtils;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;

/**
 * @author: wuwen<PERSON><PERSON>
 * @create: 2025-07-01
 * @description:
 */
@Slf4j
@SpringBootTest
public class ReadKmImageToolTest {

    @Resource
    private ReadKmImageTool readKmImageTool;

    @Test
    public void testReadKmImage() {
        String url = "https://km.sankuai.com/api/file/cdn/2707346824/157189098810?contentType=1&isNewContent=false";
        String cookieValue = "com.sankuai.it.ead.citadel_ssoid=eAGFjz1LA0EYhFltAlqIpdUVFpri2O-9tdIziYegBBTUNHK7t284Es-EGKKQwtI6ZQpRIT9BrKzTWViInf4CEbURU5gU1jLVFPPMTA7NvQyepr3h9eB9ROmCPT7yW3FWa8epn574Lk58m57EiauveM5pDcKCTjDjSpDAxpBPjFBibLWF8AHNL-85s2Nd5rolFoZhSRWCQAtRCnG4XihQrItMqzVOC9i7u7i_PWdLU_RfcDCZuDoTvX08f_3Q8uvo8vGT9lDur6uPfE4IJZBIZ7DhY2GQGoPlUkOsMZGHRHGNqeYSC65v0GLHmS5xmlNpGWEKuKE0IE4wDdTYRCnNRMWzFACU5IzkDZcCm3EcAAfUWmGAkR7y2OY2Pys3Noqn-3UWRa3G7paJRKVpodE6YNVaH8122h2XNdM4q2J5hYbfkxe_nmZ6rw**eAEFwYEBwEAEA8CVlBfWIdh_hN7JTYuG0o9AXZ49_3SU6F3BPCvLQEwWQ4S9LpbpT8Gb_QFHhxHh**jv2nOoWfHISflmJ8S3P0b47L1SHRpx4LDeEhVeXUKCiqPWEGyCeA5T6N36nRJ1_VBxUVd-_U0ARfwNfR0C3QmA**NzcwNTMxMSx3dXdlbnFpYW5nMDYs5LyN5rip5by6LHd1d2VucWlhbmcwNkBtZWl0dWFuLmNvbSwxLDM0MDAxNzA0LDE3NTE1MDg0MDY0NzE";
        RemoteResponse<String> data = readKmImageTool.parseImage(url, cookieValue);
        log.info("data = {}", SerializeUtils.toJsonStr(data));
    }

    @Test
    public void testReadImageCompress() {
        String url = "https://km.sankuai.com/api/file/cdn/2707346824/157189098810?contentType=1&isNewContent=false";
        String cookieValue = "com.sankuai.it.ead.citadel_ssoid=eAGFjz1LA0EURRlsBC3E0moLC02xzLz52mdl1riIGhUVhDQyMzsjS8yKmJAmjaVtSgsRwVLBH-BfsLBIq4UgYqPYRtAU1nKr25x77jiZeroejEUvg8_3b4AZd9SKT0zZ7JgiLtqxN3nsirbJ_eFC5D1ikC5gTrnQkiXOhEpupZa_FV1IH8j0_J63O86XvpfxNE0zXUsSlDJLabpUqwHFZY66KqBGo7f7s7tTPkfgX3AyUlycWHm9uf4Ywtbz8OLxC_pk_G_rnMSCMWAhV95SK35Dg0IanFAYDFKm9pkWSAGFolLgFZntettjHgUoxxnXQViAhHnJMYB1udbIZSPieaKsBRGgIoXiBsFY51zIudJeedEn0UnaaatGqG_6ZuCrdbddBWiUW2sb661dfZDpczLZ7XR9eVyY8oCqS_JyO3rxA1H-emI**eAENycEBgDAIA8CVaKQC4xDC_ivofa_piEFmp92-VQ-lWk6D2iv5zv_HYCafDowvWTkbOK9_WjYSZA**ZvZPg5EGOKTjNx_wEkM7v4RMi9CoKTjKx7m9fIpdkdRpaiq4I1BB96kw5IqmMXaD5p47TVlsATH7q3t-DbkyAQ**NzcwNTMxMSx3dXdlbnFpYW5nMDYs5LyN5rip5by6LHd1d2VucWlhbmcwNkBtZWl0dWFuLmNvbSwxLDM0MDAxNzA0LDE3NTE3Njc2MDgxNjQ";
        ImageContent data = readKmImageTool.readImageFromUrlCompress(url, cookieValue, 3);
        log.info("data = {}", SerializeUtils.toJsonStr(data));
    }
}

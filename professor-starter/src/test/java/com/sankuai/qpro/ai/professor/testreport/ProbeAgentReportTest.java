package com.sankuai.qpro.ai.professor.testreport;

import com.fasterxml.jackson.databind.JsonNode;
import com.google.common.collect.Lists;
import com.meituan.mdp.langmodel.api.message.AssistantMessage;
import com.meituan.mdp.langmodel.api.message.Message;
import com.meituan.mdp.langmodel.api.message.UserMessage;
import com.sankuai.qpro.ai.professor.api.enums.ConfigUnitEnum;
import com.sankuai.qpro.ai.professor.application.model.operator.probe.agent.plan.AgentPlanModel;
import com.sankuai.qpro.ai.professor.application.service.operator.probe.ProbeAgent;
import com.sankuai.qpro.ai.professor.application.utils.SerializeUtils;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.io.FileWriter;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.atomic.AtomicIntegerArray;

/**
 * @author: wuwenqiang
 * @create: 2025-02-16
 * @description:
 */
@Slf4j
@SpringBootTest
public class ProbeAgentReportTest {
    @Autowired
    private ProbeAgent probeAgent;

    private static final String CATEGORY_AGENT = "generateCategoryTips";
    private static final String EXP_AGENT = "generateExperimentTips";
    private static final String ATTR_CONFIG_AGENT = "generateConfigTips";
    private static final String GENERATE_AGENT = "generateSummaryOrCode";

    private static final String CATEGORY_REASON_PATH = "file/probe_route_test/category_reasoning.json";;
    private static final String CATEGORY_CONFIG_PATH = "file/probe_route_test/category_config.json";
    private static final String EXP_PATH = "file/probe_route_test/exp.json";
    private static final String ATTR_CONFIG_PATH = "file/probe_route_test/attr_config.json";
    private static final String GENERATE_PATH = "file/probe_route_test/generate.json";

    @Test
    public void testDifferent() {
        String[][] modelNames= {{"gpt", "claude"}, {"gpt", "deepSeekR1"}, {"claude", "deepSeekR1"}, {"deepSeekR1", "claude"}};
//        String[][] modelNames= {{"claude", "deepSeekR1"}, {"deepSeekR1", "claude"}};
//        String[][] modelNames= {{"gpt", "claude"}, {"gpt", "deepSeekR1"}};
        int executeCount = 10;
        for (String[] modelName : modelNames) {
            testModel(modelName[0], modelName[1], "file/route_statistic/result.txt", executeCount);
        }
    }

    private void testModel(String reasonModelName, String validateModelName, String filePath, int executeCount) {
        String[] paths = { CATEGORY_REASON_PATH, CATEGORY_CONFIG_PATH, EXP_PATH, ATTR_CONFIG_PATH, GENERATE_PATH };
        String[] agents = { CATEGORY_AGENT, CATEGORY_AGENT, EXP_AGENT, ATTR_CONFIG_AGENT, GENERATE_AGENT };
        String[] stageName = {"category_reason", "category_config", "exp", "attr_config", "generate"};
        AtomicIntegerArray successCounts = new AtomicIntegerArray(paths.length);
        List<CompletableFuture<Void>> allFuture = Lists.newArrayList();
        for (int i = 0; i < executeCount; i++) {
            for (int j = 0; j < paths.length; j++) {
                int index = j;
                CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                    if (routeReasoning(paths[index], agents[index], reasonModelName, validateModelName)) {
                        successCounts.getAndIncrement(index);
                    }
                });
                allFuture.add(future);
            }
        }
        CompletableFuture.allOf(allFuture.toArray(new CompletableFuture[0])).join();
        StringBuilder resSb = new StringBuilder();
        resSb.append(String.format("推理模型:%s 复检模型:%s 执行次数:%s\n", reasonModelName, validateModelName, executeCount));
        for (int i = 0; i < paths.length; i++) {
            double successRate = (double) successCounts.get(i) / executeCount * 100;
            resSb.append(String.format("StageName=%s ,route success rate=%s\n", stageName[i], successRate + "%"));
        }
        resSb.append("================\n");
        log.info("result={}", resSb);
        writeStr2File(resSb.toString(), filePath);
    }

    private synchronized void writeStr2File(String content, String filePath) {
        try {
            ClassLoader classLoader = getClass().getClassLoader();
            String pathStr = classLoader.getResource(filePath).getPath();
            // 确保目录存在
            Path path = Paths.get(pathStr);
            Files.createDirectories(path.getParent());
            log.info("target path={}", path);
            try (FileWriter writer = new FileWriter(pathStr, true)) {
                writer.write(content);
            }
        } catch (Exception e) {
            log.error("writeStr2File error", e);
        }
    }

    @Test
    public void testCategoryReason() {
        int success = 0;
        int executeCount = 10;
        String jsonPath = "file/probe_route_test/category_reasoning.json";
        for (int i = 0; i < executeCount; i++) {
            if (routeReasoning(jsonPath, CATEGORY_AGENT)) {
                success++;
            }
        }
        double successRate = (double) success / executeCount * 100;
        log.info("category reason route success rate={}", successRate + "%");
    }

    @Test
    public void testCategoryConfig() {
        int success = 0;
        int executeCount = 10;
        String jsonPath = "file/probe_route_test/category_config.json";
        for (int i = 0; i < executeCount; i++) {
            if (routeReasoning(jsonPath, CATEGORY_AGENT)) {
                success++;
            }
        }
        double successRate = (double) success / executeCount * 100;
        log.info("category config route success rate={}", successRate + "%");
    }

    @Test
    public void testExp() {
        int success = 0;
        int executeCount = 10;
        String jsonPath = "file/probe_route_test/exp.json";
        for (int i = 0; i < executeCount; i++) {
            if (routeReasoning(jsonPath, EXP_AGENT)) {
                success++;
            }
        }
        double successRate = (double) success / executeCount * 100;
        log.info("exp route success rate={}", successRate + "%");
    }

    @Test
    public void testAttrConfig() {
        int success = 0;
        int executeCount = 10;
        String jsonPath = "file/probe_route_test/attr_config.json";
        for (int i = 0; i < executeCount; i++) {
            if (routeReasoning(jsonPath, ATTR_CONFIG_AGENT)) {
                success++;
            }
        }
        double successRate = (double) success / executeCount * 100;
        log.info("attr config route success rate={}", successRate + "%");
    }

    @Test
    public void testGenerate() {
        int success = 0;
        int executeCount = 10;
        String jsonPath = "file/probe_route_test/generate.json";
        for (int i = 0; i < executeCount; i++) {
            if (routeReasoning(jsonPath, GENERATE_AGENT)) {
                success++;
            }
        }
        double successRate = (double) success / executeCount * 100;
        log.info("generate route success rate={}", successRate + "%");
    }

    private boolean routeReasoning(String jsonPath, String subAgent) {
        try {
            Long conversionId = 1L;
            String configUnit = ConfigUnitEnum.SHELF_SUBTITLE.getCode();
            List<List<Message>> msgLists = buildMsgList(jsonPath);
            for (List<Message> msgList : msgLists) {
                AssistantMessage msg = probeAgent.chooseTaskAgentReasoning(msgList, conversionId, configUnit);
                AgentPlanModel result = SerializeUtils.toObj(msg.getContent(), AgentPlanModel.class);
                log.info("result:{}", result);
                if (result == null || !subAgent.equals(result.getAgent())) {
                    return false;
                }
            }
            return true;
        } catch (Exception e) {
            log.error("routeReasoning error", e);
            return false;
        }
    }

    private boolean routeReasoning(String jsonPath, String subAgent, String reasonModel, String validateModel) {
        try {
            Long conversionId = 1L;
            String configUnit = ConfigUnitEnum.SHELF_SUBTITLE.getCode();
            List<List<Message>> msgLists = buildMsgList(jsonPath);
            for (List<Message> msgList : msgLists) {
                // 取最后一条数据
                AssistantMessage msg = probeAgent.chooseTaskAgentReasoning(msgList.subList(msgList.size() - 1, msgList.size()), conversionId, configUnit, reasonModel, validateModel);
                AgentPlanModel result = SerializeUtils.toObj(msg.getContent(), AgentPlanModel.class);
                log.info("result:{}", result);
                if (result == null || !subAgent.equals(result.getAgent())) {
                    return false;
                }
            }
            return true;
        } catch (Exception e) {
            log.error("routeReasoning error", e);
            return false;
        }
    }

    private List<List<Message>> buildMsgList(String jsonPath) throws Exception {
        ClassLoader classLoader = getClass().getClassLoader();
        String jsonStr = new String(Files.readAllBytes(Paths.get(classLoader.getResource(jsonPath).toURI())), StandardCharsets.UTF_8);
        JsonNode jsonNode = SerializeUtils.readTree(jsonStr);
        List<List<Message>> result = Lists.newArrayList();
        for (JsonNode msgListNode : jsonNode) {
            List<Message> msgList = Lists.newArrayList();
            for (JsonNode msgNode : msgListNode) {
                String role = msgNode.get("role").asText();
                String content = msgNode.get("content").asText();
                if (role.equals("user")) {
                    msgList.add(UserMessage.from(content));
                } else if (role.equals("assistant")) {
                    msgList.add(AssistantMessage.from(content));
                }
            }
            result.add(msgList);
        }
        return result;
    }
}

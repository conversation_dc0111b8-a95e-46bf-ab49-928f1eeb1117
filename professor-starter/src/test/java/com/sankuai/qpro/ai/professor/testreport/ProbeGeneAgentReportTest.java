package com.sankuai.qpro.ai.professor.testreport;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.google.common.collect.Lists;
import com.meituan.mdp.langmodel.api.message.AssistantMessage;
import com.meituan.mdp.langmodel.api.message.Message;
import com.meituan.mdp.langmodel.api.message.SystemMessage;
import com.meituan.mdp.langmodel.api.message.UserMessage;
import com.meituan.mdp.langmodel.component.model.chat.OpenAIChatModel;
import com.sankuai.qpro.ai.professor.api.enums.ConfigUnitEnum;
import com.sankuai.qpro.ai.professor.application.model.operator.probe.agent.gene.GeneStatisticModel;
import com.sankuai.qpro.ai.professor.application.model.operator.probe.agent.gene.param.ProbeGeneParam;
import com.sankuai.qpro.ai.professor.application.prompt.ProbeAgentPrompt;
import com.sankuai.qpro.ai.professor.application.service.operator.probe.agent.ProbeGeneAgent;
import com.sankuai.qpro.ai.professor.application.utils.GroovyExecuteUtils;
import com.sankuai.dzviewscene.unifiedshelf.operator.api.utils.OperatorShelfConfigValidateUtil.AttrM;
import com.sankuai.qpro.ai.professor.application.utils.SerializeUtils;
import com.sankuai.qpro.ai.professor.testreport.model.GenerateCodeTestCaseModel;
import com.sankuai.qpro.ai.professor.testreport.model.MockTag;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.test.context.SpringBootTest;

import java.io.BufferedReader;
import java.io.FileReader;
import java.io.FileWriter;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;


/**
 * @author: wuwenqiang
 * @create: 2025-02-15
 * @description:
 */
@Slf4j
@SpringBootTest
public class ProbeGeneAgentReportTest {
    @Autowired
    private ProbeGeneAgent probeGeneAgent;
    @Qualifier("gpt4oModel")
    @Autowired
    private OpenAIChatModel gpt4oModel;
    @Autowired
    private ProbeAgentPrompt agentPrompt;

    /**
     * 生成测试用例
     * @throws Exception
     */
    @Test
    public void testTestCase() throws Exception {
        String msg = generateTestCase();
        log.info("msg={}", msg);
    }

    @Test
    public void testGenerateCodeSummary() {
        String summaryPath = "file/generate_test_case/single_platform_multi_exp_update/summary.txt";
        String testCasePath = "file/generate_test_case/single_platform_multi_exp_update/test_case.json";
        List<CompletableFuture> futures = Lists.newArrayList();
        for (int k = 0; k < 5; k++) {
            CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                int executeCount = 2;
                GeneStatisticModel allResult = new GeneStatisticModel(4);
                for (int i = 0; i < executeCount; i++) {
                    try {
                        testGenerateCode(allResult, summaryPath, testCasePath);
                    } catch (Exception e) {
                        log.error("error occur", e);
                    }
                }
                String result = String.format("executeCount=%s result=%s \n", executeCount, SerializeUtils.toJsonStr(allResult));
                log.info("summary statistic result={}", result);
                writeFile("file/generate_test_case/single_platform_multi_exp_update/test_statistic_result.txt", result);
            });
            futures.add(future);
        }
        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
    }

    public void testGenerateCode(GeneStatisticModel allResult, String summaryPath, String testCasePath) throws Exception {
        ProbeGeneParam param = buildMockParam();
        List<Message> historyMsg = Lists.newArrayList(UserMessage.from("确认生成代码"));
        ClassLoader classLoader = getClass().getClassLoader();
        String summary = new String(Files.readAllBytes(Paths.get(classLoader.getResource(summaryPath).toURI())), StandardCharsets.UTF_8);
        // 第0位是第一次生成，第1、2、3位均是重新生成的结果
        ThreadLocal<GeneStatisticModel> statisticModelThreadLocal = ProbeGeneAgent.CODE_STATISTIC_MODEL;
        statisticModelThreadLocal.set(new GeneStatisticModel(4));
        try {
            probeGeneAgent.generateCode(param, historyMsg, summary);
            int curGenerateTimes = 0;
            // 找到最后一次成功的生成次数
            for (int i = 0; i < 4; i++) {
                if (statisticModelThreadLocal.get().getExecuteSuccessCount()[i] > 0) {
                    curGenerateTimes = i;
                }
            }
            String groovy = statisticModelThreadLocal.get().getDsl();
            List<GenerateCodeTestCaseModel> testCases = parseTestCase(testCasePath);
            statisticModelThreadLocal.get().getResultSuccessCount()[curGenerateTimes]++;
            for (GenerateCodeTestCaseModel testCase : testCases) {
                Object groovyResult = GroovyExecuteUtils.getGroovyResult(groovy, testCase.getInputAttrs(), testCase.getExpResultIds().get(0));
                JsonNode jsonNode = SerializeUtils.readTree(SerializeUtils.toJsonStr(groovyResult));
                List<MockTag> tags = SerializeUtils.toObj(jsonNode.get("tags").toString(), new TypeReference<List<MockTag>>(){});
                List<String> outputTags = tags == null ? Lists.newArrayList() : tags.stream().map(MockTag::getText).collect(Collectors.toList());
                // 有一个测试用例不通过，则认为当前生成结果异常
                if (!CollectionUtils.isEqualCollection(outputTags, testCase.getOutputTags())) {
                    statisticModelThreadLocal.get().getResultSuccessCount()[curGenerateTimes]--;
                    break;
                }
            }
        } catch (Exception e) {
            log.error("error occur", e);
        } finally {
            // 防止执行异常导致数据丢失，汇总数据
            for (int i = 0; i < 4; i++) {
                allResult.getTimesCount()[i] += statisticModelThreadLocal.get().getTimesCount()[i];
                allResult.getExecuteSuccessCount()[i] += statisticModelThreadLocal.get().getExecuteSuccessCount()[i];
                allResult.getResultSuccessCount()[i] += statisticModelThreadLocal.get().getResultSuccessCount()[i];
            }
            allResult.setError(allResult.getError() + statisticModelThreadLocal.get().getError());
            statisticModelThreadLocal.remove();
        }
    }

    private synchronized void writeFile(String filePath, String content) {
        // 追加模式
        try {
            ClassLoader classLoader = getClass().getClassLoader();
            String pathStr = classLoader.getResource(filePath).getPath();
            // 确保目录存在
            Path path = Paths.get(pathStr);
            Files.createDirectories(path.getParent());
            log.info("target path={}", path);
            try (FileWriter writer = new FileWriter(pathStr, true)) {
                writer.write(content);
            }
        } catch (Exception e) {
            log.error("writeStr2File error", e);
        }
    }

    private ProbeGeneParam buildMockParam() {
        ProbeGeneParam param = new ProbeGeneParam();
        param.setConversationId(1L);
        param.setConfigUnit(ConfigUnitEnum.SHELF_SUBTITLE.getCode());
        param.setPoiCategoryList(Lists.newArrayList(48));
        param.setProductCategoryId(84101019L);
        return param;
    }

    private List<GenerateCodeTestCaseModel> parseTestCase(String jsonFilePath) throws Exception {
        ClassLoader classLoader = getClass().getClassLoader();
        String jsonStr = new String(Files.readAllBytes(Paths.get(classLoader.getResource(jsonFilePath).toURI())), StandardCharsets.UTF_8);
        JsonNode jsonNode = SerializeUtils.readTree(jsonStr);
        List<GenerateCodeTestCaseModel> result = Lists.newArrayList();

        if (!jsonNode.isArray()) {
            return result;
        }
        for (JsonNode expNode : jsonNode) {
            if (expNode == null || expNode.get("exp_result_id") == null) {
                continue;
            }
            JsonNode expResultIdNode = expNode.get("exp_result_id");
            List<String> expResultIds = null;
            if (expResultIdNode.isArray()) {
                expResultIds = SerializeUtils.toObj(expResultIdNode.toPrettyString(), new TypeReference<List<String>>(){});
            } else if (expResultIdNode.isTextual()) {
                expResultIds = Lists.newArrayList(expResultIdNode.asText());
            }
            if (CollectionUtils.isEmpty(expResultIds)) {
                continue;
            }
            JsonNode testCaseNodes = expNode.get("test_case");
            if (testCaseNodes == null || !testCaseNodes.isArray()) {
                continue;
            }
            for (JsonNode testCaseNode : testCaseNodes) {
                if (testCaseNode == null || testCaseNode.get("input") == null || !testCaseNode.get("input").isArray() || testCaseNode.get("output") == null) {
                    continue;
                }
                GenerateCodeTestCaseModel testCaseModel = new GenerateCodeTestCaseModel();
                testCaseModel.setExpResultIds(expResultIds);
                JsonNode inputNode = testCaseNode.get("input");
                List<AttrM> attrMs = Lists.newArrayList();
                for (JsonNode attrNode : inputNode) {
                    if (attrNode == null) {
                        continue;
                    }
                    AttrM attrM = new AttrM();
                    if (attrNode.get("attr_name") != null) {
                        attrM.setName(attrNode.get("attr_name").asText());
                    }
                    if (attrNode.get("attr_value") != null) {
                        JsonNode attrValueNode = attrNode.get("attr_value");
                        if (attrValueNode.isArray()) {
                            attrM.setValueList(SerializeUtils.toObj(attrValueNode.toPrettyString(), new TypeReference<List<String>>(){}));
                        } else if (attrValueNode.isTextual()) {
                            attrM.setValue(attrValueNode.asText());
                        }
                    }
                    attrMs.add(attrM);
                }
                testCaseModel.setInputAttrs(attrMs);
                JsonNode outputNode = testCaseNode.get("output");
                if (outputNode == null) {
                    continue;
                }
                testCaseModel.setOutputTags(Lists.newArrayList(outputNode.asText().split(",\\s*")));
                result.add(testCaseModel);
            }
        }
        return result;
    }

    /**
     * 生成测试用例
     * @return
     * @throws Exception
     */
    private String generateTestCase() throws Exception {
        List<Message> msgList = Lists.newArrayList(SystemMessage.from(agentPrompt.PROBE_GENERATE_TEST_CASE_PROMPT));
        StringBuilder summarySb = new StringBuilder();
        String path = getClass().getClassLoader().getResource("file/generate_test_case/test_summary1.txt").getPath();
        try(BufferedReader br = new BufferedReader(new FileReader(path))) {
            String line;
            while ((line = br.readLine()) != null) {
                summarySb.append(line);
            }
        }
        String summary = summarySb.toString();
        msgList.add(UserMessage.from(summary));
        AssistantMessage msg = gpt4oModel.sendMessages(msgList);
        return msg == null ? null : msg.getContent();
    }
}

package com.sankuai.qpro.ai.professor.testreport;

import com.fasterxml.jackson.core.type.TypeReference;
import com.meituan.mdp.langmodel.api.message.Message;
import com.sankuai.qpro.ai.professor.application.service.operator.probe.agent.ProbeGeneAgent;
import com.sankuai.qpro.ai.professor.application.utils.SerializeUtils;
import com.sankuai.qpro.ai.professor.application.utils.TextSimilarityUtils;
import com.sankuai.qpro.ai.professor.testreport.model.SummaryTestCase;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.List;

/**
 * @author: w<PERSON><PERSON><PERSON><PERSON>
 * @create: 2025-02-17
 * @description:
 */
@Slf4j
@SpringBootTest
public class ProbeGeneSummaryReportTest {

    @Autowired
    private ProbeGeneAgent probeGeneAgent;

    @Test
    public void testSummaryBase() {
        try {
            // 1.获取测试消息和参考文本
            String path = "file/summary_test_case/two_platform_multi_exp_override/test_case.json";
            String jsonStr = readDataFromFile(path);
            if (StringUtils.isBlank(jsonStr)) {
                return;
            }
            List<SummaryTestCase> summaryTestCases = SerializeUtils.toObj(jsonStr, new TypeReference<List<SummaryTestCase>>() {});
            if (CollectionUtils.isEmpty(summaryTestCases)) {
                return;
            }
            for (SummaryTestCase summaryTestCase : summaryTestCases) {
                // 2.调用模型生成小结
                Long conversationId = Long.valueOf(summaryTestCase.getConversationId());
                List<Message> historyMsg = probeGeneAgent.queryHistoryMsg(conversationId);
                String referenceSummary = summaryTestCase.getSummary();
                String summary = probeGeneAgent.generateSummary(null, historyMsg);
                // 3.比较小结和参考文本相似度
                double similarity = computeSimilarity(referenceSummary, summary);
                log.info("similarity = {}", similarity);
            }
        } catch (Exception e) {
            log.error("testSummaryBase error", e);
        }
    }


    private double computeSimilarity(String reference, String hyperSummary) {
        return TextSimilarityUtils.calculateRougeL(reference, hyperSummary);
    }

//    private double computeSimWithLLM(String reference, String hyperSummary) {
//
//    }

    private String readDataFromFile(String filePath) {
        try {
            ClassLoader classLoader = getClass().getClassLoader();
            String jsonStr = new String(Files.readAllBytes(Paths.get(classLoader.getResource(filePath).toURI())), StandardCharsets.UTF_8);
            return jsonStr;
        } catch (Exception e) {
            log.error("读取文件失败");
        }
        return null;
    }

}
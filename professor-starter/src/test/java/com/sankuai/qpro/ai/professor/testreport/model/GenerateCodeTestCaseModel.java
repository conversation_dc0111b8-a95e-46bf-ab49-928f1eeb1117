package com.sankuai.qpro.ai.professor.testreport.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.sankuai.dzviewscene.unifiedshelf.operator.api.utils.OperatorShelfConfigValidateUtil.AttrM;
import lombok.Data;

import java.util.List;

/**
 * @author: wu<PERSON><PERSON><PERSON>
 * @create: 2025-02-15
 * @description:
 */
@Data
public class GenerateCodeTestCaseModel {
    private List<String> expResultIds;
    private List<AttrM> inputAttrs;
    private List<String> outputTags;

}

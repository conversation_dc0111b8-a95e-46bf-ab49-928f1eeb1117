# 团购货架国补功能需求描述（初版）

## 业务范围

- **页面**：团购货架页面（T型货架）
- **类目**：团购商品（涉及国补商品）
- **平台**：美团
- **货架类型**：团购货架
- **活动标识**：activity_unified_shelf
- **展位标识**：unified_shelf
- **场景标识**：shop_unified_shelf

## 功能需求详述

### 1. 国补快筛tab功能

#### 1.1 样式设计
- 展示"国家补贴"tab
- 带有绿色icon（icon链接：https://p0.meituan.net/ingee/c15882b2de010d1b5c3f38aad25077653677.png）
- 显示"国家补贴"文案
- 视觉突出，与其他tab区分明显

#### 1.2 交互逻辑
- 点击"国家补贴"快筛tab后，仅展示有国补资质的商品
- 排序规则与线上一致（具体排序逻辑不在统一货架接口职责范围内）

#### 1.3 职责说明
- 统一货架接口仅负责国补快筛tab的样式和文案展示
- 优先级和顺序由其他模块控制，不在统一货架接口职责范围内

### 2. 商品卡片展示功能

#### 2.1 副标题逻辑
- 每个商品卡片副标题区域优先展示绿色"国家补贴"标签
- 若为国补商品，则根据商品类目展示CPV属性值：
  - 智能手机：显示屏幕尺寸、后置摄像头像素等
  - 运动相机：仅突出"国家补贴"标签
- 标明"购买后90天可用"等相关信息

#### 2.2 价格展示
- 商品价格分为现价、折扣、原价
- 现价：如¥2999
- 折扣：如8.6折
- 原价：如 ~¥3499~（删除线样式）

#### 2.3 国补优惠标签
- **位置**：展示于每个商品卡片的优惠标签区域
- **优先级**：优先级最高，明显高于其他优惠明细标签（如神券）
- **样式**：绿色标签，格式为"国补价约￥xxxx"
- **数据来源**：价格从报价系统获取
- **交互**：标签不可点击，仅用于突出国补后的到手价
- **示例**：绿色标签为"国补价约￥2549"



### 4. 页面整体布局

#### 4.1 设计风格
- 整体为白底设计，布局简洁
- Tab栏和筛选栏采用橙色及灰色区分
- 商品卡片一行一个，整齐排列

#### 4.2 页面信息展示
- **顶部信息**：门店信息、距离、商场名称、神券信息等
- **商品卡片**：产品照片、橙色"抢购"按钮、年销量信息
- **底部信息**：评价区块、写评价赢积分等

## 职责范围说明

### 统一货架接口负责的展示内容：
1. 国补快筛tab的展示样式和文案（不涉及优先级和顺序）
2. 国补优惠标签的展示样式和优先级
3. 商品卡片副标题的国补标签展示
4. 所有UI层面的样式、布局、标签展示

### 不在统一货架接口职责范围内：
1. 筛选导航的召回与排序逻辑
2. 货架商品模块的召回与排序逻辑
3. 从报价系统获取国补价格的逻辑
4. 国补资质商品的识别逻辑

## 技术要点

### 关键标识
- 国补相关元素均使用绿色主题色
- icon清晰醒目，视觉识别度高
- 标签优先级明确，层次分明

### 数据依赖
- 国补价格数据（从报价系统获取）
- 国补资质信息（用于判断是否展示国补标签）
- 商品CPV属性值（用于副标题展示）

### 用户体验
- 用户能够一目了然地识别国补商品
- 页面整体结构清晰，用户体验友好
- 便于快速筛选和识别国补商品
- 信息层次分明，关键信息突出
